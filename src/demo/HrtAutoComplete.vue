<template>
  <div>
    <hrt-autocomplete
      v-model="val"
      :fetch-suggestions="querySearch"
      popper-class="my-autocomplete"
      placeholder="Please input"
      @select="handleSelect"
    >
    </hrt-autocomplete>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { Search } from '@element-plus/icons-vue';

const val = ref();
const links = ref<any[]>([]);
const querySearch = (queryString: string, cb: any) => {
  const results = queryString
    ? links.value.filter(createFilter(queryString))
    : links.value;
  // call callback function to return suggestion objects
  setTimeout(() => {
    cb(results);
  }, 500);
};
const createFilter = (queryString: any) => {
  return (restaurant:any) => {
    return (
      restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1
    );
  };
};
const handleSelect = (item: any) => {
  console.log(item);
};

const handleIconClick = (ev: Event) => {
  console.log(ev);
};

const loadAll = () => {
  return [
    { value: 'vue', link: 'https://github.com/vuejs/vue' },
    { value: 'element', link: 'https://github.com/ElemeFE/element' },
    { value: 'cooking', link: 'https://github.com/ElemeFE/cooking' },
    { value: 'mint-ui', link: 'https://github.com/ElemeFE/mint-ui' },
    { value: 'vuex', link: 'https://github.com/vuejs/vuex' },
    { value: 'vue-router', link: 'https://github.com/vuejs/vue-router' },
    { value: 'babel', link: 'https://github.com/babel/babel' },
  ];
};
links.value = loadAll();
defineOptions({
  name: 'HrtAutoComplete',
});
</script>

<style scoped lang="less">
// todo
</style>
