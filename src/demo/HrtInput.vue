<template>
  <div>
    <div style="margin-top: 20px">
      <hrt-input v-model="val" disabled placeholder="size:mini" size="mini" />
    </div>
    <div style="margin-top: 20px">
      <hrt-input
        readonly
        v-model="val"
        placeholder="size:small"
        size="small"
        :suffix-icon="Search"
      />
    </div>
    <div style="margin-top: 20px">
      <hrt-input v-model="val" placeholder="size:middle" size="middle">
      </hrt-input>
    </div>
    <div style="margin-top: 20px">
      <hrt-input v-model="val" placeholder="size:large" size="large">
        <template #prepend>Http://</template>
      </hrt-input>
    </div>
    <div style="margin-top: 20px">
      <hrt-input v-model="val" placeholder="size:super" size="super" clearable>
        <template #prepend>Http://</template>
        <template #append>.com</template>
      </hrt-input>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { Search } from '@element-plus/icons-vue';

const val = ref();
defineOptions({
  name: 'HrtInputDemo',
});
</script>

<style scoped lang="less">
// todo
</style>
