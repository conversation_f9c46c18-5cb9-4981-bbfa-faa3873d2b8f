<template>
  <div>
    <el-breadcrumb separator="/">
      <el-breadcrumb-item :to="{ path: '/' }">aaaa</el-breadcrumb-item>
      <el-breadcrumb-item>
        <a href="/">bbbb</a>
      </el-breadcrumb-item>
      <el-breadcrumb-item>cccc</el-breadcrumb-item>
      <el-breadcrumb-item>dddd</el-breadcrumb-item>
    </el-breadcrumb>
  </div>
</template>

<script setup lang="ts">
import { ElBreadcrumb, ElBreadcrumbItem } from 'element-plus';

defineOptions({
  name: 'HrtBreadCrumb',
});
</script>

<style scoped lang="less">
// todo
</style>
