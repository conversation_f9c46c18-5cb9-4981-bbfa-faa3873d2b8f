<template>
  <div>
    <h1 class="h-50 text-center font-bold bg-[#ccc] leading-[50px]">
      组件预览
    </h1>
    <el-tabs tab-position="left" class="demo-tabs">
      <el-tab-pane label="hrt-button"><HrtButtonDemo /> </el-tab-pane>
      <el-tab-pane label="hrt-input"><HrtInputDemo /></el-tab-pane>
      <el-tab-pane label="hrt-autocomplete"
        ><HrtAutoCompleteDemo
      /></el-tab-pane>
      <el-tab-pane label="breadCrumb"><HrtBreadCrumbDemo /></el-tab-pane>
      <el-tab-pane label="hrt-collapse"><HrtCollapseDemo /></el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import HrtButtonDemo from '../demo/HrtButton.vue';
import HrtInputDemo from '../demo/HrtInput.vue';
import HrtAutoCompleteDemo from '../demo/HrtAutoComplete.vue';
import HrtBreadCrumbDemo from '../demo/HrtBreadCrumb.vue';
import HrtCollapseDemo from '../demo/HrtCollapseDemo.vue';
</script>

<style lang="less">
.demo-tabs {
  padding: 20px 0;
}
</style>
