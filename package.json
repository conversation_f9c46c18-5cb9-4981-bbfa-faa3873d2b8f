{"name": "@hrt/ui", "version": "0.0.1", "private": false, "packageManager": "pnpm@10.11.1+sha512.e519b9f7639869dc8d5c3c5dfef73b3f091094b0a006d7317353c72b124e80e1afd429732e28705ad6bfa1ee879c1fce46c128ccebd3192101f43dd67c667912", "main": "/dist/index.js", "module": "/dist/index.js", "files": ["dist"], "scripts": {"docs:dev": "vitepress dev docs --port 9000 --host", "docs:build": "vitepress build docs", "docs:preview": "vitepress preview docs"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@hrt/components": "workspace:^", "@hrt/icons": "workspace:^", "@hrt/tailwindcss": "workspace:^", "@vueuse/core": "^13.2.0", "element-plus": "^2.9.10", "lodash-es": "^4.17.21", "unocss": "^66.1.2", "vue": "^3.3.8", "vue-router": "^4.2.5"}, "devDependencies": {"@antfu/eslint-config": "^4.13.1", "@iconify-json/ep": "^1.1.14", "@iconify-json/ri": "^1.2.5", "@types/node": "^22.15.19", "@unocss/eslint-plugin": "^66.1.2", "@vitejs/plugin-vue": "^5.2.4", "autoprefixer": "^10.4.16", "eslint": "^9.27.0", "eslint-plugin-format": "^1.0.1", "husky": "^9.1.7", "less": "^4.2.0", "lint-staged": "^16.0.0", "markdown-it-container": "^4.0.0", "postcss": "^8.4.32", "postcss-nesting": "^13.0.1", "prettier": "^3.1.1", "rollup": "^4.8.0", "sass": "^1.77.6", "tailwindcss": "^3.3.6", "terser": "^5.17.6", "typescript": "^5.8.3", "unplugin-auto-import": "^19.2.0", "unplugin-icons": "^22.1.0", "unplugin-vue-components": "^28.5.0", "vite": "^6.3.5", "vite-plugin-dts": "^4.5.4", "vite-plugin-remove-console": "^2.2.0", "vitepress": "^1.6.3", "vue-tsc": "^2.2.10"}, "lint-staged": {"src/**/*.{json,ts,vue}": ["eslint"]}}