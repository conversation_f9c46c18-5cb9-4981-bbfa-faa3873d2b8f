{"name": "@hrt/ui", "version": "0.0.1", "private": false, "packageManager": "pnpm@10.12.1+sha512.f0dda8580f0ee9481c5c79a1d927b9164f2c478e90992ad268bbb2465a736984391d6333d2c327913578b2804af33474ca554ba29c04a8b13060a717675ae3ac", "scripts": {"dev": "turbo run dev", "docs:dev": "vitepress dev docs --port 9000 --host", "docs:build": "vitepress build docs", "docs:preview": "vitepress preview docs", "build:components": "pnpm --filter @hrt/components build", "build:icons": "pnpm --filter @hrt/icons build", "build:mobile": "pnpm --filter @hrt/mobile build", "build:mobile-site": "pnpm --filter @hrt/mobile build:site", "build:tools": "pnpm --filter @hrt/tools build", "publish:components": "pnpm --filter @hrt/components publish", "publish:icons": "pnpm --filter @hrt/icons publish", "publish:tailwindcss": "pnpm --filter @hrt/tailwindcss publish", "test": "vitest", "coverage": "vitest run --coverage"}, "dependencies": {"@element-plus/icons-vue": "^2.3.2", "@hrt/components": "workspace:^", "@hrt/icons": "workspace:^", "@hrt/tailwindcss": "workspace:^", "@vueuse/core": "^13.6.0", "cva": "1.0.0-beta.4", "element-plus": "^2.10.5", "gsap": "^3.13.0", "lodash-es": "^4.17.21", "qiniu-js": "3.4.3", "quill": "^2.0.3", "sortablejs": "^1.15.6", "vue": "^3.5.18", "vue-router": "^4.5.1"}, "devDependencies": {"@antfu/eslint-config": "5.2.0", "@iconify-json/ep": "^1.2.3", "@iconify-json/ri": "^1.2.5", "@types/node": "^24.2.1", "@types/sortablejs": "^1.15.8", "@vitejs/plugin-vue": "^6.0.1", "@vitest/coverage-v8": "^3.2.4", "autoprefixer": "^10.4.21", "eslint": "^9.33.0", "eslint-plugin-format": "^1.0.1", "eslint-plugin-oxlint": "^1.11.2", "husky": "^9.1.7", "less": "^4.4.0", "lint-staged": "^16.1.5", "markdown-it-container": "^4.0.0", "postcss": "^8.5.6", "postcss-nesting": "^13.0.2", "rollup": "^4.46.2", "sass": "^1.90.0", "tailwindcss": "^3.3.6", "terser": "^5.43.1", "turbo": "^2.5.6", "typescript": "^5.8.3", "unplugin-auto-import": "^20.0.0", "unplugin-icons": "^22.2.0", "unplugin-vue-components": "^29.0.0", "vite": "^7.1.1", "vite-plugin-dts": "^4.5.4", "vite-plugin-remove-console": "^2.2.0", "vite-tsconfig-paths": "^6.0.0-beta.3", "vitepress": "^1.6.4", "vitest": "^3.2.4", "vue-tsc": "^3.0.5"}, "lint-staged": {"src/**/*.{json,ts,vue}": ["eslint"]}, "workspaces": ["apps/*", "packages/*"]}