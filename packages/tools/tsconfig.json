{"compilerOptions": {"target": "esnext", "lib": ["es2023"], "moduleDetection": "force", "baseUrl": ".", "module": "preserve", "moduleResolution": "bundler", "paths": {"@/*": ["src/*"]}, "resolveJsonModule": true, "types": [], "strict": true, "noUnusedLocals": true, "declaration": true, "declarationMap": true, "outDir": "dist", "esModuleInterop": true, "isolatedDeclarations": true, "isolatedModules": true, "verbatimModuleSyntax": true, "skipLibCheck": true}, "include": ["src/**/*", "tests/**/*"]}