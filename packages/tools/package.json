{"name": "@hrt/tools", "type": "module", "version": "0.0.1", "exports": {".": "./src/index.ts", "./package.json": "./package.json"}, "main": "./src/index.ts", "module": "./src/index.ts", "files": ["src"], "scripts": {"build": "vitest run --coverage && tsdown", "test": "vitest", "coverage": "vitest run --coverage", "publish": "pnpm build && pnpm publish --no-git-checks"}, "dependencies": {"es-toolkit": "1.39.8"}, "publishConfig": {"linkDirectory": false, "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": "./dist/index.js", "./package.json": "./package.json"}, "files": ["dist"]}}