{"name": "@hrt/tools", "type": "module", "version": "0.0.1", "exports": {".": "./dist/index.js", "./package.json": "./package.json"}, "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["src"], "scripts": {"dev": "tsdown", "build": "vitest run --coverage && tsdown", "test": "vitest", "coverage": "vitest run --coverage", "publish": "pnpm build && pnpm publish --no-git-checks"}, "dependencies": {"es-toolkit": "1.39.10"}, "devDependencies": {"tsdown": "^0.14.1", "typescript": "^5.9.2", "vitest": "^3.2.4"}, "publishConfig": {"linkDirectory": false, "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": "./dist/index.js", "./package.json": "./package.json"}, "files": ["dist"]}}