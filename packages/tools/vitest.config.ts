import tsconfigPaths from 'vite-tsconfig-paths'
import { defineConfig } from 'vitest/config'

export default defineConfig({
  test: {
    globals: true,
    environment: 'node',
    include: ['tests/**/*.test.ts', 'src/**/*.test.ts'],
    coverage: {
      enabled: true,
      thresholds: {
        lines: 95,
        functions: 100,
        branches: 100,
        statements: 100,
        perFile: true,
      },
      include: ['src/**/*.ts'],
      exclude: ['src/**/*.test.ts'],
    },
  },
  plugins: [tsconfigPaths()],
})
