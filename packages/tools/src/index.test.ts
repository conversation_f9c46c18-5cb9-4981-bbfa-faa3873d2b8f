import { describe, expect, it } from 'vitest'
import * as tools from '@/index'

describe('工具库入口导出', () => {
  it('应导出 dateFormat', () => {
    expect(typeof tools.dateFormat).toBe('function')
  })
  it('应导出 getDayStart', () => {
    expect(typeof tools.getDateStart).toBe('function')
  })
  it('应导出 getDayEnd', () => {
    expect(typeof tools.getDateEnd).toBe('function')
  })
  it('应导出 isEmpty', () => {
    expect(typeof tools.isEmpty).toBe('function')
  })
  it('应导出 isPhone', () => {
    expect(typeof tools.isPhone).toBe('function')
  })
  it('应导出 isEmail', () => {
    expect(typeof tools.isEmail).toBe('function')
  })
  it('应导出 parseSeconds', () => {
    expect(typeof tools.parseSeconds).toBe('function')
  })
  it('应导出 isNumber', () => {
    expect(typeof tools.isNumber).toBe('function')
  })
  it('应导出 isFunction', () => {
    expect(typeof tools.isFunction).toBe('function')
  })
  it('应导出 moneyFormat', () => {
    expect(typeof tools.moneyFormat).toBe('function')
  })
  it('应导出 isIDCard', () => {
    expect(typeof tools.isIDCard).toBe('function')
  })
  it('应导出 generateKey', () => {
    expect(typeof tools.generateKey).toBe('function')
  })
  it('应导出 getYearEnd', () => {
    expect(typeof tools.getYearEnd).toBe('function')
  })
  it('应导出 getYearStart', () => {
    expect(typeof tools.getYearStart).toBe('function')
  })
  it('应导出 getMonthEnd', () => {
    expect(typeof tools.getMonthEnd).toBe('function')
  })
  it('应导出 getMonthStart', () => {
    expect(typeof tools.getMonthStart).toBe('function')
  })
})
