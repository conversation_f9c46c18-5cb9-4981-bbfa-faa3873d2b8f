import { describe, expect, it } from 'vitest'
import { getMonthStart } from './getMonthStart'

describe('getMonthStart', () => {
  it('返回当月1号0点', () => {
    const d = getMonthStart('2025-08-07T15:30:45')
    expect(d?.getFullYear()).toBe(2025)
    expect(d?.getMonth()).toBe(7) // 8月，0开始
    expect(d?.getDate()).toBe(1)
    expect(d?.getHours()).toBe(0)
    expect(d?.getMinutes()).toBe(0)
    expect(d?.getSeconds()).toBe(0)
    expect(d?.getMilliseconds()).toBe(0)
  })
  it('支持Date对象', () => {
    const d = getMonthStart(new Date('2023-01-15T12:00:00'))
    expect(d?.getFullYear()).toBe(2023)
    expect(d?.getMonth()).toBe(0)
    expect(d?.getDate()).toBe(1)
  })
  it('非法日期返回无效Date', () => {
    const d = getMonthStart('invalid-date')
    expect(d).toBeNull()
  })
})
