/**
 * 格式化日期，支持部分 dayjs 格式化规则
 * @param date 日期对象或可被 Date 解析的字符串/数字
 * @param format 格式化字符串，如 'YYYY-MM-DD HH:mm:ss'
 * @returns 格式化后的日期字符串
 */
export function dateFormat(date: Date | string | number | null | undefined, format: string = 'YYYY-MM-DD'): string {
  if (!date)
    return ''
  const d = date instanceof Date ? date : new Date(date)
  if (Number.isNaN(d.getTime()))
    return ''
  const pad = (n: number, len = 2) => String(n).padStart(len, '0')

  const map: Record<string, string> = {
    YYYY: String(d.getFullYear()),
    MM: pad(d.getMonth() + 1),
    DD: pad(d.getDate()),
    HH: pad(d.getHours()),
    mm: pad(d.getMinutes()),
    ss: pad(d.getSeconds()),
    SSS: pad(d.getMilliseconds(), 3),
  }
  const result = format.replace(/YYYY|MM|DD|HH|mm|ss|SSS/g, key => map[key])
  return result
}
