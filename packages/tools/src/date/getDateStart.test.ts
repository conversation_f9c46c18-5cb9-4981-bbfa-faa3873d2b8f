import { describe, expect, it } from 'vitest'
import { getDateStart } from '@/date'

describe('getDateStart', () => {
  it('返回当天开始时间', () => {
    const date = new Date('2025-07-24T15:30:45.123')
    const start = getDateStart(date)
    expect(start?.getFullYear()).toBe(2025)
    expect(start?.getMonth()).toBe(6) // 月份从0开始
    expect(start?.getDate()).toBe(24)
    expect(start?.getHours()).toBe(0)
    expect(start?.getMinutes()).toBe(0)
    expect(start?.getSeconds()).toBe(0)
    expect(start?.getMilliseconds()).toBe(0)
  })

  it('支持字符串日期', () => {
    const start = getDateStart('2025-07-24T23:59:59')
    expect(start?.getHours()).toBe(0)
    expect(start?.getMinutes()).toBe(0)
    expect(start?.getSeconds()).toBe(0)
    expect(start?.getMilliseconds()).toBe(0)
  })

  it('非法日期返回无效Date', () => {
    const start = getDateStart('invalid-date')
    expect(start).toBeNull()
  })
})
