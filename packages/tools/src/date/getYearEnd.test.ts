import { describe, expect, it } from 'vitest'
import { getYearEnd } from './getYearEnd'

describe('getYearEnd', () => {
  it('应该正确处理Date对象输入', () => {
    const date = new Date(2023, 5, 15) // 2023年6月15日
    const result = getYearEnd(date)
    expect(result).toEqual(new Date(2023, 11, 31, 23, 59, 59, 999))
  })

  it('应该正确处理字符串输入', () => {
    const result = getYearEnd('2023-02-15')
    expect(result).toEqual(new Date(2023, 11, 31, 23, 59, 59, 999))
  })

  it('应该正确处理时间戳输入', () => {
    const timestamp = new Date(2023, 3, 10).getTime() // 2023年4月10日
    const result = getYearEnd(timestamp)
    expect(result).toEqual(new Date(2023, 11, 31, 23, 59, 59, 999))
  })

  it('应该返回null当日期无效时', () => {
    expect(getYearEnd('invalid-date')).toBeNull()
    expect(getYearEnd(Number.NaN)).toBeNull()
  })
})