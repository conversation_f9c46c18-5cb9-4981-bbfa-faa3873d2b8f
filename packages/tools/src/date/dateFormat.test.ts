import { describe, expect, it } from 'vitest'
import { dateFormat } from '@/date'

describe('dateFormat', () => {
  it('格式化为 YYYY-MM-DD', () => {
    expect(dateFormat('2025-07-24', 'YYYY-MM-DD')).toBe('2025-07-24')
  })

  it('格式化为 YYYY/MM/DD', () => {
    expect(dateFormat('2025-07-24', 'YYYY/MM/DD')).toBe('2025/07/24')
  })

  it('格式化为 YYYY-MM-DD HH:mm:ss', () => {
    expect(dateFormat('2025-07-24T15:30:45', 'YYYY-MM-DD HH:mm:ss')).toBe('2025-07-24 15:30:45')
  })

  it('支持毫秒 SSS', () => {
    expect(dateFormat(new Date('2025-07-24T15:30:45.123'), 'SSS')).toBe('123')
  })

  it('非法日期返回空字符串', () => {
    expect(dateFormat('invalid-date')).toBe('')
  })

  it('支持时间戳', () => {
    expect(dateFormat(1753356771278, 'YYYY-MM-DD HH')).toBe('2025-07-24 19')
  })

  it('格式化为 YYYY年MM月DD日 HH时', () => {
    expect(dateFormat(1753356771278, 'YYYY年MM月DD日 HH时')).toBe('2025年07月24日 19时')
  })

  it('null 或 undefined 返回空字符串', () => {
    expect(dateFormat(null)).toBe('')
    expect(dateFormat(undefined)).toBe('')
  })
})
