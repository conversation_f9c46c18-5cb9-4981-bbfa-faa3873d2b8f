import { describe, expect, it } from 'vitest'
import { getMonthEnd } from './getMonthEnd'

describe('getMonthEnd', () => {
  it('应该正确处理Date对象输入', () => {
    const date = new Date(2023, 0, 15) // 2023年1月15日
    const result = getMonthEnd(date)
    expect(result).toEqual(new Date(2023, 0, 31, 23, 59, 59, 999))
  })

  it('应该正确处理字符串输入', () => {
    const result = getMonthEnd('2023-02-15')
    expect(result).toEqual(new Date(2023, 1, 28, 23, 59, 59, 999))
  })

  it('应该正确处理时间戳输入', () => {
    const timestamp = new Date(2023, 3, 10).getTime() // 2023年4月10日
    const result = getMonthEnd(timestamp)
    expect(result).toEqual(new Date(2023, 3, 30, 23, 59, 59, 999))
  })

  it('应该返回null当日期无效时', () => {
    expect(getMonthEnd('invalid-date')).toBeNull()
    expect(getMonthEnd(Number.NaN)).toBeNull()
  })
})