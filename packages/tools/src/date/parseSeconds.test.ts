import { describe, expect, it } from 'vitest'
import { parseSeconds } from '@/date'

describe('parseSeconds', () => {
  it('0秒', () => {
    expect(parseSeconds(0)).toEqual({ hour: 0, minute: 0, second: 0 })
  })
  it('59秒', () => {
    expect(parseSeconds(59)).toEqual({ hour: 0, minute: 0, second: 59 })
  })
  it('60秒', () => {
    expect(parseSeconds(60)).toEqual({ hour: 0, minute: 1, second: 0 })
  })
  it('3599秒', () => {
    expect(parseSeconds(3599)).toEqual({ hour: 0, minute: 59, second: 59 })
  })
  it('3600秒', () => {
    expect(parseSeconds(3600)).toEqual({ hour: 1, minute: 0, second: 0 })
  })
  it('3661秒', () => {
    expect(parseSeconds(3661)).toEqual({ hour: 1, minute: 1, second: 1 })
  })
  it('负数秒', () => {
    expect(parseSeconds(-1)).toEqual({ hour: -1, minute: -1, second: -1 })
  })
})
