import { describe, expect, it } from 'vitest'
import { getYearStart } from './getYearStart'

describe('getYearStart', () => {
  it('当传入 Date 对象时，应该返回该日期所在年份的开始时间', () => {
    const date = new Date('2023-05-15T12:34:56')
    const result = getYearStart(date)
    expect(result).toEqual(new Date('2023-01-01T00:00:00'))
  })

  it('当传入字符串时，应该返回该字符串所在年份的开始时间', () => {
    expect(getYearStart('2023-05-15')).toEqual(new Date('2023-01-01T00:00:00'))
  })

  it('当传入时间戳时，应该返回该时间戳所在年份的开始时间', () => {
    const timestamp = new Date('2023-05-15').getTime()
    expect(getYearStart(timestamp)).toEqual(new Date('2023-01-01T00:00:00'))
  })

  it('当传入无效日期时，应该返回 null', () => {
    expect(getYearStart('invalid-date')).toBeNull()
  })
})
