import { describe, expect, it } from 'vitest'
import { getDateEnd } from '@/date'

describe('getDateEnd', () => {
  it('返回当天结束时间', () => {
    const date = new Date('2025-07-24T15:30:45.123')
    const end = getDateEnd(date)
    expect(end?.getFullYear()).toBe(2025)
    expect(end?.getMonth()).toBe(6) // 月份从0开始
    expect(end?.getDate()).toBe(24)
    expect(end?.getHours()).toBe(23)
    expect(end?.getMinutes()).toBe(59)
    expect(end?.getSeconds()).toBe(59)
    expect(end?.getMilliseconds()).toBe(999)
  })

  it('支持字符串日期', () => {
    const end = getDateEnd('2025-07-24T00:00:00')
    expect(end?.getHours()).toBe(23)
    expect(end?.getMinutes()).toBe(59)
    expect(end?.getSeconds()).toBe(59)
    expect(end?.getMilliseconds()).toBe(999)
  })

  it('非法日期返回无效Date', () => {
    const end = getDateEnd('invalid-date')
    expect(end).toBeNull()
  })
})
