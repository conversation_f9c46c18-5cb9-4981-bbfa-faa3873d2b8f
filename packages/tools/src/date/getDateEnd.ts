/**
 * 获取一天的结束时间（23:59:59.999）
 * @param date 日期对象或可被 Date 解析的字符串/数字
 * @returns 当天结束的 Date 对象
 * @example
 * getDateEnd(new Date('2024-01-01')) // 2024-01-01 23:59:59.999
 * getDateEnd('2024-01-01') // 2024-01-01 23:59:59.999
 * getDateEnd(1672531200000) // 2024-01-01 23:59:59.999
 */
export function getDateEnd(date: Date | string | number): Date | null {
  const d = new Date(date)
  if (Number.isNaN(d.getTime()))
    return null
  d.setHours(23, 59, 59, 999)
  return d
}
