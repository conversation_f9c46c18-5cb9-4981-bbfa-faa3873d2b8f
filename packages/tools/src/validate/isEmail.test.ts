import { describe, expect, it } from 'vitest'
import { isEmail } from '@/validate'

describe('是否是合法邮件地址', () => {
  it('合法邮箱返回 true', () => {
    expect(isEmail('<EMAIL>')).toBe(true)
    expect(isEmail('<EMAIL>')).toBe(true)
    expect(isEmail('<EMAIL>')).toBe(true)
  })
  it('非法邮箱返回 false', () => {
    expect(isEmail('test@')).toBe(false)
    expect(isEmail('test@com')).toBe(false)
    expect(isEmail('test.com')).toBe(false)
    expect(isEmail('')).toBe(false)
  })
})
