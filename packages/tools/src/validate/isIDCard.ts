/**
 * 校验身份证号码
 * @param idCard 身份证号码
 * @see https://baike.baidu.com/item/%E8%BA%AB%E4%BB%BD%E8%AF%81%E6%A0%A1%E9%AA%8C%E7%A0%81/3800388
 * @returns 校验结果
 */
export function isIDCard(idCard: string): boolean {
  const length = idCard.length
  // 身份证长度只能为18位
  if (length !== 18) {
    return false
  }

  // 18位身份证号码的正则表达式
  // - 1. 验证前6位地区码（首位1-9）
  // - 2. 验证年份范围（18/19/20开头）
  // - 3. 验证月份（01-12）
  // - 4. 验证日期（01-31）
  // - 5. 最后4位（3位顺序码+1位校验码，校验码可以是数字或X）
  // - 6. 不区分大小写（支持X或x）
  const regIdCard = /^[1-9]\d{5}(?:18|19|20)\d{2}(?:0[1-9]|1[0-2])(?:0[1-9]|[12]\d|3[01])\d{3}[\dX]$/i

  // 如果通过该验证，说明身份证格式正确，但准确性还需计算
  if (regIdCard.test(idCard)) {
    const idCardWi = [
      7,
      9,
      10,
      5,
      8,
      4,
      2,
      1,
      6,
      3,
      7,
      9,
      10,
      5,
      8,
      4,
      2,
    ] // 将前17位加权因子保存在数组里
    const idCardY = [1, 0, 'X', 9, 8, 7, 6, 5, 4, 3, 2] // 这是除以11后，可能产生的11位余数、验证码，也保存成数组
    let idCardWiSum = 0 // 用来保存前17位各自乖以加权因子后的总和
    for (let i = 0; i < 17; i++) {
      idCardWiSum += Number(idCard[i]) * idCardWi[i]
    }
    const idCardMod = idCardWiSum % 11 // 计算出校验码所在数组的位置
    const idCardLast = idCard.charAt(17) // 得到最后一位身份证号码
    // 如果等于2，则说明校验码是10，身份证号码最后一位应该是X
    // 用计算出的验证码与最后一位身份证号码匹配，如果一致，说明通过，否则是无效的身份证号码
    return idCardLast.toLocaleUpperCase() === idCardY[idCardMod].toString()
  }
  else {
    return false
  }
}
