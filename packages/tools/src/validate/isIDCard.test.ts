import { describe, expect, it } from 'vitest'
import { isIDCard } from './isIDCard'

describe('isIDCard', () => {
  it('校验18位身份证号码', () => {
    expect(isIDCard('210124198508162281')).toBe(true)
    expect(isIDCard('210905197807210546')).toBe(true)
    expect(isIDCard('37078119790127719X')).toBe(true)
    expect(isIDCard('37142819800508053x')).toBe(true)
    expect(isIDCard('37010219680709292X')).toBe(true)
  })

  it('校验不通过的18位身份证号码', () => {
    expect(isIDCard('370781197901277191')).toBe(false)
    expect(isIDCard('371428198005080532')).toBe(false)
  })

  it('校验不符合长度的身份证号码', () => {
    expect(isIDCard('37078119790127719')).toBe(false)
    expect(isIDCard('3707811979012771912')).toBe(false)
  })

  it('校验不符合格式的身份证号码', () => {
    expect(isIDCard('⬇️3423515123')).toBe(false)
    expect(isIDCard('07078119790127719X')).toBe(false)
  })
})
