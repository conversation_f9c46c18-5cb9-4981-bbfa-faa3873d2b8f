import { describe, expect, it } from 'vitest'
import { isPhone } from '@/validate'

describe('是否是合法手机号', () => {
  it('合法手机号返回 true', () => {
    expect(isPhone('13812345678')).toBe(true)
    expect(isPhone('19987654321')).toBe(true)
  })
  it('非法手机号返回 false', () => {
    expect(isPhone('12345678901')).toBe(false)
    expect(isPhone('23812345678')).toBe(false)
    expect(isPhone('1381234567')).toBe(false)
    expect(isPhone('')).toBe(false)
  })
})
