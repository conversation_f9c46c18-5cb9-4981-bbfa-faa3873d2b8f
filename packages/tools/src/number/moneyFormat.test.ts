import { describe, expect, it } from 'vitest'
import { moneyFormat } from './moneyFormat'

describe('moneyFormat', () => {
  it('整数', () => {
    expect(moneyFormat(1234567)).toBe('1,234,567.00')
    expect(moneyFormat(0)).toBe('0.00')
    expect(moneyFormat(1000)).toBe('1,000.00')
  })
  it('小数', () => {
    expect(moneyFormat(1234.5)).toBe('1,234.50')
    expect(moneyFormat(1234.567)).toBe('1,234.57')
    expect(moneyFormat(1434334434.5674443)).toBe('1,434,334,434.57')
    expect(moneyFormat(0.1)).toBe('0.10')
  })
  it('负数', () => {
    expect(moneyFormat(-1234.5)).toBe('-1,234.50')
  })
  it('非数字或NaN', () => {
    // @ts-expect-error 测试非数字类型
    expect(moneyFormat('abc')).toBe('0.00')
    expect(moneyFormat(Number.NaN)).toBe('0.00')
    // @ts-expect-error 测试非数字类型
    expect(moneyFormat(null)).toBe('0.00')
    // @ts-expect-error 测试非数字类型
    expect(moneyFormat(undefined)).toBe('0.00')
    // @ts-expect-error 测试非数字类型
    expect(moneyFormat({})).toBe('0.00')
    // @ts-expect-error 测试非数字类型
    expect(moneyFormat([])).toBe('0.00')
  })
  it('removeFinalZero=true 时去除末尾0和小数点', () => {
    expect(moneyFormat(1234567, true)).toBe('1,234,567')
    expect(moneyFormat(1234.50, true)).toBe('1,234.5')
    expect(moneyFormat(1234.00, true)).toBe('1,234')
    expect(moneyFormat(0, true)).toBe('0')
    expect(moneyFormat(0.10, true)).toBe('0.1')
    expect(moneyFormat(-1234.00, true)).toBe('-1,234')
    expect(moneyFormat(-1234.50, true)).toBe('-1,234.5')
    expect(moneyFormat(-1234.56, true)).toBe('-1,234.56')
    expect(moneyFormat(1000.01, true)).toBe('1,000.01')
    // @ts-expect-error 测试非数字类型
    expect(moneyFormat(null, true)).toBe('0')
  })
})
