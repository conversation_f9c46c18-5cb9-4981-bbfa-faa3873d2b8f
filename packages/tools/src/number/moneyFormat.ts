/**
 * 将数字格式化为货币格式（带千分位和两位小数）
 * @param num 要格式化的数字
 * @returns 格式化后的字符串
 * @example
 * moneyFormat(1234567) // "1,234,567.00"
 * moneyFormat(0) // "0.00"
 * moneyFormat(1234.5) // "1,234.50"
 */
export function moneyFormat(num: number, removeFinalZero: boolean = false): string {
  if (typeof num !== 'number' || Number.isNaN(num))
    return removeFinalZero ? '0' : '0.00'
  const fixed = num.toFixed(2)
  const [intPart, decimalPart] = fixed.split('.')
  const formattedInt = intPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  if (removeFinalZero) {
    // 去除末尾无意义的0和小数点
    const decimal = decimalPart.replace(/0+$/, '')
    return decimal ? `${formattedInt}.${decimal}` : formattedInt
  }
  return `${formattedInt}.${decimalPart}`
}
