import { describe, expect, it } from 'vitest'
import { generateKey } from './generateKey'

describe('generateKey', () => {
  it('生成指定长度的字符串', () => {
    const len = 10
    const key = generateKey(len)
    expect(key).toHaveLength(len)
  })

  it('生成的字符串只包含有效字符', () => {
    const key = generateKey(100)
    expect(key).toMatch(/^[A-Z0-9]+$/i)
  })

  it('应该为每次调用生成不同的密钥', () => {
    const key1 = generateKey(12)
    const key2 = generateKey(12)
    expect(key1).not.toBe(key2)
  })

  it('应该返回一个空字符串，如果长度为 0', () => {
    expect(generateKey(0)).toBe('')
  })
})
