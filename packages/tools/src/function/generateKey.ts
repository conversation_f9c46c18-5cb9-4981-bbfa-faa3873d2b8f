/**
 * 生成随机字符串
 * @param num 生成的字符串长度
 * @returns 随机字符串
 */
export function generateKey(num: number = 16): string {
  const library =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";

  let key = "";

  for (let i: number = 0; i < num; i++) {
    const randomPoz = Math.floor(Math.random() * library.length);

    key += library.substring(randomPoz, randomPoz + 1);
  }

  return key;
}
