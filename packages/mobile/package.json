{"name": "@hrt/mobile", "version": "1.0.0", "description": "", "author": "", "main": "lib/mobile.js", "module": "es/index.js", "style": "lib/index.css", "typings": "es/index.d.ts", "files": ["es", "lib"], "scripts": {"dev": "vant-cli dev", "lint": "vant-cli lint", "build": "vant-cli build", "build:site": "vant-cli build-site", "release:site": "pnpm build:site && npx gh-pages -d site-dist"}, "nano-staged": {"*.md": "prettier --write", "*.{ts,tsx,js,vue,less,scss}": "prettier --write", "*.{ts,tsx,js,vue}": "eslint --fix"}, "peerDependencies": {"vant": "^4.9.21", "vue": "^3.5.0"}, "dependencies": {"@hrt/tools": "workspace:^", "@vue-office/docx": "1.6.3", "@vue-office/excel": "1.7.14", "@vue-office/pdf": "2.0.10"}, "devDependencies": {"@rsbuild/core": "^1.4.15", "@vant/cli": "^7.1.0"}, "eslintConfig": {"root": true, "extends": ["@vant"]}, "prettier": {"singleQuote": true}, "browserslist": ["Chrome >= 87", "iOS >= 13"]}