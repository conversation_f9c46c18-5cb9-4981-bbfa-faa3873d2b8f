/** @type {import('tailwindcss').Config} */
export default {
  content: [
    './src/**/*.{vue,js,ts,jsx,tsx}',
    './style/**/*.{vue,js,ts,jsx,tsx,css,less}',
  ],
  plugins: [],
  theme: {
    spacing: {
      ...Object.fromEntries(Array.from({ length: 1000 }, (_, index) => [index, `${index}px`])),
      '3xs': '4px',
      '2xs': '8px',
      'xs': '12px',
      'sm': '16px',
      'md': '20px',
      'lg': '24px',
      'xl': '28px',
      '2xl': '32px',
      '3xl': '36px',
      '4xl': '40px',
      '5xl': '50px',
    },
    fontSize: {
      'xs': ['12px', '16px'],
      'sm': ['14px', '21px'],
      'base': ['16px', '24px'],
      'lg': ['18px', '27px'],
      'xl': ['21px', '30px'],
      '2xl': ['24px', '32px'],
      '3xl': ['28px', '36px'],
      '4xl': ['32px', '40px'],
    },
    extend: {
      colors: {
        n: {
          100: '#f4f7fb',
          200: '#f7f7f7',
          300: '#e9e8eb',
          400: '#d8d8d8',
          500: '#979797',
          600: '#aeaeae',
          700: '#999999',
          800: '#333333',
          900: '#111111',
        },
        gb: {
          100: '#eef1ff',
          200: '#e5ecfd',
          300: '#9baffc',
          400: '#6885f8',
          600: '#2953f5',
          800: '#132f9b',
        },
        g: {
          100: '#f7fff0',
          600: '#62d12a',
        },
        o: {
          100: '#fff2e8',
          600: '#ff7d1a',
        },
        r: {
          100: '#fff3f2',
          600: '#fd513e',
        },
        y: {
          600: '#ffbd38',
        },
      },

    },
  },
}
