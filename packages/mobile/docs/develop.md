# 贡献指南

### 项目结构

项目结构如下，每个组件位于 `packages/mobile/src` 文件夹下，每个组件主要包含3个文件：

1. `index.vue`：组件的代码
2. `README.md`：组件的文档
3. `demo` 文件夹：组件的演示代码

```plain
├── packages
│   ├── mobile
│   │   ├── src
│   │   │   ├── button
│   │   │   │   ├── index.vue
│   │   │   │   ├── README.md
│   │   │   │   ├── demo
│   │   │   │   │   ├── index.vue
│   │   │   ├── field
│   │   │   │   ├── index.vue
│   │   │   │   ├── README.md
│   │   │   │   ├── demo
│   │   │   │   │   ├── index.vue
│   │   ├── docs
│   │   ├── package.json
```

### 开发

#### 运行项目

```bash
pnpm --filter @hrt/mobile dev
```

#### 开发组件

在 `packages/mobile/src` 目录下开发组件，每个组件对应一个目录，目录名就是组件名。

1. `index.vue`：组件的代码
2. `README.md`：组件的文档
3. `demo` 文件夹：组件的演示代码

#### 全局样式

在 `packages/mobile/src` 目录下有一个 `global.css` 文件，里面是全局的样式文件，在里面定义全局的CSS变量，覆盖Vant的默认样式。

打包时会单独打包这个文件，在引入组件库时需要单独引入这个文件。
