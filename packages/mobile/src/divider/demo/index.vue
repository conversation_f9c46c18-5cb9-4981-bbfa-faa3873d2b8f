<script setup>
import HrtDivider from '../index.vue'
import 'vant/lib/index.css'
import '../../global.css'
</script>

<template>
  <demo-block title="基础用法">
    <HrtDivider />
  </demo-block>

  <demo-block title="展示文本">
    <div>
      <HrtDivider>
        文本
      </HrtDivider>
    </div>
  </demo-block>

  <demo-block title="内容位置">
    <HrtDivider content-position="left">
      文本
    </HrtDivider>
    <HrtDivider content-position="right">
      文本
    </HrtDivider>
  </demo-block>
  <demo-block title="虚线">
    <HrtDivider content-position="left" dashed>
      文本
    </HrtDivider>
    <HrtDivider dashed>
      文本
    </HrtDivider>
    <HrtDivider content-position="right" dashed>
      文本
    </HrtDivider>
  </demo-block>
  <demo-block title="垂直">
    <div class="grid">
      文本
      <HrtDivider vertical dashed />
      文本
      <HrtDivider vertical :hairline="false" />
      文本
      <HrtDivider vertical style="border-color: rebeccapurple;" />
      文本
    </div>
  </demo-block>
</template>

<style lang="css" scoped>
.grid {
  padding: 0 16px;
}
</style>
