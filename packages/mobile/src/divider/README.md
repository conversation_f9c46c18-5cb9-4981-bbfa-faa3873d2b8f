# Divider 分割线

### 介绍

用于将内容分隔为多个区域。

### 引入

```js
import { Divider } from '@hrt/mobile'
import Vue from 'vue'

Vue.use(Divider)
```

## 代码演示

### 基础用法

```html
<hrt-divider />
```

### 展示文本

通过插槽在可以分割线中间插入内容。

```html
<hrt-divider>文本</hrt-divider>
```

### 内容位置

通过 `content-position` 指定内容所在位置。

```html
<hrt-divider content-position="left">文本</hrt-divider>
<hrt-divider content-position="right">文本</hrt-divider>
```

### 虚线

添加 `dashed` 属性使分割线渲染为虚线。

```html
<hrt-divider dashed>文本</hrt-divider>
```

### 垂直

```html
<hrt-divider vertical />
文本
<hrt-divider vertical dashed />
文本
<hrt-divider vertical :hairline="false" />
文本
<hrt-divider vertical :style="{ borderColor: '#1989fa' }" />
```

## API

### Props

| 参数             | 说明                          | 类型                      | 默认值   |
| ---------------- | ----------------------------- | ------------------------- | -------- |
| dashed           | 是否使用虚线                  | _boolean_                 | `false`  |
| hairline         | 是否使用 0.5px 线             | _boolean_                 | `true`   |
| content-position | 内容位置，可选值为 left right | _left \| center \| right_ | `center` |
| vertical         | 是否使用垂直                  | _boolean_                 | `false`  |

### Slots

| 名称    | 说明     |
| ------- | -------- |
| default | 默认插槽 |
