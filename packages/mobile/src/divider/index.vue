<script lang="ts" setup>
import type { DividerProps } from './types'
import { Divider } from 'vant'
import { dividerDefaultProps } from './types'

import '../global.css'

defineOptions({
  name: 'HrtDivider',
})

withDefaults(defineProps<DividerProps>(), dividerDefaultProps)
</script>

<template>
  <Divider
    v-bind="$attrs"
    :dashed="dashed"
    :vertical="vertical"
    :content-position="contentPosition"
    :hairline="hairline"
  >
    <slot />
  </Divider>
</template>

<style lang="less">

</style>
