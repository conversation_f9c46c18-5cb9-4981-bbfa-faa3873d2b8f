<script setup>
import { ref } from 'vue'
import HrtButton from '../../button/index.vue'
import HrtField from '../index.vue'
import 'vant/lib/index.css'
import '../../global.css'

const value = ref('')
const value1 = ref('')
const value2 = ref('')
const password = ref('')
const showPassword = ref(false)
const sms = ref()
const price = ref()
</script>

<template>
  <demo-block title="基础用法">
    <HrtField v-model="value" label="文本" placeholder="请输入文本" />
    <HrtField v-model="value" label="标题最多五字" placeholder="请输入文本" />
  </demo-block>
  <demo-block title="无标题输入框">
    <HrtField v-model="value1" placeholder="请输入文本" />
  </demo-block>
  <demo-block title="必填输入框">
    <HrtField v-model="value2" label="用户名" required placeholder="请输入文本" />
  </demo-block>
  <demo-block title="不可编辑">
    <HrtField v-model="value2" :disabled="true" label="用户名" required placeholder="请输入文本" />
  </demo-block>
  <demo-block title="带辅助说明输入框">
    <HrtField
      v-model="value2"
      label="用户名"
      required
      placeholder="请输入文本"
      error-message="错误提示"
    />
    <HrtField
      v-model="value2"
      label="用户名"
      required
      placeholder="请输入文本"
      info-message="提示说明"
    />
    <HrtField
      v-model="value2"
      label="用户名"
      required
      placeholder="请输入文本"
      warning-message="警告提示"
    />
    <HrtField
      v-model="value2"
      label="用户名"
      required
      placeholder="请输入文本"
      success-message="成功提示"
    />
  </demo-block>
  <demo-block title="带图标输入框">
    <HrtField v-model="value2" label="用户名" placeholder="请输入文本" left-icon="smile-o" />
    <HrtField
      v-model="password"
      label="密码"
      placeholder="请输入密码"
      :clearable="false"
      :type="showPassword ? 'text' : 'password'"
      left-icon="smile-o"
      :right-icon="showPassword ? 'closed-eye' : 'eye'"
      @click-right-icon="() => {
        console.log('click right icon')
        showPassword = !showPassword
      }"
    />
  </demo-block>
  <demo-block title="带按钮输入框">
    <HrtField
      v-model="sms"
      center
      clearable
      label="短信验证码"
      placeholder="请输入短信验证码"
    >
      <template #button>
        <HrtButton size="small" type="text">
          发送验证码
        </HrtButton>
      </template>
    </HrtField>
    <HrtField
      v-model="price"
      type="number"
      center
      clearable
      label="价格"
      placeholder="0.00"
      input-align="right"
    >
      <template #button>
        CNY
      </template>
    </HrtField>
  </demo-block>
  <demo-block title="垂直布局">
    <HrtField
      v-model="price"
      type="number"
      center
      required
      clearable
      label="价格"
      placeholder="0.00"
      label-align="top"
    >
      <template #button>
        CNY
      </template>
    </HrtField>
    <HrtField
      v-model="price"
      type="number"
      center
      required
      label="价格"
      placeholder="0.00"
      label-align="top"
      right-icon="arrow"
    />
  </demo-block>
</template>

<style lang="css" scoped>
.grid {
  padding: 0 16px;
}
</style>
