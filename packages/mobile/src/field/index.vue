<script lang="ts" setup>
import type { FieldProps } from './types'
import { cva } from 'cva'
import { Field } from 'vant'
import { computed } from 'vue'
import { fieldDefaultProps } from './types'

import '../global.css'

defineOptions({
  name: 'HrtField',
})

const props = withDefaults(defineProps<FieldProps>(), fieldDefaultProps)
const emit = defineEmits<{
  (e: 'clickRightIcon', event: MouseEvent): void
  (e: 'clickLeftIcon', event: MouseEvent): void
  (e: 'click', event: MouseEvent): void
  (e: 'clickInput', value: MouseEvent): void
  (e: 'clear', value: MouseEvent): void
  (e: 'focus', value: Event): void
  (e: 'blur', value: Event): void
  (e: 'startValidate'): void
  (e: 'endValidate', value: { status: string, message: string }): void
}>()

const field = cva({
  base: 'hrt-field',
  variants: {
    infoMessage: {
      true: 'hrt-field--info',
    },
    successMessage: {
      true: 'hrt-field--success',
    },
    warningMessage: {
      true: 'hrt-field--warning',
    },
  },
})

const computedErrorMessage = computed(() => {
  if (props.errorMessage) {
    return props.errorMessage
  }
  if (props.infoMessage) {
    return props.infoMessage
  }
  if (props.successMessage) {
    return props.successMessage
  }
  if (props.warningMessage) {
    return props.warningMessage
  }
  return ''
})
</script>

<template>
  <Field
    v-bind="$attrs"
    :type="props.type"
    :label="props.label"
    :clearable="props.clearable"
    :readonly="props.readonly"
    :disabled="props.disabled"
    :label-width="props.labelWidth"
    :error-message="computedErrorMessage"
    :size="props.size"
    :border="props.border"
    :required="props.required"
    :colon="props.colon"
    :center="props.center"
    :clear-trigger="props.clearTrigger"
    :clickable="props.clickable"
    :is-link="props.isLink"
    :autofocus="props.autofocus"
    :show-word-limit="props.showWordLimit"
    :error="props.error"
    :error-message-align="props.errorMessageAlign"
    :format-trigger="props.formatTrigger"
    :arrow-direction="props.arrowDirection"
    :label-align="props.labelAlign"
    :input-align="props.inputAlign"
    :icon-prefix="props.iconPrefix"
    :class="field({
      ...props,
      infoMessage: !!props.infoMessage,
      successMessage: !!props.successMessage,
      warningMessage: !!props.warningMessage,
    })"
    @click-right-icon="emit('clickRightIcon', $event)"
    @click-left-icon="emit('clickLeftIcon', $event)"
    @click="emit('click', $event)"
    @click-input="emit('clickInput', $event)"
    @clear="emit('clear', $event)"
    @focus="emit('focus', $event)"
    @blur="emit('blur', $event)"
    @start-validate="emit('startValidate')"
    @end-validate="emit('endValidate', $event)"
  >
    <template v-if="$slots.button" #button>
      <slot name="button" />
    </template>
    <template v-if="$slots.rightIcon" #right-icon>
      <slot name="right-icon" />
    </template>
    <template v-if="$slots.leftIcon" #left-icon>
      <slot name="left-icon" />
    </template>
    <template v-if="$slots.errorMessage" #error-message>
      <slot name="error-message" />
    </template>
    <template v-if="$slots.input" #input>
      <slot name="input" />
    </template>
    <template v-if="$slots.label" #label>
      <slot name="label" />
    </template>
    <template v-if="$slots.extra" #extra>
      <slot name="extra" />
    </template>
  </Field>
</template>

<style lang="less">
.van-field__control {
  caret-color: var(--color-primary);
}

.van-field--disabled.van-field--disabled .van-field__label {
  color: var(--color-n8);
}
.hrt-field--info {
  .van-field__error-message {
    color: var(--color-n7);
  }
}

.hrt-field--success {
  .van-field__error-message {
    color: var(--color-success);
  }
}

.hrt-field--warning {
  .van-field__error-message {
    color: var(--color-warning);
  }
}
</style>
