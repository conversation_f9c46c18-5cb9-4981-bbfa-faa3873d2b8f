# Field 输入框

### 介绍

用户可以在文本框内输入或编辑文字。

### 引入

```js
import { Field } from '@hrt/mobile'
import Vue from 'vue'

Vue.use(Field)
```

## 代码演示

### 基础用法

可以通过 `v-model` 双向绑定输入框的值，通过 `placeholder` 设置占位提示文字。

```ts
import { ref } from 'vue'

const value = ref('')
```

```html
<hrt-field v-model="value" label="文本" placeholder="请输入" />
```

### 必填星号

通过 `required` 属性添加必填星号。

```html
<hrt-field v-model="value" label="文本" placeholder="请输入" required />
```

### 不可编辑

通过 `disabled` 属性禁用输入框。

```html
<hrt-field v-model="value" label="文本" placeholder="请输入" disabled />
```

### 带辅助说明输入框

通过 `error-message`、`warning-message`、`success-message`、`info-message` 属性添加辅助说明。

```html
<hrt-field v-model="value" label="文本" placeholder="请输入" info-message="错误提示" />
<hrt-field v-model="value" label="文本" placeholder="请输入" info-message="提示信息" />
<hrt-field v-model="value" label="文本" placeholder="请输入" warning-message="警告提示" />
<hrt-field v-model="value" label="文本" placeholder="请输入" success-message="成功提示" />
```

## API

### Props

参考 [Field 组件](https://vant-ui.github.io/vant/#/zh-CN/field#props)

| 参数            | 说明                           | 类型     | 默认值 |
| --------------- | ------------------------------ | -------- | ------ |
| info-message    | 底部提示文案，为空时不展示     | _string_ | -      |
| warning-message | 底部警告提示文案，为空时不展示 | _string_ | -      |
| success-message | 底部成功提示文案，为空时不展示 | _string_ | -      |

### Slots

| 名称          | 说明                                                       | 参数                  |
| ------------- | ---------------------------------------------------------- | --------------------- |
| label         | 自定义输入框左侧文本                                       | -                     |
| input         | 自定义输入框，使用此插槽后，与输入框相关的属性和事件将失效 | -                     |
| left-icon     | 自定义输入框头部图标                                       | -                     |
| right-icon    | 自定义输入框尾部图标                                       | -                     |
| button        | 自定义输入框尾部按钮                                       | -                     |
| error-message | 自定义底部错误提示文案                                     | `{ message: string }` |
| extra         | 自定义输入框最右侧的额外内容                               | -                     |
