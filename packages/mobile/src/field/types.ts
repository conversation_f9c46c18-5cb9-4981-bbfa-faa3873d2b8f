import type { FieldProps as VantFieldProps } from 'vant'

export interface FieldProps {
  label?: VantFieldProps['label']
  errorMessage?: VantFieldProps['errorMessage']
  clearable?: VantFieldProps['clearable']
  readonly?: VantFieldProps['readonly']
  disabled?: VantFieldProps['disabled']
  labelWidth?: VantFieldProps['labelWidth']
  type?: VantFieldProps['type']
  size?: VantFieldProps['size']
  border?: VantFieldProps['border']
  required?: VantFieldProps['required']
  colon?: VantFieldProps['colon']
  center?: VantFieldProps['center']
  clearTrigger?: VantFieldProps['clearTrigger']
  clickable?: VantFieldProps['clickable']
  isLink?: VantFieldProps['isLink']
  autofocus?: VantFieldProps['autofocus']
  showWordLimit?: VantFieldProps['showWordLimit']
  error?: VantFieldProps['error']
  errorMessageAlign?: VantFieldProps['errorMessageAlign']
  formatTrigger?: VantFieldProps['formatTrigger']
  arrowDirection?: VantFieldProps['arrowDirection']
  labelAlign?: VantFieldProps['labelAlign']
  inputAlign?: VantFieldProps['inputAlign']
  iconPrefix?: VantFieldProps['iconPrefix']
  infoMessage?: string
  successMessage?: string
  warningMessage?: string
}

export const fieldDefaultProps: FieldProps = {
  clearable: false,
  readonly: false,
  disabled: false,
  labelWidth: 96,
  errorMessage: '',
  type: 'text',
  size: 'normal',
  border: true,
  required: false,
  colon: false,
  center: false,
  clearTrigger: 'focus',
  clickable: false,
  isLink: false,
  autofocus: false,
  showWordLimit: false,
  error: false,
  errorMessageAlign: 'left',
  formatTrigger: 'onChange',
  arrowDirection: 'right',
  labelAlign: 'left',
  inputAlign: 'left',
  iconPrefix: 'van-icon',
}
