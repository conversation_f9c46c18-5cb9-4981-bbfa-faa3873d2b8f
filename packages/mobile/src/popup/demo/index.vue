<script setup>
import { Cell } from 'vant'
import { ref } from 'vue'
import Button from '../../button/index.vue'
import HrtPopup from '../index.vue'
import 'vant/lib/index.css'
import '../../global.css'

const show = ref(false)
const show2 = ref(false)
const show3 = ref(false)

function openPopup() {
  show.value = true
}
</script>

<template>
  <demo-block title="基础用法">
    <Cell title="打开弹窗" @click="openPopup" />
    <HrtPopup
      v-model:show="show"
      title="弹窗标题"
      style="height: 50%;"
    >
      <div class="p-16">
        <div class="flex-1 h-[500px] bg-red-300">
          弹窗内容
        </div>
      </div>
    </HrtPopup>
  </demo-block>
  <demo-block title="带底部按钮用法">
    <Cell title="带按钮弹窗" @click="show2 = true" />
    <HrtPopup
      v-model:show="show2"
      title="弹窗标题"
      style="height: 50%;"
    >
      <div class="p-16">
        <div class="flex-1 h-[500px] bg-red-300">
          弹窗内容
        </div>
      </div>
      <template #footer>
        <div>
          <Button size="large" type="primary" block @click="show2 = false">
            确定
          </Button>
        </div>
      </template>
    </HrtPopup>
  </demo-block>
  <demo-block title="右侧弹窗">
    <Cell title="右侧显示" @click="show3 = true" />
    <HrtPopup
      v-model:show="show3"
      style="height: 100%; width: 60%;"
      position="right"
      :round="false"
    >
      <div class="p-16">
        <div class="flex-1 h-[500px] bg-red-300">
          弹窗内容
        </div>
      </div>
    </HrtPopup>
  </demo-block>
</template>
