# Popup 弹窗

### 介绍

弹出层容器，用于展示弹窗、信息提示等内容，支持多个弹出层叠加展示。

### 引入

通过以下方式来全局注册组件，更多注册方式请参考组件注册。

```ts
import { createApp } from 'vue';
import { Popup } from '@hrt/mobile';

const app = createApp();
app.use(Popup);
```

### 基础使用

通过 `v-model:show` 绑定一个变量来控制弹窗的显示和隐藏，`title` 为弹窗标题。

```html
<HrtPopup v-model:show="show" title="弹窗标题">
  <div class="p-16">
    <div class="flex-1 h-[500px] bg-red-300">
      弹窗内容
    </div>
  </div>
</HrtPopup>
```

### 带按钮弹窗

通过 `footer` 插槽来添加底部按钮，`footer` 插槽会自动添加一个 `padding-bottom: 16px` 的样式。

```html
<HrtPopup v-model:show="show" title="弹窗标题">
  <div class="p-16">
    <div class="flex-1 h-[500px] bg-red-300">
      弹窗内容
    </div>
  </div>
  <template #footer>
    <div>
      <HrtButton size="large" type="primary" block @click="show = false">
        确定
      </HrtButton>
    </div>
  </template>
</HrtPopup>
```

### 弹窗位置

通过 `position` 属性来设置弹窗的位置，可选值为 `top`、`bottom`、`left`、`right`，默认为 `bottom`。

```html
<HrtPopup v-model:show="show" position="right">
  <div class="p-16">
    <div class="flex-1 h-[500px] bg-red-300">
      弹窗内容
    </div>
  </div>
</HrtPopup>
```
