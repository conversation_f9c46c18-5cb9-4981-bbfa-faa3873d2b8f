<script setup lang="ts">
import type { PopupProps } from './types'
import { Icon, Popup } from 'vant'
import { popupDefaultProps } from './types'
import '../global.css'

const props = withDefaults(defineProps<PopupProps>(), popupDefaultProps)
const emit = defineEmits<{
  (e: 'click', value: MouseEvent): void
  (e: 'clickOverlay', value: MouseEvent): void
  (e: 'clickCloseIcon', value: MouseEvent): void
  (e: 'open'): void
  (e: 'opened'): void
  (e: 'close'): void
  (e: 'closed'): void
}>()
const show = defineModel('show', {
  type: Boolean,
  default: false,
  required: false,
})
</script>

<template>
  <Popup
    v-model:show="show"
    v-bind="$attrs"
    :overlay-class="props.overlayClass"
    :overlay-style="props.overlayStyle"
    :overlay-props="props.overlayProps"
    :duration="props.duration"
    :z-index="props.zIndex"
    :round="props.round"
    :position="props.position"
    :overlay="props.overlay"
    :closeable="props.title ? false : props.closeable"
    :close-icon="props.closeIcon"
    :close-icon-position="props.closeIconPosition"
    :before-close="props.beforeClose"
    :icon-prefix="props.iconPrefix"
    :transition="props.transition"
    :transition-appear="props.transitionAppear"
    :teleport="props.teleport"
    :safe-area-inset-top="props.safeAreaInsetTop"
    :safe-area-inset-bottom="props.safeAreaInsetBottom"
    :destroy-on-close="props.destroyOnClose"
    :lock-scroll="props.lockScroll"
    :lazy-render="props.lazyRender"
    :close-on-popstate="props.closeOnPopstate"
    :close-on-click-overlay="props.closeOnClickOverlay"
    @click="emit('click', $event)"
    @click-overlay="emit('clickOverlay', $event)"
    @click-close-icon="emit('clickCloseIcon', $event)"
    @open="emit('open')"
    @opened="emit('opened')"
    @close="emit('close')"
    @closed="emit('closed')"
  >
    <div class="flex flex-col h-full">
      <slot v-if="props.title" name="header">
        <div class="hrt-popup--header">
          <span class="hrt-popup--header--title">{{ props.title }}</span>
          <div
            class="absolute flex items-center justify-center h-48 w-48 right-0 top-0 cursor-pointer"
            @click="show = false"
          >
            <Icon name="cross" color="#999999" size="18" />
          </div>
        </div>
      </slot>
      <div class="flex-1 overflow-auto">
        <slot />
      </div>
      <div v-if="$slots.footer" class="hrt-popup--footer">
        <slot name="footer" />
      </div>
    </div>
    <template v-if="$slots['overlay-content']" #overlay-content>
      <slot name="overlay-content" />
    </template>
  </Popup>
</template>

<style lang="css">
.hrt-popup--header {
  border-bottom: 1px solid #eee;
  min-height: 48px;
  padding: 0 48px;
  display: flex;
  align-items: center;
  position: relative;
  justify-content: center;
}
.hrt-popup--header--title {
  font-size: 16px;
  font-weight: 500;
  color: var(--color-n8);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.hrt-popup--footer {
  background: #ffffff;
  box-shadow: 0px -1px 4px 0px rgba(0, 0, 0, 0.1);
  padding: 15px 16px;
}
</style>
