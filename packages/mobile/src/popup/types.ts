import type { CSSProperties } from 'vue'

export interface PopupProps {
  overlay?: boolean
  /** 弹出位置 */
  position?: 'top' | 'bottom' | 'left' | 'right'
  /** 自定义遮罩层类名 */
  overlayClass?: string | Array<string> | object
  /** 自定义遮罩层样式 */
  overlayStyle?: CSSProperties
  /** 遮罩层属性，参考 Overlay 组件 */
  overlayProps?: object
  /** 动画时长，单位秒，设置为 0 可以禁用动画 */
  duration?: number | string
  /** 弹窗 z-index 层级设置为一个固定值 */
  zIndex?: number | string
  /** 是否显示圆角 */
  round?: boolean
  /** 是否在关闭时销毁内容 */
  destroyOnClose?: boolean
  /** 是否锁定背景滚动 */
  lockScroll?: boolean
  /** 是否在显示弹层时才渲染节点 */
  lazyRender?: boolean
  /** 是否在页面回滚时自动关闭 */
  closeOnPopstate?: boolean
  /** 是否点击遮罩层关闭 */
  closeOnClickOverlay?: boolean
  /** 是否显示关闭图标 */
  closeable?: boolean
  /** 弹窗标题 */
  title?: string
  /** 关闭图标名称，等同于 Icon 组件的 name 属性 */
  closeIcon?: string
  /** 关闭图标位置，可选值为 top-left、bottom-left、bottom-right */
  closeIconPosition?: 'top-left' | 'bottom-left' | 'bottom-right'
  /** 关闭前的回调函数，返回 false 可阻止关闭，支持返回 Promise */
  beforeClose?: (action: string) => boolean | Promise<boolean>
  /** 图标类名前缀，等同于 Icon 组件的 class-prefix 属性 */
  iconPrefix?: string
  /** 动画类名，等同于 transition 的 name 属性 */
  transition?: string
  /** 是否在初始渲染时启用过渡动画 */
  transitionAppear?: boolean
  /** 指定挂载的节点，等同于 Teleport 组件的 to 属性 */
  teleport?: string | Element
  /** 是否开启顶部安全区适配 */
  safeAreaInsetTop?: boolean
  /** 是否开启底部安全区适配 */
  safeAreaInsetBottom?: boolean
}

export const popupDefaultProps = {
  overlay: true,
  position: 'bottom' as PopupProps['position'],
  overlayClass: () => ({}),
  overlayStyle: () => ({}),
  overlayProps: () => ({}),
  duration: 0.3,
  zIndex: 2000,
  round: true,
  destroyOnClose: false,
  lockScroll: true,
  lazyRender: true,
  closeOnPopstate: false,
  closeOnClickOverlay: true,
  closeable: false,
  closeIcon: 'cross',
  closeIconPosition: 'top-right' as PopupProps['closeIconPosition'],
  iconPrefix: 'van-icon',
  transitionAppear: false,
  safeAreaInsetTop: false,
  safeAreaInsetBottom: false,
}
