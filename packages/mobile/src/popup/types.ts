import type { CSSProperties } from 'vue'

export interface PopupProps {
  overlay?: boolean
  position?: 'top' | 'bottom' | 'left' | 'right'
  overlayClass?: string | Array<string> | object
  overlayStyle?: CSSProperties
  /** 动画时长，单位秒，设置为 0 可以禁用动画 */
  duration?: number | string
  /** 弹窗 z-index 层级 */
  zIndex?: number | string
  /** 弹窗是否圆角 */
  round?: boolean
  /** 是否显示关闭图标 */
  closeable?: boolean
  title?: string
}

export const popupDefaultProps = {
  overlay: true,
  position: 'bottom' as PopupProps['position'],
  duration: 0.3,
  zIndex: 2000,
  round: true,
  overlayClass: () => ({}),
  overlayStyle: () => ({ }),
  closeable: false,
}
