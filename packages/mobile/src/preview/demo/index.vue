<script setup>
import { ref } from 'vue'
import Button from '../../button/index.vue'
import Preview from '../index.vue'
import 'vant/lib/index.css'
import '../../global.css'

const imgVisible = ref(false)
const pdfVisible = ref(false)
const txtVisible = ref(false)
const excelVisible = ref(false)
const wordVisible = ref(false)
const pptVisible = ref(false)
const imgUrls = [
  'https://hrt-devimages.hrttest.cn/1754387268937u=937513426,2161739410&fm=193&f=GIF.jpg',
  'https://hrt-devimages.hrttest.cn/1753250115180%E5%87%BA%E9%99%A2%E5%B8%A6%E8%8D%AF.png',
  'https://hrt-devimages.hrttest.cn/1754360899985223566.jpg',
]
const pdfUrl = 'https://hrt-devimages.hrttest.cn/1755151638344test.pdf'
const txtUrl = 'https://hrt-devimages.hrttest.cn/1755152237814test.txt'
const wordUrl = 'https://hrt-devimages.hrttest.cn/1755152303559test.docx'
const excelUrl = 'https://hrt-devimages.hrttest.cn/1755152127772test.xlsx'
const pptUrl = 'https://hrt-devimages.hrttest.cn/1755155541906test.pptx'
</script>

<template>
  <demo-block title="图片预览">
    <Button size="small" @click="imgVisible = true">
      预览
    </Button>
    <Preview
      v-model:show="imgVisible"
      style="margin-left: 15px"
      type="img"
      :images="imgUrls"
      :start-position="1"
    />
  </demo-block>
  <demo-block title="pdf预览">
    <Button size="small" @click="pdfVisible = true">
      预览
    </Button>
    <Preview
      v-model:show="pdfVisible"
      style="margin-left: 15px"
      :file="pdfUrl"
    />
  </demo-block>

  <demo-block title="txt预览">
    <Button size="small" @click="txtVisible = true">
      预览
    </Button>
    <Preview
      v-model:show="txtVisible"
      style="margin-left: 15px"
      :file="txtUrl"
    />
  </demo-block>
  <demo-block title="excel 预览">
    <Button size="small" @click="excelVisible = true">
      预览
    </Button>
    <Preview
      v-model:show="excelVisible"
      style="margin-left: 15px"
      :file="excelUrl"
    />
  </demo-block>
  <demo-block title="word 预览">
    <Button size="small" @click="wordVisible = true">
      预览
    </Button>
    <Preview
      v-model:show="wordVisible"
      style="margin-left: 15px"
      :file="wordUrl"
    />
  </demo-block>
  <demo-block title="ppt 预览">
    <Button size="small" @click="pptVisible = true">
      预览
    </Button>
    <Preview
      v-model:show="pptVisible"
      style="margin-left: 15px"
      :file="pptUrl"
    />
  </demo-block>
</template>
