<script setup lang="ts">
import But<PERSON> from '../../button/index.vue'

defineOptions({
  name: 'PreviewContent',
})
</script>

<template>
  <div class="container">
    <Button type="primary" class="close-btn">
      关闭预览
    </Button>
    <div class="slot-content" @click.stop>
      <slot />
    </div>
  </div>
</template>

<style scoped lang="less">
.container {
  padding: 72px 0 8px 0;
  height: 100vh;
  width: 100vw;
  position: relative;
}
.close-btn {
  position: absolute;
  top: 8px;
  right: 8px;
}
.slot-content {
  width: 100%;
  height: 100%;
}
</style>
