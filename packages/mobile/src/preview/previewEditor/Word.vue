<script setup lang="ts">
import VueOfficeDocx from '@vue-office/docx/lib/v3'
import { Overlay } from 'vant'
import { ref } from 'vue'
import Content from './Content.vue'
import '@vue-office/docx/lib/v3/index.css'

interface IProps {
  value?: string
}
defineOptions({
  name: 'PreviewWord',
})
const props = defineProps<IProps>()
const show = defineModel('show', {
  required: true,
  type: Boolean,
  default: false,
})
const hasError = ref(false)

function errorHandler() {
  hasError.value = true
}
</script>

<template>
  <Overlay :show="show" @click="show = false">
    <Content>
      <VueOfficeDocx
        v-if="!hasError"
        :src="props.value"
        class="view-word"
        @error="errorHandler"
      />
      <div v-else class="error">
        文件解析失败！
      </div>
    </Content>
  </Overlay>
</template>

<style  scoped lang="less">
.error {
  text-align: center;
  font-size: 30px;
  color: rgba(153, 153, 153, 1);
}
.view-word {
  height: 100%;
  width: 100%;
  overflow: scroll;
}
</style>
