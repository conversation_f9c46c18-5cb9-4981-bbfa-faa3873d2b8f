<script setup lang="ts">
import { Overlay } from 'vant'
import { ref, watch } from 'vue'
import Content from './Content.vue'

interface IProps {
  value?: string
}
defineOptions({
  name: 'PreviewTxt',
})
const props = defineProps<IProps>()

const show = defineModel('show', {
  required: true,
  type: Boolean,
  default: false,
})
const txtContent = ref('')

function getTxt() {
  fetch(props.value!, {
    headers: {
      Origin: '*',
    },
  }).then(res => res.text()).then((res) => {
    txtContent.value = res
  })
}

watch(
  () => show.value,
  (val) => {
    if (val && props.value) {
      getTxt()
    }
  },
)
</script>

<template>
  <Overlay :show="show" @click="show = false">
    <Content>
      <div class="content">
        {{ txtContent }}
      </div>
    </Content>
  </Overlay>
</template>

<style lang="less">
.content {
  height: 100%;
  padding: 12px;
  color: #000;
  background: #fff;
  white-space: pre-wrap;
}
</style>
