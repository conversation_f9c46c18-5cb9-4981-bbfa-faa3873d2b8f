<script setup lang="ts">
import { Overlay } from 'vant'
import Content from './Content.vue'

interface IProps {
  value?: string
}
defineOptions({
  name: 'PreviewPdf',
})
defineProps<IProps>()
const show = defineModel('show', {
  required: true,
  type: Boolean,
  default: false,
})
</script>

<template>
  <Overlay :show="show" @click="show = false">
    <Content>
      <iframe width="100%" height="100%" frameBorder="0" :src="`${value}#toolbar=0`" />
    </Content>
  </Overlay>
</template>

<style lang="less">

</style>
