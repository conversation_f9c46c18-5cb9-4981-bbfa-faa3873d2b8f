<script setup lang="ts">
import VueOfficeExcel from '@vue-office/excel'
import { Overlay } from 'vant'
import { ref } from 'vue'
import Content from './Content.vue'
import '@vue-office/excel/lib/v3/index.css'

defineOptions({
  name: 'PreviewExcel',
})
const props = defineProps<IProps>()

interface IProps {
  value?: string
}
const show = defineModel('show', {
  required: true,
  type: Boolean,
  default: false,
})
const hasError = ref(false)

function errorHandler() {
  hasError.value = true
}
</script>

<template>
  <Overlay :show="show" @click="show = false">
    <Content>
      <VueOfficeExcel
        v-if="!hasError"
        :src="props.value"
        class="view-excel"
        @error="errorHandler"
      />
      <div v-else class="error">
        文件解析失败！
      </div>
    </Content>
  </Overlay>
</template>

<style  scoped lang="less">
.error {
  text-align: center;
  font-size: 30px;
  color: rgba(153, 153, 153, 1);
}
.view-excel {
  height: 100%;
  width: 100%;
  overflow: scroll;
}
</style>
