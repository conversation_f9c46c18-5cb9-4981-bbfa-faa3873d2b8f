<script setup lang="ts">
import type { ImagePreviewProps } from 'vant'
import { ImagePreview } from 'vant'

interface IProps extends /* @vue-ignore */ Partial<ImagePreviewProps> {
}
defineOptions({
  name: 'PreviewImg',
})
const props = defineProps<IProps>()
const show = defineModel('show', {
  required: true,
  type: Boolean,
  default: false,
})
</script>

<template>
  <ImagePreview v-bind="{ ...props, ...$attrs }" v-model:show="show">
    <template v-for="(_, name) in $slots" :key="name" #[name]="scope">
      <slot :name="name" v-bind="scope || {}" />
    </template>
  </ImagePreview>
</template>

<style lang="less">
</style>
