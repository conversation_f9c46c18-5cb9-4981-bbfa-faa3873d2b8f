<script setup lang="ts">
import { Overlay } from 'vant'
import { computed } from 'vue'
import Content from './Content.vue'

interface IProps {
  value?: string
}
defineOptions({
  name: 'PreviewPpt',
})
const props = defineProps<IProps>()
const show = defineModel('show', {
  required: true,
  type: Boolean,
  default: false,
})

const viewUrl = computed(() => `https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(props.value!)}`)
</script>

<template>
  <Overlay :show="show" @click="show = false">
    <Content>
      <iframe width="100%" height="100%" frameBorder="0" :src="viewUrl" @click.stop />
    </Content>
  </Overlay>
</template>
