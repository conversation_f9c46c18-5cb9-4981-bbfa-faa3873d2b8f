# DemoButton 按钮

### 介绍

文件预览组件， img， pdf, txt, excel, word, ppt， 当前主要 img 适用, 有需求可针对性扩展其他类型

### 引入

```js
import { Preview } from '@hrt/mobile'
import Vue from 'vue'

Vue.use(Preview)
```

## 代码演示

### 图片预览

```html
// 图片预览需要传入类型 type='img', 其他参数同 vant imagePreview
<Preview v-model:show="imgVisible" style="margin-left: 15px" type="img" :images="imgUrls" :start-position="1" />

```

### pdf预览

```html
<Preview v-model:show="imgVisible" style="margin-left: 15px" :file="fileUrl" />
```

### txt预览

```html
<Preview v-model:show="imgVisible" style="margin-left: 15px" :file="fileUrl" />
```

### excel预览

```html
<Preview v-model:show="imgVisible" style="margin-left: 15px" :file="fileUrl" />
```

### word预览

```html
<Preview v-model:show="imgVisible" style="margin-left: 15px" :file="fileUrl" />
```

## API

### Props （其他参考 vant）

| 参数 | 说明                                 | 类型                                                  | 默认值 |
| ---- | ------------------------------------ | ----------------------------------------------------- | ------ |
| type | 预览文件类型(除图片外其他类型可不传) | 'img' \| 'pdf' \| 'txt' \| 'ppt' \| 'excel' \| 'word' | -      |
| file | 文件地址（非图片）                   | string >                                              | -      |

### Events (图片预览事件参考 [ImagePreview ](https://vant-ui.github.io/vant/#/zh-CN/image-preview) )

### Slots （图片类型参考 Vant ImagePreview，其他类型暂无）
