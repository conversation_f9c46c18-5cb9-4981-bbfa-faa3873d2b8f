<script setup lang="ts">
import type { ImagePreviewProps } from 'vant'
import { computed, useAttrs } from 'vue'
import PreviewExcel from './previewEditor/Excel.vue'
import PreviewImg from './previewEditor/Img.vue'
import PreviewPdf from './previewEditor/Pdf.vue'
import PreviewPpt from './previewEditor/Ppt.vue'
import PreviewTxt from './previewEditor/Txt.vue'
import PreviewWord from './previewEditor/Word.vue'

interface IProps extends /* @vue-ignore */ Partial<ImagePreviewProps> {
  type?: 'img' | 'pdf' | 'txt' | 'ppt' | 'excel' | 'word'
  file?: string
  useIframe?: boolean
}
defineOptions({
  name: 'HrtPreview',
  inheritAttrs: false,
})

const props = defineProps<IProps>()
const show = defineModel('show', {
  required: true,
  type: Boolean,
  default: false,
})

const attrs = useAttrs()
function checkFileType(types: string[]) {
  const url = props.file
  return types.some(type => url?.includes(type))
}
const curType = computed(() => {
  if (props.type) {
    return props.type
  }
  if (checkFileType(['.pdf'])) {
    return 'pdf'
  }
  if (checkFileType(['.txt'])) {
    return 'txt'
  }
  if (checkFileType(['.xlsx', 'xls'])) {
    return 'excel'
  }
  if (checkFileType(['.doc', '.docx'])) {
    return 'word'
  }
  if (checkFileType(['.ppt', '.pptx', 'pot', 'pps'])) {
    return 'ppt'
  }
  return 'img'
})
const curImages = computed(() => attrs.images as string[] || (props.file ? [props.file] : []))
</script>

<template>
  <PreviewImg v-if="curType === 'img'" v-bind="{ ...props, ...$attrs, ...attrs }" v-model:show="show" :images="curImages">
    <template v-for="(_, name) in $slots" :key="name" #[name]="scope">
      <slot :name="name" v-bind="scope || {}" />
    </template>
  </PreviewImg>
  <PreviewPdf v-if="curType === 'pdf'" v-model:show="show" :value="file" />
  <PreviewTxt v-if="curType === 'txt'" v-model:show="show" :value="file" />
  <PreviewExcel v-if="curType === 'excel'" v-model:show="show" :value="file" />
  <PreviewPpt v-if="curType === 'ppt'" v-model:show="show" :value="file" />
  <PreviewWord v-if="curType === 'word'" v-model:show="show" :value="file" />
</template>

<style lang="less">
</style>
