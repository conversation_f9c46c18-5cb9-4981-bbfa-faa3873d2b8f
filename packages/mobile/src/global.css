@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --color-gb1: #eef1ff;
  /*  */
  --color-gb2: #e5ecfd;
  /* 失效色 */
  --color-gb3: #9baffc;
  /* 默认色 */
  --color-gb4: #6885f8;
  /* 主色 */
  --color-gb6: #2953f5;
  /* 主色active状态色 */
  --color-gb8: #132f9b;
  /* 成功状态消息中的边框颜色 */
  --color-g1: #f7fff0;
  /* 成功消息中的文本颜色 */
  --color-g6: #62d12a;
  /* 警告消息中的背景颜色 */
  --color-o1: #fff2e8;
  /* 警告消息中的文本颜色,警告消息中的边框颜色 */
  --color-o6: #ff7d1a;
  /* 错误状态消息中的背景颜色 */
  --color-r1: #fff3f2;
  /* 错误消息中的文本颜色,错误状态消息中的边框颜色 */
  --color-r6: #fd513e;
  /* 页面后面的背景色 */
  --color-n1: #f4f7fb;
  /* 控件背景 */
  --color-n2: #f7f7f7;
  --color-n3: #e9e8eb;
  --color-n4: #d8d8d8;
  --color-n5: #979797;
  --color-n6: #aeaeae;
  --color-n7: #999;
  --color-n8: #333;
  --color-n9: #111;
  --color-w: #fff;
  /* 设计辅助，如评分打星 */
  --color-y6: #ffbd38;
  --color-primary: var(--color-gb6);
  --color-success: var(--color-g6);
  --color-warning: var(--color-o6);
  --color-danger: var(--color-r6);
  --color-primary-text: var(--color-n8);
  /* 蓝色微渐变色 */
  --color-bg-gb1: linear-gradient(270deg, #2953f5 0%, #5794ff 100%);
  /* 红色微渐变色 */
  --color-bg-gr1: linear-gradient(90deg, #f88073 0%, #fd513e 100%);
  /* 橙色微渐变色 */
  --color-bg-go1: linear-gradient(90deg, #ffa257 0%, #ff7d1a 100%);
  /* 绿色微渐变色 */
  --color-bg-gg1: linear-gradient(90deg, #8bee59 0%, #62d12a 100%);
  --text-size-xs: 12px;
  --text-size-sm: 14px;
  --text-size-base: 16px;
  --text-size-lg: 18px;
  --text-size-xl: 21px;
  --text-size-2xl: 24px;
  --text-size-3xl: 28px;
  --text-size-4xl: 32px;
  --text-leading-xs: 16px;
  --text-leading-sm: 21px;
  --text-leading-base: 24px;
  --text-leading-lg: 27px;
  --text-leading-xl: 30px;
  --gap-base: 8px;
  --gap-middle: 12px;
  --gap-large: 16px;
  --rounded: 2px;
  --rounded-md: 3px;
  --rounded-lg: 4px;
  --rounded-xl: 6px;
  --rounded-2xl: 8px;
  --shadow-sm: 0px 0px 4px 0px rgba(178, 188, 206, 0.2);
  --shadow-md: 0px 2px 8px 0px rgba(178, 188, 206, 0.2);
  --shadow-lg: 0px 2px 24px 0px rgba(178, 188, 206, 0.2);
}

:root:root {
  --van-button-primary-background: var(--color-gb6);
  --van-primary-color: var(--color-gb6);
  --van-success-color: var(--color-g6);
  --van-danger-color: var(--color-r6);
  --van-border-color: var(--color-n3);
  --van-orange: var(--color-o6);
  --van-button-default-background: var(--color-n3);
  --van-button-default-border-color: var(--color-n3);
  --van-button-normal-padding: 0 12px;
  --van-button-normal-font-size: var(--text-size-base);
  --van-button-default-line-height: var(--text-leading-base);
  --van-button-default-height: 32px;
  --van-divider-border-color: var(--color-n3);
  --van-divider-text-color: var(--color-n6);
  --van-divider-font-size: var(--text-size-sm);
  --van-text-color: var(--color-n8);
  --van-cell-font-size: var(--text-size-base);
  --van-cell-vertical-padding: 12.5px;
  --van-cell-horizontal-padding: 16px;
  --van-field-placeholder-text-color: var(--color-n7);
  --van-disabled-opacity: 0.4;
  --van-picker-cancel-action-color: var(--color-gb6);
  --van-picker-action-padding: 0 12px;
  --van-picker-action-font-size: var(--text-size-base);
}
