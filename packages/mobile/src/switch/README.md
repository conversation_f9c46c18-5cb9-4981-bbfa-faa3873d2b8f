# Switch 开关

### 介绍

用于在打开和关闭状态之间进行切换。

## 代码演示

### 基础用法

```html
<HrtSwitch v-model="value" />
```

### 带描述开关

```html
<HrtSwitch v-model="value1">
  <template #node>
    <div class="icon-wrapper" :class="{ checked: value1 }">{{ value1 ? '开' : '关' }}</div>
  </template>
</HrtSwitch>
```

### 禁用状态

```html
<HrtSwitch v-model="value" disabled />
```

### 加载状态

```html
<HrtSwitch v-model="value" loading />
```

## API

### Props

| 参数           | 说明                          | 类型      | 默认值    |
| -------------- | ----------------------------- | --------- | --------- |
| v-model        | 开关选中状态                  | _any_     | `false`   |
| loading        | 是否为加载状态                | _boolean_ | `false`   |
| disabled       | 是否为禁用状态                | _boolean_ | `false`   |
| size           | 开关按钮的尺寸，默认单位为 px | _number_  | `21px`    |
| active-color   | 打开时的背景色                | _string_  | `#2953F5` |
| inactive-color | 关闭时的背景色                | _string_  | `#D8D8D8` |
| active-value   | 打开时对应的值                | _any_     | `true`    |
| inactive-value | 关闭时对应的值                | _any_     | `false`   |

### Events

| 事件名 | 说明               | 回调参数            |
| ------ | ------------------ | ------------------- |
| change | 开关状态切换时触发 | _value: any_        |
| click  | 点击时触发         | _event: MouseEvent_ |

### Slots

| 名称       | 说明                 | 参数 |
| ---------- | -------------------- | ---- |
| node       | 自定义按钮的内容     | -    |
| background | 自定义开关的背景内容 | -    |
