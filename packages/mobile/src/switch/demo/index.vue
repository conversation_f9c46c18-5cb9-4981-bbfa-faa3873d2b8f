<script setup>
import { ref } from 'vue'
import HrtSwitch from '../index.vue'
import 'vant/lib/index.css'
import '../../global.css'

const value = ref(false)
const value1 = ref(false)
</script>

<template>
  <demo-block title="基础用法">
    <div class="hrt-px-4">
      <HrtSwitch v-model="value" />
    </div>
  </demo-block>
  <demo-block title="带描述开关">
    <div class="hrt-px-4">
      <HrtSwitch v-model="value1">
        <template #node>
          <div class="icon-wrapper" :class="{ checked: value1 }">
            {{ value1 ? '开' : '关' }}
          </div>
        </template>
      </HrtSwitch>
    </div>
  </demo-block>
  <demo-block title="禁用状态">
    <div class="hrt-px-4">
      <HrtSwitch v-model="value" disabled />
    </div>
  </demo-block>
  <demo-block title="加载状态">
    <div class="hrt-px-4">
      <HrtSwitch v-model="value" loading />
    </div>
  </demo-block>
</template>

<style lang="css" scoped>
.icon-wrapper {
  font-size: 12px;
  color: #d8d8d8;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  &.checked {
    color: #2953f5;
  }
}
</style>
