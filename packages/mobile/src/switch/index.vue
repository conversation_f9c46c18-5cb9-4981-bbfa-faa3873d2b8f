<script setup lang="ts">
import type { SwitchProps } from './types'
import { Switch } from 'vant'
import { switchDefaultProps } from './types'
import '../global.css'

const props = withDefaults(defineProps<SwitchProps>(), switchDefaultProps)
const emit = defineEmits<{
  (e: 'click', event: MouseEvent): void
  (e: 'change', value: any): void
}>()

const modelValue = defineModel()
</script>

<template>
  <Switch
    v-model="modelValue"
    v-bind="$attrs"
    :size="props.size"
    :active-color="props.activeColor"
    :inactive-color="props.inactiveColor"
    :active-value="props.activeValue"
    :inactive-value="props.inactiveValue"
    :disabled="props.disabled"
    :loading="props.loading"
    @change="emit('change', modelValue)"
    @click="emit('click', $event)"
  >
    <template v-if="$slots.node" #node>
      <slot name="node" />
    </template>
    <template v-if="$slots.background" #background>
      <slot name="background" />
    </template>
  </Switch>
</template>
