import type {
  PickerFieldNames,
  PickerOption,
  PickerToolbarPosition,
} from 'vant'

export interface PickerProps {
  /** 对象数组，配置每一列显示的数据 */
  columns?: PickerOption[] | PickerOption[][]
  columnsFieldNames?: PickerFieldNames
  /** 顶部栏标题 */
  title?: string
  confirmButtonText?: string
  cancelButtonText?: string
  toolbarPosition?: PickerToolbarPosition
  loading?: boolean
  readonly?: boolean
  /** 是否显示顶部栏 */
  showToolbar?: boolean
  allowHtml?: boolean
  optionHeight?: number | string
  /** 可见的选项个数 */
  visibleOptionNum?: number | string
  /** 快速滑动时惯性滚动的时长，单位 ms */
  swipeDuration?: number | string
  selectedStyle?: 'normal' | 'bg'
}

export const pickerDefaultProps = {
  columns: () => [],
  confirmButtonText: '确定',
  cancelButtonText: '取消',
  columnsFieldNames: () => ({
    text: 'text',
    value: 'value',
    children: 'children',
  }),
  toolbarPosition: 'top' as PickerToolbarPosition,
  loading: false,
  optionHeight: 44,
  allowHtml: false,
  showToolbar: true,
  readonly: false,
  visibleOptionNum: 4,
  swipeDuration: 1000,
  selectedStyle: 'normal' as PickerProps['selectedStyle'],
}
