# Picker 选择器

### 介绍

提供多个选项集合供用户选择，支持单列选择、多列选择和级联选择，通常与弹出层组件配合使用。

## 代码演示

### 基础用法

```html
<HrtPicker title="标题" :columns="columns" />
```

### 选择带背景

可通过设置 `selected-style` 属性为 `bg` 改变选中的样式

```html
<HrtPicker title="标题" :columns="columns" selected-style="bg" />
```

## API

### Props

参考[van-picker Props](https://vant-ui.github.io/vant/#/zh-CN/picker#props)

| 参数          | 说明     | 类型           | 默认值   |
| ------------- | -------- | -------------- | -------- |
| selectedStyle | 选中样式 | _normal \| bg_ | `normal` |

### Events

参考[van-picker Events](https://vant-ui.github.io/vant/#/zh-CN/picker#events)

### Slots

参考[van-picker Slots](https://vant-ui.github.io/vant/#/zh-CN/picker#slots)
