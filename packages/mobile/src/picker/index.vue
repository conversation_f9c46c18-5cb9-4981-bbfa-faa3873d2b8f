<script setup lang="ts">
import type { PickerProps } from './types'
import { Picker } from 'vant'
import { pickerDefaultProps } from './types'
import '../global.css'

const props = withDefaults(defineProps<PickerProps>(), pickerDefaultProps)
const emit = defineEmits<{
  (e: 'confirm', event: { selectedValues: any, selectedOptions: any, selectedIndexes: any }): void
  (e: 'cancel', value: { selectedValues: any, selectedOptions: any, selectedIndexes: any }): void
  (e: 'change', value: { selectedValues: any, selectedOptions: any, selectedIndexes: any, columnIndex: number }): void
  (e: 'clickOption', value: { selectedValues: any, selectedOptions: any, selectedIndexes: any, columnIndex: number, currentOption: any }): void
  (e: 'scrollInto', value: { columnIndex: number, currentOption: any }): void
}>()

const modelValue = defineModel<string[] | number[]>()
</script>

<template>
  <Picker
    v-model="modelValue"
    v-bind="$attrs"
    :columns="props.columns"
    :allow-html="props.allowHtml"
    :cancel-button-text="props.cancelButtonText"
    :confirm-button-text="props.confirmButtonText"
    :toolbar-position="props.toolbarPosition"
    :loading="props.loading"
    :readonly="props.readonly"
    :show-toolbar="props.showToolbar"
    :option-height="props.optionHeight"
    :visible-option-num="props.visibleOptionNum"
    :swipe-duration="props.swipeDuration"
    :columns-field-names="props.columnsFieldNames"
    :title="props.title"
    :class="{
      'hrt-picker--bg': props.selectedStyle === 'bg',
    }"
    @change="(e) => emit('change', e)"
    @click-option="(e) => emit('clickOption', e)"
    @scroll-into="(e) => emit('scrollInto', e)"
    @confirm="(e) => emit('confirm', e)"
    @cancel="(e) => emit('cancel', e)"
  >
    <template v-for="(_, name) in $slots" #[name]="slotData">
      <slot :name="name" v-bind="slotData" />
    </template>
  </Picker>
</template>

<style lang="css">
.van-picker {
  border-radius: 8px 8px 0 0;
}
.van-picker__toolbar {
  border-bottom: 1px solid var(--color-n3);
}
.van-picker__title.van-picker__title {
  font-weight: 400;
}
.van-picker-column {
  z-index: 3;
}
.hrt-picker--bg {
  .van-picker__frame.van-picker__frame {
    background-color: var(--color-n2);
    left: 12px;
    right: 12px;
    border-radius: 4px;
    &::after {
      border: none;
    }
  }
}

.van-picker__frame.van-picker__frame {
  left: 0;
  right: 0;
}
</style>
