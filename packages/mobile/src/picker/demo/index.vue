<script setup>
import { ref } from 'vue'
import HrtPicker from '../index.vue'
import 'vant/lib/index.css'
import '../../global.css'

const columns = ref([
  {
    text: '居民户口簿',
    value: '1',
  },
  {
    text: '身份证',
    value: '2',
  },
  {
    text: '军官证',
    value: '3',
  },
  {
    text: '港澳通行证',
    value: '4',
  },
])

const multiColumns = ref([
  [
    { text: '周一', value: 'Monday' },
    { text: '周二', value: 'Tuesday' },
    { text: '周三', value: 'Wednesday' },
    { text: '周四', value: 'Thursday' },
    { text: '周五', value: 'Friday' },
  ],
  // 第二列
  [
    { text: '上午', value: 'Morning' },
    { text: '下午', value: 'Afternoon' },
    { text: '晚上', value: 'Evening' },
  ],
])
</script>

<template>
  <demo-block title="基础用法">
    <div>
      <HrtPicker title="标题" :columns="columns" />
    </div>
  </demo-block>
  <demo-block title="选择带背景">
    <div>
      <HrtPicker title="标题" :columns="columns" selected-style="bg" />
    </div>
  </demo-block>
  <demo-block title="多列选择">
    <div>
      <HrtPicker title="标题" :columns="multiColumns" />
    </div>
  </demo-block>
</template>

<style lang="css" scoped>

</style>
