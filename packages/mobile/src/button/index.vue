<script lang="ts" setup>
import type { ButtonProps } from './types'
import { cva } from 'cva'
import { buttonDefaultProps } from './types'
import '../global.css'

defineOptions({
  name: 'HrtButton',
})

const props = withDefaults(defineProps<ButtonProps>(), buttonDefaultProps)

const button = cva({
  base: 'hrt-button',
  variants: {
    type: {
      primary: 'hrt-button--primary',
      secondary: 'hrt-button--secondary',
      text: 'hrt-button--text',
      default: 'hrt-button--default',
      success: 'hrt-button--success',
      danger: 'hrt-button--danger',
      warning: 'hrt-button--warning',
    },
    size: {
      large: 'hrt-button--large',
      medium: 'hrt-button--medium',
      small: 'hrt-button--small',
      normal: 'hrt-button--normal',
    },
    plain: {
      true: 'hrt-button--plain',
    },
    ghost: {
      true: 'hrt-button--ghost',
    },
    dashed: {
      true: 'hrt-button--dashed',
    },
    block: {
      true: 'hrt-button--block',
    },
    round: {
      true: 'hrt-button--round',
    },
    disabled: {
      true: 'hrt-button--disabled',
    },
  },
  defaultVariants: buttonDefaultProps,
})
</script>

<template>
  <button
    v-bind="$attrs"
    :class="button(props)"
    :disabled="disabled"
  >
    <slot />
  </button>
</template>

<style lang="less">
.hrt-button {
  outline: none;
  border: 1px solid transparent;
  box-sizing: border-box;
  border-radius: 4px;
  cursor: pointer;
}

.hrt-button--large {
  padding: 5.5px 11px;
  font-size: var(--text-size-lg);
  line-height: var(--text-leading-lg);
}

.hrt-button--normal {
  padding: 3px 11px;
  font-size: var(--text-size-base);
  line-height: var(--text-leading-base);
}

.hrt-button--small {
  padding: 2.5px 11px;
  font-size: var(--text-size-sm);
  line-height: var(--text-leading-sm);
}

.hrt-button--success {
  background-color: var(--color-success);
  color: var(--color-w);
  border-color: var(--color-success);
}

.hrt-button--danger {
  background-color: var(--color-danger);
  color: var(--color-w);
  border-color: var(--color-danger);
}

.hrt-button--primary {
  background-color: var(--color-primary);
  color: var(--color-w);
  border-color: var(--color-primary);
}

.hrt-button--warning {
  background-color: var(--color-warning);
  color: var(--color-w);
  border-color: var(--color-warning);
}

.hrt-button--secondary {
  background-color: var(--color-gb1);
  color: var(--color-gb6);
  border-color: var(--color-gb1);
}

.hrt-button--default {
  background-color: var(--color-n3);
  color: var(--color-n8);
  border-color: var(--color-n3);
}

.hrt-button--text {
  background-color: transparent;
  color: var(--color-primary);
  border-color: transparent;
}

.hrt-button--plain {
  background-color: white;
  &.hrt-button--primary {
    color: var(--color-primary);
  }
  &.hrt-button--secondary {
    color: var(--color-gb6);
  }
  &.hrt-button--text {
    color: var(--color-primary);
  }
  &.hrt-button--danger {
    color: var(--color-danger);
  }
  &.hrt-button--warning {
    color: var(--color-warning);
  }
  &.hrt-button--success {
    color: var(--color-success);
  }
}

.hrt-button--ghost {
  background-color: transparent;
  &.hrt-button--primary {
    color: var(--color-primary);
  }
  &.hrt-button--secondary {
    color: var(--color-gb6);
  }
  &.hrt-button--text {
    color: var(--color-primary);
  }
  &.hrt-button--danger {
    color: var(--color-danger);
  }
  &.hrt-button--warning {
    color: var(--color-warning);
  }
  &.hrt-button--success {
    color: var(--color-success);
  }
  &.hrt-button--default {
    color: var(--color-n8);
  }
}

.hrt-button--ghost {
  background-color: transparent;
  &.hrt-button--default {
    border-style: dashed;
  }
}

.hrt-button--dashed {
  border-style: dashed;
}

.hrt-button--block {
  width: 100%;
}

.hrt-button--round {
  border-radius: 120px;
}

.hrt-button--disabled {
  opacity: 0.4;
  cursor: not-allowed;
}
</style>
