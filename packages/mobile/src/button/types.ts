import type { ButtonType } from 'vant'

export interface ButtonProps {
  /** 按钮类型 */
  type?: ButtonType | 'secondary' | 'text'
  color?: string
  /** 按钮尺寸 */
  size?: 'large' | 'normal' | 'small'
  /** 是否为朴素按钮 */
  plain?: boolean
  /** 是否为幽灵按钮 */
  ghost?: boolean
  /** 是否为虚线边框 */
  dashed?: boolean
  /** 是否为圆角按钮 */
  round?: boolean
  /** 是否为块级元素 */
  block?: boolean
  /** 是否为方形按钮 */
  square?: boolean
  disabled?: boolean
}

export const buttonDefaultProps: ButtonProps = {
  type: 'default',
  size: 'normal',
  plain: false,
  ghost: false,
  dashed: false,
  round: false,
  square: false,
  block: false,
}
