<script setup>
import HrtButton from '../index.vue'
import 'vant/lib/index.css'
import '../../global.css'
</script>

<template>
  <demo-block title="基础按钮">
    <div class="grid">
      <HrtButton type="primary">
        按钮
      </HrtButton>
      <HrtButton type="secondary">
        次要按钮
      </HrtButton>
      <HrtButton>
        按钮
      </HrtButton>
      <HrtButton type="primary" plain>
        常规按钮
      </HrtButton>
      <HrtButton type="primary" plain dashed>
        按钮
      </HrtButton>
      <HrtButton type="text">
        按钮
      </HrtButton>
    </div>
  </demo-block>

  <demo-block title="语义按钮">
    <div class="grid">
      <HrtButton type="success">
        成功按钮
      </HrtButton>
      <HrtButton type="danger">
        危险按钮
      </HrtButton>
      <HrtButton type="warning">
        警告按钮
      </HrtButton>
    </div>
  </demo-block>

  <demo-block title="朴素按钮">
    <div class="grid">
      <HrtButton plain type="primary">
        按钮
      </HrtButton>
    </div>
  </demo-block>

  <demo-block title="幽灵按钮">
    <div class="grid">
      <HrtButton ghost type="primary">
        幽灵按钮
      </HrtButton>
      <HrtButton ghost>
        幽灵按钮
      </HrtButton>
      <HrtButton ghost type="danger">
        幽灵按钮
      </HrtButton>
    </div>
  </demo-block>
  <demo-block title="通栏按钮">
    <div class="grid">
      <HrtButton block type="primary">
        通栏按钮
      </HrtButton>
      <HrtButton block type="secondary">
        通栏按钮
      </HrtButton>
      <HrtButton block>
        通栏按钮
      </HrtButton>
    </div>
  </demo-block>
  <demo-block title="通栏按钮-圆角">
    <div class="grid">
      <HrtButton block type="primary" round>
        通栏按钮
      </HrtButton>
      <HrtButton block type="primary" round plain>
        通栏按钮
      </HrtButton>
    </div>
  </demo-block>
  <demo-block title="禁用按钮">
    <div class="grid">
      <HrtButton block type="primary" round disabled>
        通栏按钮
      </HrtButton>
      <HrtButton block type="primary" round plain disabled>
        通栏按钮
      </HrtButton>
    </div>
  </demo-block>
  <demo-block title="尺寸按钮">
    <div class="grid">
      <HrtButton type="primary" round size="large">
        通栏按钮
      </HrtButton>
      <HrtButton type="primary" round>
        通栏按钮
      </HrtButton>
      <HrtButton type="primary" round size="small">
        通栏按钮
      </HrtButton>
    </div>
  </demo-block>
</template>

<style lang="css" scoped>
.grid {
  display: flex;
  flex-wrap: wrap;
  padding: 0 16px;
  gap: 16px;
  align-items: flex-start;
}
</style>
