<script lang="ts" setup>
import type { EmptyProps } from './types'
import { Empty } from 'vant'
import { computed, useSlots } from 'vue'
import box from './empty-box.webp'
import card from './empty-card.webp'
import '../global.css'

defineOptions({
  name: 'HrtEmpty',
})

const props = withDefaults(defineProps<EmptyProps>(), {
  image: 'default',
  imageSize: () => ([167, 'auto']),
})
defineSlots<{
  description?: () => any
  image?: () => any
  default?: () => any
}>()
const slots = useSlots()
const computedImage = computed(() => {
  if (props.image === 'default') {
    return box
  }
  if (props.image === 'card') {
    return card
  }
  return props.image
})
console.log('slots', slots)
</script>

<template>
  <Empty
    v-bind="$attrs"
    :image="computedImage"
    :image-size="imageSize"
    :description="description"
  >
    <template v-for="(_, name) in $slots" #[name]="slotData">
      <slot :name="name" v-bind="slotData || {}" />
    </template>
    <div class="flex float-start flex-1" />
  </Empty>
</template>

<style lang="less">
.hrt-button {
  outline: none;
  border: 1px solid transparent;
  box-sizing: border-box;
  border-radius: 4px;
  cursor: pointer;
  @apply font-mono text-gray-200 leading-none bg-gray-600/70 font-bold text-ellipsis;
}

.hrt-button--large {
  padding: 5.5px 11px;
  font-size: var(--text-size-lg);
  line-height: var(--text-leading-lg);
}

.hrt-button--normal {
  padding: 3px 11px;
  font-size: var(--text-size-base);
  line-height: var(--text-leading-base);
}

.hrt-button--small {
  padding: 2.5px 11px;
  font-size: var(--text-size-sm);
  line-height: var(--text-leading-sm);
}

.hrt-button--success {
  background-color: var(--color-success);
  color: var(--color-w);
  border-color: var(--color-success);
}

.hrt-button--danger {
  background-color: var(--color-danger);
  color: var(--color-w);
  border-color: var(--color-danger);
}

.hrt-button--primary {
  background-color: var(--color-primary);
  color: var(--color-w);
  border-color: var(--color-primary);
}

.hrt-button--warning {
  background-color: var(--color-warning);
  color: var(--color-w);
  border-color: var(--color-warning);
}

.hrt-button--secondary {
  background-color: var(--color-gb1);
  color: var(--color-gb6);
  border-color: var(--color-gb1);
}

.hrt-button--default {
  background-color: var(--color-n3);
  color: var(--color-n8);
  border-color: var(--color-n3);
}

.hrt-button--text {
  background-color: transparent;
  color: var(--color-primary);
  border-color: transparent;
}

.hrt-button--plain {
  background-color: white;
  &.hrt-button--primary {
    color: var(--color-primary);
  }
  &.hrt-button--secondary {
    color: var(--color-gb6);
  }
  &.hrt-button--text {
    color: var(--color-primary);
  }
  &.hrt-button--danger {
    color: var(--color-danger);
  }
  &.hrt-button--warning {
    color: var(--color-warning);
  }
  &.hrt-button--success {
    color: var(--color-success);
  }
}

.hrt-button--ghost {
  background-color: transparent;
  &.hrt-button--primary {
    color: var(--color-primary);
  }
  &.hrt-button--secondary {
    color: var(--color-gb6);
  }
  &.hrt-button--text {
    color: var(--color-primary);
  }
  &.hrt-button--danger {
    color: var(--color-danger);
  }
  &.hrt-button--warning {
    color: var(--color-warning);
  }
  &.hrt-button--success {
    color: var(--color-success);
  }
  &.hrt-button--default {
    color: var(--color-n8);
  }
}

.hrt-button--ghost {
  background-color: transparent;
  &.hrt-button--default {
    border-style: dashed;
  }
}

.hrt-button--dashed {
  border-style: dashed;
}

.hrt-button--block {
  width: 100%;
}

.hrt-button--round {
  border-radius: 120px;
}

.hrt-button--disabled {
  opacity: 0.4;
  cursor: not-allowed;
}
</style>
