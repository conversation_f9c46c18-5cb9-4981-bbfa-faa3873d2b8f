# Empty 空状态

### 介绍

空状态时的占位提示。

### 引入

```js
import { Empty } from '@hrt/mobile'
import Vue from 'vue'

Vue.use(Empty)
```

## 代码演示

### 基础用法

```html
<hrt-empty description="描述文字" />
```

### 卡片空状态

```html
<hrt-empty image="card" description="暂无数据" />
```

## API

### Props

| 参数        | 说明                                                      | 类型                        | 默认值    |
| ----------- | --------------------------------------------------------- | --------------------------- | --------- |
| image       | 图片类型，可选值为 error network search，支持传入图片 URL | _string_                    | `default` |
| image-size  | 图片大小，默认单位为 px                                   | _number \| string \| Array_ | -         |
| description | 图片下方的描述文字                                        | _string_                    | -         |

### Events

| 事件名 | 说明       | 回调参数            |
| ------ | ---------- | ------------------- |
| click  | 点击时触发 | _event: MouseEvent_ |

### Slots

| 名称    | 说明     |
| ------- | -------- |
| default | 默认插槽 |
