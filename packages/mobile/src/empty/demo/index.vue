<script setup>
import HrtButton from '../../button/index.vue'
import HrtEmpty from '../index.vue'
import 'vant/lib/index.css'
import '../../global.css'
</script>

<template>
  <demo-block title="基础用法">
    <div class="px-16">
      <HrtEmpty description="暂无智能设备" :image-size="[167, 'auto']" />
      <HrtButton type="primary" block size="large" round>
        添加设备
      </HrtButton>
    </div>
  </demo-block>
  <demo-block title="基础用法">
    <div class="px-16 flex flex-col items-center">
      <HrtEmpty image="card" description="暂无数据" :image-size="[167, 'auto']" />
      <HrtButton type="primary" size="small" round>
        添加设备
      </HrtButton>
    </div>
  </demo-block>
</template>

<style lang="css" scoped>
</style>
