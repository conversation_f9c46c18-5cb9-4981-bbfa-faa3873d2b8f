export default {
  name: 'mobile',
  build: {
    css: {
      preprocessor: 'less',
      base: 'global.css',
    },
    site: {
      publicPath: '/',
    },
    packageManager: 'pnpm',
    tagPrefix: 'hrt-',
    namedExport: false,
    vetur: {
      tagPrefix: 'hrt-',
    },
    bundleOptions: [{
      minify: false,
      formats: ['es'],
    }],
  },
  site: {
    title: '哈瑞特移动端组件库',
    logo: 'https://fastly.jsdelivr.net/npm/@vant/assets/logo.png',
    nav: [
      {
        title: '开发指南',
        items: [
          {
            path: 'home',
            title: '介绍',
          },
          {
            path: 'quickstart',
            title: '快速上手',
          },
          {
            path: 'develop',
            title: '贡献指南',
          },
        ],
      },
      {
        title: '基础组件',
        items: [
          {
            path: 'button',
            title: 'Button 按钮',
          },
          {
            path: 'popup',
            title: 'Popup 弹窗',
          },
        ],
      },
      {
        title: '表单组件',
        items: [
          {
            path: 'field',
            title: 'Field 输入框',
          },
          {
            path: 'picker',
            title: 'Picker 选择器',
          },
          {
            path: 'switch',
            title: 'Switch 开关',
          },
        ],
      },
      {
        title: '展示组件',
        items: [
          {
            path: 'divider',
            title: 'Divider 分割线',
          },
          {
            path: 'preview',
            title: '文件预览 preview',
          },
        ],
      },
    ],
  },
}
