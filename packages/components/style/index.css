@import 'element-plus/dist/index.css';
/* tailwindcss 主题CSS变量 */
@import '@hrt/tailwindcss/theme.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

:root:root {
  --el-color-primary: var(--hrt-color-blue);
  --el-color-primary-light-3: #5285eb;
  --el-color-primary-dark-2: #1e52ba;
  --el-border-radius-base: 2px;
  --el-color-success: #2fb324;
  --el-color-success-light-3: #4ec244;
  --el-color-success-dark-2: #219117;
  --el-color-warning: #e37221;
  --el-color-warning-light-3: #e88b48;
  --el-color-warning-dark-2: #b85916;
  --el-color-error: #e63746;
  --el-color-danger: #e63746;
  --el-color-danger-light-3: #eb525f;
  --el-color-danger-dark-2: #ba1e2b;
  --el-text-color-primary: #3a4762;
  --el-text-color-regular: #3a4762;
  --el-text-color-secondary: #7a8599;
  --el-color-info: #7a8599;
  --el-radio-input-width: 16px;
  --el-radio-input-height: 16px;
  --el-disabled-border-color: var(--hrt-color-neutral-500);
  --el-disabled-bg-color: var(--hrt-color-neutral-700);
  --el-disabled-text-color: var(--hrt-color-neutral-400);
  .el-input {
    --el-input-border-color: var(--hrt-color-neutral-500);
    --el-input-hover-border-color: var(--hrt-color-blue);
  }
  .el-popover {
    --el-popover-padding: 16px 36px 16px 16px;
    --el-box-shadow-light: 0px 2px 8px 0px rgba(8, 38, 99, 0.16);
  }
}

:root {
  --hrt-tooltip-max-width: 480px;
}

.hrt-text-highlight {
  color: var(--el-color-primary);
}
