/** 正则特殊字符转义 */
export function escapeRegexSpecialChars(str: string) {
  return str?.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
}

/**
 * @description 获取内容中匹配到的关键字
 * @param content 文本内容
 * @param matchStr 关键字
 * @param flags 正则标识符
 * @returns {Array} 关键字数组
 */
export function getHighlightKeywords(content: string, matchStr: string, flags: string = 'i') {
  if (!matchStr || !content) {
    return [content || '']
  }
  const reg = new RegExp(`(${escapeRegexSpecialChars(matchStr)})`, flags)

  if (!flags.includes('g')) {
    const result = content.match(reg)
    if (result === null)
      return [content]
    return [
      content.slice(0, result.index),
      result[0],
      content.slice(result.index! + matchStr.length, content.length),
    ]
  }

  let result
  let currentIndex = 0
  const returnData = []
  // eslint-disable-next-line no-cond-assign
  while ((result = reg.exec(content))) {
    returnData.push(content.slice(currentIndex, result.index))
    returnData.push(result[1])
    currentIndex = result.index + result[0].length
  }
  if (content.length > currentIndex) {
    returnData.push(content.slice(currentIndex, content.length))
  }

  return returnData
}

/**
 * 检查值是否为空
 * @param value 任意值
 */
export function isEmpty(value: unknown): boolean {
  if (value == null)
    return true
  if (typeof value === 'string' || Array.isArray(value))
    return value.length === 0
  if (value instanceof Map || value instanceof Set)
    return value.size === 0
  if (typeof value === 'object')
    return Object.keys(value).length === 0
  return false
}

export function getUuid(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = Math.random() * 16 | 0
    const v = c === 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  })
}
