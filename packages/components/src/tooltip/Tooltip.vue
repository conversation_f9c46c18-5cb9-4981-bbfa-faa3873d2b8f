<script lang="ts" setup>
import { ElTooltip, useTooltipProps } from 'element-plus'
import { getCurrentInstance } from 'vue'

defineOptions({
  name: 'HrtTooltip',
})

const props = defineProps({
  ...useTooltipProps,
})

const vm = getCurrentInstance()

function changeRef(el: any) {
  vm!.exposed = el || {}
  vm!.exposeProxy = el || {}
}
</script>

<template>
  <ElTooltip v-bind="{ ...$attrs, ...props, popperClass: 'hrt-tooltip', ref: changeRef }">
    <template v-for="(_, name) in $slots" :key="name" #[name]="scope">
      <slot :name="name" v-bind="scope || {}" />
    </template>
  </ElTooltip>
</template>

<style lang="less">
.hrt-tooltip.el-tooltip {
  max-width: var(--hrt-tooltip-max-width);
  padding: 8px 12px;
  font-size: 14px;
}
</style>
