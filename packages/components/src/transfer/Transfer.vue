<!-- eslint-disable no-template-curly-in-string -->
<script setup lang="ts">
import type { TransferDirection, TransferFormat, TransferKey } from 'element-plus'
import { ElTransfer, formItemContextKey } from 'element-plus'
import { computed, inject } from 'vue'

interface IProps {
  format?: TransferFormat
  simple?: boolean
  validateEvent?: boolean
}

defineOptions({ name: 'HrtTransfer' })

const props = withDefaults(defineProps<IProps>(), {
  simple: false,
  validateEvent: true,
})
const emits = defineEmits<{
  (e: 'change', value: TransferKey[], direction: TransferDirection, movedKeys: TransferKey[]): void
  (e: 'leftCheckChange', value: TransferKey[], movedKeys?: TransferKey[]): void
  (e: 'rightCheckChange', value: TransferKey[], movedKeys?: TransferKey[]): void
}>()
const formItem = inject(formItemContextKey)
const modelValue = defineModel<Array<string | number>>({ default: () => [] })
const countFormat = computed(() => {
  if (props.format) {
    return props.format
  }

  if (props.simple) {
    return {
      noChecked: '（${total} 项）',
      hasChecked: '（${total} 项）',
    }
  }

  return {
    noChecked: '（0/${total} 项）',
    hasChecked: '（${checked}/${total} 项）',
  }
})

function handleChange(values: TransferKey[], direction: TransferDirection, movedKeys: TransferKey[]) {
  emits('change', [...values], direction, [...movedKeys])
}
function handleLeftCheckChange(values: TransferKey[], movedKeys?: TransferKey[]) {
  emits('leftCheckChange', values, movedKeys)

  if (props.simple) {
    values.forEach((value) => {
      if (!modelValue.value.includes(value)) {
        modelValue.value.push(value)
        handleChange(modelValue.value, 'right', [value])
        props.validateEvent && formItem?.validate('change')
      }
    })
  }
}
function handleRightCheckChange(values: TransferKey[], movedKeys?: TransferKey[]) {
  emits('rightCheckChange', values, movedKeys)

  if (props.simple) {
    values.forEach((value) => {
      const findIndex = modelValue.value.findIndex(checkedValue => checkedValue === value)

      if (findIndex !== -1) {
        modelValue.value.splice(findIndex, 1)
        handleChange(modelValue.value, 'left', [value])
        props.validateEvent && formItem?.validate('change')
      }
    })
  }
}
</script>

<template>
  <ElTransfer
    v-bind="$attrs"
    v-model="modelValue"
    class="hrt-transfer"
    :class="{ 'hrt-transfer__simple': props.simple }"
    :format="countFormat"
    :validate-event="props.validateEvent"
    @change="handleChange"
    @left-check-change="handleLeftCheckChange"
    @right-check-change="handleRightCheckChange"
  >
    <!-- 透传所有插槽（含作用域插槽） -->
    <template v-for="(_, name) in $slots" #[name]="slotProps">
      <slot :name="name" v-bind="slotProps" />
    </template>
  </ElTransfer>
</template>

<style lang="less">
.hrt-transfer.el-transfer {
  --el-transfer-panel-width: 240px;
  --el-transfer-panel-header-height: 32px;
  --el-transfer-panel-header-bg-color: #ffffff;
  --el-transfer-panel-footer-height: 40px;
  --el-transfer-panel-body-height: 260px;
  --el-transfer-item-height: 32px;
  --el-transfer-filter-height: 30px;
  --el-transfer-border-color: var(--hrt-color-neutral-500);
  .el-transfer-panel {
    .el-transfer-panel__header {
      padding: 0 12px;
      .el-checkbox {
        .el-checkbox__label {
          font-size: 14px;
          display: inline-flex;
          align-items: center;
          span {
            position: static;
            font-size: 14px;
            color: var(--el-text-color-primary);
            transform: none;
          }
        }
      }
    }
    .el-transfer-panel__list {
      padding: 2px 0;
      .el-transfer-panel__item {
        padding-left: 12px;
        padding-right: 12px;
        margin-right: 0;
        font-weight: normal;
        &:hover {
          color: var(--el-text-color-primary);
        }
      }
      .el-checkbox__input.is-checked + .el-checkbox__label {
        color: var(--el-text-color-primary);
      }
    }
  }
  .el-transfer__buttons {
    width: 64px;
    padding: 0 16px;
    display: inline-flex;
    flex-direction: column-reverse;
    align-items: center;
    justify-content: center;
    .el-transfer__button {
      width: 32px;
      margin-left: 0;
      &:first-child {
        margin-top: 8px;
      }
      &.is-disabled {
        background: #f7f8fa;
        border-color: #dcdfe6;
        color: #b8becc;
      }
    }
  }
  .el-transfer-panel__filter {
    padding: 12px 12px 4px 12px;
    .el-input__wrapper {
      padding-right: 38px;
      position: relative;
      .el-input__prefix {
        position: absolute;
        right: 12px;
        .el-input__icon {
          margin: 0;
        }
      }
      .el-input__suffix {
        display: none;
      }
      .el-input__inner {
        font-size: 14px;
      }
    }
  }
}
.hrt-transfer__simple.el-transfer {
  .el-checkbox__input {
    display: none !important;
  }
  .el-checkbox__label {
    padding-left: 0 !important;
  }
  .el-transfer-panel {
    &:first-child {
      .el-transfer-panel__header {
        border-right: 0;
        border-top-right-radius: 0;
      }
      .el-transfer-panel__body {
        border-right: 0;
        border-bottom-right-radius: 0;
      }
    }
    &:last-child {
      .el-transfer-panel__header {
        border-top-left-radius: 0;
      }
      .el-transfer-panel__body {
        border-bottom-left-radius: 0;
      }
    }
    .el-transfer-panel__item {
      &:hover {
        background-color: var(--hrt-color-blue-100);
      }
    }
  }
  .el-transfer__buttons {
    display: none;
  }
}
</style>
