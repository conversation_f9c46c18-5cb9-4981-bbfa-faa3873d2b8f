<script setup lang="ts">
import type { AlertProps } from 'element-plus'
import { ElAlert } from 'element-plus'

export interface IProps extends Partial<AlertProps> {
  /** 卡片样式 */
  isCard?: boolean
}

defineOptions({
  name: 'HrtAlert',
})
const props = withDefaults(defineProps<IProps>(), {
  closable: true,
})
</script>

<template>
  <ElAlert
    class="hrt-alert is-light"
    :class="{ card: isCard, closeable: closable }"
    v-bind="props"
    v-on="$attrs"
  >
    <template v-for="(_, name) in $slots" :key="name" #[name]="scope">
      <slot :name="name" v-bind="scope || {}" />
    </template>
  </ElAlert>
</template>

<style lang="less">
.hrt-alert {
  &.el-alert {
    --el-alert-padding: 11px 16px;
    border-radius: 2px;
    display: flex;

    .el-alert__title {
      font-size: 14px;
      line-height: 20px;
      color: var(--hrt-color-neutral-100);
    }
    &.closeable {
      .el-alert__content {
        padding-right: 20px;
      }
    }
    .el-alert__content .el-alert__description {
      font-size: 14px;
      line-height: 20px;
      color: var(--hrt-color-neutral-400);
    }

    .el-alert__icon {
      top: 3px;
      align-self: start;
      &.is-big {
        align-self: start;
        font-size: 16px;
        top: 3px;
        margin-right: 8px;
      }
    }

    .el-alert__closebtn {
      font-size: 14px;
      color: var(--hrt-color-neutral-400);

      &:hover {
        color: var(--hrt-color-neutral-300);
      }
    }

    &.is-light {
      background: var(--hrt-color-neutral-700);
      border: 1px solid var(--hrt-color-neutral-500);

      &.el-alert--success {
        background-color: var(--hrt-color-green-100);
        border-color: var(--hrt-color-green-400);

        .el-alert__icon {
          color: var(--hrt-color-green-400);
        }
      }

      &.el-alert--warning {
        background-color: var(--hrt-color-orange-100);
        border-color: var(--hrt-color-orange-400);

        .el-alert__icon {
          color: var(--hrt-color-orange-400);
        }
      }

      &.el-alert--error {
        background-color: var(--hrt-color-red-100);
        border-color: var(--hrt-color-red-400);

        .el-alert__icon {
          color: var(--hrt-color-red-400);
        }
      }

      &.el-alert--info {
        background: var(--hrt-color-neutral-700);
        border: 1px solid var(--hrt-color-neutral-500);

        .el-alert__icon {
          color: var(--hrt-color-neutral-400);
        }
      }

      &.el-alert--primary {
        background: var(--hrt-color-blue-100);
        border-color: var(--hrt-color-blue-400);

        .el-alert__icon {
          color: var(--hrt-color-blue-400);
        }
      }
    }
    &.card {
      border-bottom-color: #fff !important;
      border-left-color: #fff !important;
      border-right-color: #fff !important;
      background: #fff !important;
      box-shadow: 0px 2px 8px 0px rgba(8, 38, 99, 0.16);
      border-radius: 4px;
      border-top: 4px solid var(--hrt-color-red-400);
      &.el-alert--success {
        .el-alert__title {
          color: var(--hrt-color-green-400);
        }
      }
      &.el-alert--warning {
        .el-alert__title {
          color: var(--hrt-color-orange-400);
        }
      }
      &.el-alert--error {
        .el-alert__title {
          color: var(--hrt-color-red-400);
        }
      }
      &.el-alert--info {
        .el-alert__title {
          color: var(--hrt-color-neutral-400);
        }
      }
    }
  }
}
</style>
