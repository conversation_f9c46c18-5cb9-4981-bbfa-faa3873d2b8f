.hrt-notice.el-notification {
  --el-notification-width: 400px;
  --el-notification-title-font-size: 14px;
  --el-notification-title-color: #15233f;
  --el-notification-close-color: var(--hrt-color-neutral-300);
  --el-notification-shadow: 0px 4px 24px 0px rgba(8, 38, 99, 0.16);
  --el-notification-radius: 4px;
  --el-notification-padding: 16px 26px 24px 13px;
}
.hrt-notice__dark.el-notification {
  background-color: rgba(17, 17, 17, 0.9);
  --el-notification-title-color: #fff;
  --el-notification-content-color: #fff;
  --el-notification-close-color: #fff;
  --el-notification-close-hover-color: var(--hrt-color-neutral);
}
.hrt-notice__with-icon {
  --hrt-notice-icon-size: 16px;
}
.hrt-notice__with-type {
  --hrt-notice-icon-size: 24px;
}
.hrt-notice__no-title.el-notification {
  --el-notification-padding: 16px 26px 16px 13px;
}
.hrt-notice .el-notification__group {
  margin-left: 3px;
}
.hrt-notice .el-notification__content {
  margin-top: 12px;
  line-height: 20px;
}
.hrt-notice .el-notification__title {
  line-height: 20px;
}
.hrt-notice__with-icon.el-notification .el-notification__icon {
  --el-notification-icon-size: var(--hrt-notice-icon-size);
  position: absolute;
  top: 18px;
  left: 16px;
}
.hrt-notice.el-notification .el-notification--primary,
.hrt-notice.el-notification .el-notification--info,
.hrt-notice.el-notification .el-notification--success,
.hrt-notice.el-notification .el-notification--warning,
.hrt-notice.el-notification .el-notification--error {
  top: 14px;
  --el-notification-icon-size: var(--hrt-notice-icon-size);
}
.hrt-notice__with-icon .el-notification__title {
  text-indent: calc(var(--hrt-notice-icon-size) + 4px);
}
.hrt-notice__no-title .el-notification__content {
  text-indent: calc(var(--hrt-notice-icon-size) + 4px);
}
