import type { NotifyFn } from 'element-plus'
import type { App } from 'vue'
import { ElNotification, notificationTypes } from 'element-plus'
import { isVNode } from 'vue'
import { emptyObject } from '../../utils/base'
import './notice.css'

type HrtNoticeOptions = Parameters<NotifyFn>[0] & {
  theme?: 'light' | 'dark'
}

type HrtNoticeContext = Parameters<NotifyFn>[1]

type NotificationType = typeof notificationTypes[number]

type HrtNoticeMethods = {
  [key in NotificationType]: (
    opts?: HrtNoticeOptions,
    context?: HrtNoticeContext
  ) => ReturnType<NotifyFn>
}

type HrtNoticeFunction
  = { (opts?: HrtNoticeOptions, context?: HrtNoticeContext): ReturnType<NotifyFn> }
    & HrtNoticeMethods
    & {
      closeAll: () => void
    }

function getCustomClassName(opts: HrtNoticeOptions = emptyObject) {
  let customClass = 'hrt-notice'
  if (typeof opts === 'string' || isVNode(opts)) {
    return `${customClass} hrt-notice__no-title`
  }
  customClass = opts.customClass ? `${opts.customClass} ${customClass}` : customClass
  if (opts.theme === 'dark') {
    customClass += ' hrt-notice__dark'
  }
  if (opts.icon || opts.type) {
    customClass += ' hrt-notice__with-icon'
  }
  if (opts.type) {
    customClass += ` hrt-notice__with-type`
  }
  if (!opts.title?.trim()) {
    customClass += ` hrt-notice__no-title`
  }
  return customClass
}

const HrtNotice = function (opts: HrtNoticeOptions = emptyObject, context: HrtNoticeContext = null) {
  const customClass = getCustomClassName(opts)
  const _opts = isVNode(opts) || typeof opts === 'string' ? { message: opts } : opts
  return ElNotification({
    ..._opts,
    customClass,
  }, context)
} as HrtNoticeFunction

// 重写 .success|.warning|.info|.error|.primary 方法
notificationTypes.forEach((type) => {
  (HrtNotice)[type] = (opts: HrtNoticeOptions = emptyObject, context: HrtNoticeContext = null) => {
    const _opts = isVNode(opts) || typeof opts === 'string' ? { message: opts } : opts
    return HrtNotice({
      ..._opts,
      type,
    }, context)
  }
})

Object.assign(HrtNotice, {
  closeAll: () => {
    ElNotification.closeAll()
  },
  install: (app: App) => {
    app.config.globalProperties.$hrtNotice = HrtNotice
  },
})

export { HrtNotice }

export default HrtNotice
