<script lang="ts" setup>
import type { CascaderInstance, CascaderNode, CascaderValue } from 'element-plus'
import { HrtOverflowTooltip } from '@hrt/components'
import { cascaderProps, ElCascader } from 'element-plus'
import { computed, getCurrentInstance, nextTick, onMounted, onUnmounted, ref } from 'vue'
import { getHighlightKeywords } from '../../utils'

defineOptions({
  name: 'HrtCascader',
})

const props = defineProps({
  ...cascaderProps,
  /** 标签效果 */
  tagEffect: {
    ...cascaderProps.tagEffect,
    default: 'light',
  },
  /** 折叠标签tooltip的最大高度 */
  maxCollapseTagsTooltipHeight: {
    ...cascaderProps.maxCollapseTagsTooltipHeight,
    default: 300,
  },
  /** 下拉框的宽度是否与输入框相同 默认true */
  fitInputWidth: {
    type: Boolean,
    default: true,
  },
  /** 无匹配选项时的内容 */
  noMatchText: {
    type: String,
    default: '暂无结果',
  },
  size: {
    ...cascaderProps.size,
    default: '',
  },
})

const emits = defineEmits<{
  visibleChange: [visible: boolean]
  focus: [event: FocusEvent]
  blur: [event: FocusEvent]
  change: [value: CascaderValue]
  expandChange: [value: CascaderValue]
}>()

defineSlots<CascaderInstance['$slots']>()

const cascaderRef = ref<CascaderInstance>()
const containerRef = ref<HTMLElement>()
const popClass = computed(() => {
  return `hrt-cascader__dropdown ${props.popperClass}`
})

const matchStr = ref('')
const focused = ref(false)
const visible = ref(false)
const cascaderFocused = computed(() => focused.value || visible.value)

const vm = getCurrentInstance()
function changeRef(el: any) {
  vm!.exposed = Object.assign(el || {}, { $el: containerRef.value })
  vm!.exposeProxy = Object.assign(el || {}, { $el: containerRef.value })
  cascaderRef.value = el
}

function setDropDownWidth() {
  nextTick(() => {
    const dropdownDom = cascaderRef.value?.contentRef
    if (!dropdownDom)
      return
    const menus = Array.from([
      // 菜单项
      ...dropdownDom.querySelectorAll('.el-cascader-menu') as any,
      // 搜索项
      dropdownDom.querySelector('.el-cascader__suggestion-panel'),
    ]).filter(Boolean) as HTMLElement[]
    menus.forEach((menu) => {
      menu.style.width = `${containerRef.value!.offsetWidth}px`
    })
  })
}
async function onVisibleChange(val: boolean) {
  if (props.fitInputWidth && val) {
    setDropDownWidth()
  }
  if (!val) {
    /** 等待el-cascader内置的focus完成 */
    nextTick(() => {
      // 手动失焦
      (containerRef.value!.querySelector('.el-input__inner')! as HTMLInputElement).blur()
    })
  }
  visible.value = val
  emits('visibleChange', val)
}

function onExpandChange(val: CascaderValue) {
  if (props.fitInputWidth) {
    setDropDownWidth()
  }
  emits('expandChange', val)
}

function onFocus(e: FocusEvent) {
  focused.value = true
  emits('focus', e)
}

function onBlur(e: FocusEvent) {
  focused.value = false
  emits('blur', e)
}

function cascaderFilterMethod(node: CascaderNode, keyword: string) {
  matchStr.value = keyword
  return props.filterMethod(node, keyword)
}

async function onChange(val: CascaderValue) {
  emits('change', val)
  /** 修复多选时element-plus cascader高度计算错误的问题 */
  if (props.props.multiple) {
    const tagsContainer = containerRef.value!.querySelector('.el-cascader__tags') as HTMLElement
    const inputWrapper = containerRef.value!.querySelector('.el-input__wrapper') as HTMLElement
    if (!tagsContainer || !inputWrapper)
      return
    inputWrapper.style.height = `${tagsContainer.offsetHeight}px`
  }
}

/** 下拉框空白区域点击收起下拉框 */
function closeDropDown(e: Event) {
  const target = e.target as HTMLElement
  if (target.querySelector('.el-cascader-menu')) {
    cascaderRef.value?.togglePopperVisible(false)
  }
}
onMounted(() => {
  const dropdownDom = cascaderRef.value?.contentRef
  if (dropdownDom) {
    dropdownDom.addEventListener('click', closeDropDown)
  }
})
onUnmounted(() => {
  const dropdownDom = cascaderRef.value?.contentRef
  if (dropdownDom) {
    dropdownDom.removeEventListener('click', closeDropDown)
  }
})
</script>

<template>
  <div ref="containerRef" class="hrt-cascader-wrapper" :class="props.size ? `hrt-cascader-wrapper--${props.size}` : ''">
    <ElCascader
      v-bind="{
        ...$attrs,
        ...props,
        popperClass: popClass,
        filterMethod: cascaderFilterMethod,
        ref: changeRef,
      }"
      class="hrt-cascader" :class="[
        { 'hrt-cascader--multiple': props.props.multiple },
        { 'multiple-one-line': props.props.multiple && props.collapseTags && props.maxCollapseTags === 1 },
        { 'is-focused': cascaderFocused },
      ]"
      @visible-change="onVisibleChange"
      @expand-change="onExpandChange"
      @focus="onFocus"
      @blur="onBlur"
      @change="onChange"
    >
      <template #default="scope">
        <slot name="default" v-bind="scope">
          <HrtOverflowTooltip :content="scope.node.label" />
        </slot>
      </template>
      <template #suggestion-item="scope">
        <slot name="suggestion-item" v-bind="scope">
          <HrtOverflowTooltip render-slot-template>
            <span
              v-for="(item, i) in getHighlightKeywords(scope.item.text, matchStr)"
              :key="i"
              :class="{ 'hrt-text-highlight': i % 2 !== 0 }"
            >{{ item }}</span>
          </HrtOverflowTooltip>
        </slot>
      </template>
      <template #empty>
        <slot name="empty">
          <li class="el-cascader__empty-text">
            {{ props.noMatchText }}
          </li>
        </slot>
      </template>
      <template v-for="(_, name) in $slots" :key="name" #[name]="scope">
        <slot :name="name" v-bind="scope || {}" />
      </template>
    </ElCascader>
  </div>
</template>

<style lang="less">
@import url(../tag/mixin.less);

.hrt-cascader__tag-mixin() {
  .hrt-tag-mixin();
  padding-left: 6px;
  padding-right: 6px;
  &:not(.is-hit) {
    border-color: var(--el-tag-border-color);
  }
}

.hrt-cascader-wrapper {
  --el-cascader-node-color-disabled: var(--hrt-color-neutral-400);
  --el-border-color-hover: var(--el-color-primary);
  --hrt-cascader-min-height: 32px;
  --hrt-cascader-max-height: 32px;
  display: inline-block;
  width: 100%;
  &.hrt-cascader-wrapper--default {
    width: 240px;
  }
  &.hrt-cascader-wrapper--large {
    width: 364px;
  }
  &.hrt-cascader-wrapper--small {
    width: 116px;
  }
}

.hrt-cascader {
  &.el-cascader {
    height: 100%;
    width: 100%;
    overflow-y: auto;
    border-radius: 2px;
    border: 1px solid var(--el-border-color);
    transition: all 0.25s ease-in-out;
    max-height: var(--hrt-cascader-max-height);
    &.hrt-cascader--multiple:not(.multiple-one-line) {
      --hrt-cascader-max-height: 60px;
      .el-input__wrapper {
        padding-top: 0;
        padding-bottom: 0;
      }
    }
    &::-webkit-scrollbar {
      width: 4px !important;
      height: 4px !important;
    }
    &::-webkit-scrollbar-thumb {
      background-color: var(--hrt-color-neutral);
      border-radius: 2px;
    }

    .el-input__wrapper {
      --el-input-inner-height: calc(var(--hrt-cascader-min-height) - 2px);
      min-height: calc(var(--hrt-cascader-min-height) - 2px);
      max-height: calc(var(--hrt-cascader-max-height) - 2px);
    }

    .el-cascader__tags {
      top: 0;
      transform: translateY(0);
      gap: 4px;
      padding: 3px 4px;
    }
  }
  .el-input__icon.icon-circle-close {
    color: #939cae;
    border-radius: 50%;
    font-size: 16px;

    &:hover {
      background: #eaecef;
    }
  }

  .el-input__wrapper {
    box-shadow: none !important;
  }
  .el-input--suffix {
    position: sticky;
    top: 0;
  }

  .el-input .icon-arrow-down {
    width: 16px;
    height: 16px;
    background: url('../../assets/svgs/caretBottom.svg') no-repeat center;
  }
  .el-input .icon-arrow-down svg {
    display: none;
  }
  .el-input.is-disabled .icon-arrow-down {
    opacity: 0.26;
  }
  .el-cascader__tags .el-tag {
    .hrt-cascader__tag-mixin();
  }

  &:not(.is-disabled):hover,
  &:not(.is-disabled).is-focused {
    border-color: var(--el-border-color-hover);
  }
}

.hrt-cascader--multiple.multiple-one-line {
  .el-cascader__tags {
    flex-wrap: nowrap;
    .el-tag:nth-child(1) {
      min-width: 0;
      max-width: 100%;
    }
  }
}

.hrt-cascader__dropdown {
  max-width: unset !important;
  // pointer-events: none;
  &[data-popper-placement='bottom-start'],
  &[data-popper-placement='bottom'],
  &[data-popper-placement='bottom-end'] {
    margin-top: -8px;
  }
  &[data-popper-placement='top-start'],
  &[data-popper-placement='top'],
  &[data-popper-placement='top-end'] {
    margin-bottom: -8px;
    .el-cascader-panel {
      align-items: flex-end;
    }
  }
  &[data-popper-placement='left-start'],
  &[data-popper-placement='left'],
  &[data-popper-placement='left-end'] {
    margin-right: -8px;
    .el-cascader-panel {
      direction: rtl;
    }
    .el-cascader-node__postfix {
      left: 10px;
      right: unset;
      transform: rotate(180deg);
    }
    .el-cascader-node__label {
      padding-right: 0;
      padding-left: 12px;
      text-align: right;
    }
    .el-cascader-menu + .el-cascader-menu {
      margin-right: 1px;
      margin-left: 0;
    }
  }
  &[data-popper-placement='right-start'],
  &[data-popper-placement='right'],
  &[data-popper-placement='right-end'] {
    margin-left: -8px;
  }

  &.el-popper.is-light {
    background: unset;
  }
  &.el-cascader__dropdown.el-popper {
    box-shadow: none;
    border: none;
  }
  .el-popper__arrow:before {
    display: none;
  }
  .el-cascader-menu {
    min-width: 116px;
    flex-shrink: 0;
    background: #fff;
    box-shadow: var(--hrt-shadow);
    border: none !important;
    border-radius: var(--el-border-radius-base);
    pointer-events: all;
    & + .el-cascader-menu {
      margin-left: 1px;
    }
  }
  .el-cascader-node {
    padding: 0 12px;
    height: 32px;
    line-height: 32px;
    column-gap: 8px;
  }
  .el-cascader-node__label {
    padding-left: 0;
  }
  .el-cascader-node__prefix {
    display: none;
  }

  .el-cascader-node:not(.is-disabled):focus,
  .el-cascader-node:not(.is-disabled):hover {
    background-color: var(--hrt-color-blue-100);
  }

  .el-cascader-node.el-cascader-node.in-active-path,
  .el-cascader-node.el-cascader-node.is-active,
  .el-cascader-node.is-selectable.in-checked-path {
    color: var(--el-text-color-regular);
    background-color: var(--hrt-color-neutral-700);
    font-weight: normal;
  }

  .el-cascader-menu__wrap.el-scrollbar__wrap {
    height: unset;
    max-height: 268px;
  }
}

.el-tooltip {
  .el-cascader__collapse-tags {
    max-width: var(--hrt-tooltip-max-width);
    display: flex;
    flex-wrap: wrap;
    column-gap: 4px;
    .el-tag {
      height: unset;
      min-height: 24px;
      white-space: wrap;
      .hrt-cascader__tag-mixin();
    }
  }
  .el-cascader__suggestion-panel {
    background: #fff;
    box-shadow: var(--hrt-shadow);
  }
  .el-cascader__suggestion-list {
    max-height: 268px;
  }
  .el-cascader__suggestion-item {
    height: 32px;
  }
}
</style>
