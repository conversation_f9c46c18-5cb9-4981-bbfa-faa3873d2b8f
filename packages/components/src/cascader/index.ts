import type { CascaderInstance } from 'element-plus'
import type { App } from 'vue'
import Cascader from './Cascader.vue'

Cascader.install = (app: App) => {
  app.component(Cascader.name || 'HrtCascader', Cascader)
}

export const HrtCascader = Cascader

export interface HrtCascaderInstance extends Omit<CascaderInstance, '$el' | '$props'> {
  $el: HTMLElement | null
  $props: HrtCascaderProps
}

export type HrtCascaderProps = InstanceType<typeof Cascader>['$props']

export default HrtCascader
