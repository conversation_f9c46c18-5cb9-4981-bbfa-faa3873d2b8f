<script setup lang="ts">
import { ElDrawer } from 'element-plus'
import { computed } from 'vue'

defineOptions({
  name: 'HrtDrawer',
})

const props = withDefaults(defineProps<HrtDrawerProps>(), {
  direction: 'rtl',
  showClose: true,
  size: 'large',
  modal: true,
  closeOnClickModal: true,
})

const emit = defineEmits<{
  (e: 'open'): void
  (e: 'opened'): void
  (e: 'close'): void
  (e: 'closed'): void
  (e: 'openAutoFocus'): void
  (e: 'closeAutoFocus'): void
}>()

const modelValue = defineModel<boolean>('modelValue', {
  type: Boolean,
  required: true,
  default: false,
})

export interface HrtDrawerProps {
  /** Drawer 打开的方向 */
  direction?: 'rtl' | 'ltr' | 'ttb' | 'btt'
  /** 是否显示关闭按钮 */
  showClose?: boolean
  /** 抽屉的宽度 */
  size?: 'large' | 'middle' | 'small' | string | number
  /** 是否需要遮罩层 */
  modal?: boolean
  /** 是否可以通过点击 modal 关闭 Drawer */
  closeOnClickModal?: boolean
}

/** 抽屉的宽度 */
const drawerSize = computed(() => {
  if (props.size === 'large')
    return 1200
  if (props.size === 'middle')
    return 540
  if (props.size === 'small')
    return 320
  return props.size
})
</script>

<template>
  <ElDrawer
    v-model="modelValue"
    v-bind="$attrs"
    :direction="direction"
    :size="drawerSize"
    :modal="modal"
    :close-on-click-modal="modal ? false : closeOnClickModal"
    :show-close="showClose"
    @open="emit('open')"
    @opened="emit('opened')"
    @close="emit('close')"
    @closed="emit('closed')"
    @open-auto-focus="emit('openAutoFocus')"
    @close-auto-focus="emit('closeAutoFocus')"
  >
    <template v-for="(_, key) in $slots" #[key]="slotProps" :key="key">
      <slot :name="key" v-bind="slotProps" />
    </template>
  </ElDrawer>
</template>

<style lang="css">
.el-drawer {
  box-shadow: 0 4px 24px 0 rgba(8, 38, 99, 0.16);
  &.rtl {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
  }
  &.ltr {
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
  }
  &.ttb {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
  }
  &.btt {
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
  }
}
</style>
