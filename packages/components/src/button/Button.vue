<script setup lang="ts">
import type { ButtonProps } from 'element-plus'
import { ElButton } from 'element-plus'
import { computed, ref, useAttrs } from 'vue'

defineOptions({
  name: 'HrtButton',
})

const {
  size = 'default',
  block = false,
  type = 'default',
  plain = false,
  disabled = false,
  dashed = false,
  innerLoading = false,
  loading = false,
  asyncClick,
} = defineProps<HrtButtonProps>()

const emit = defineEmits<{
  (e: 'click', event: (close: () => void) => void): void
}>()

export interface HrtButtonProps
  extends Omit<Partial<ButtonProps>, 'size' | 'type' | 'plain'> {
  size?: 'small' | 'large' | 'default'
  /** 按钮是否为块级元素 */
  block?: boolean
  type?:
    | 'primary'
    | 'success'
    | 'warning'
    | 'danger'
    | 'info'
    | 'text'
    | 'default'
  plain?: boolean
  text?: boolean
  link?: boolean
  disabled?: boolean
  dashed?: boolean
  /** 内部加载状态管理 */
  innerLoading?: false
  /** 同步点击状态 */
  asyncClick?: () => Promise<void>
}

const attrs = useAttrs()
const loadingState = ref(false)
const asyncLoading = ref(false)

/** 按钮加载状态 */
const buttonLoading = computed(() => {
  if (asyncClick) {
    return asyncLoading.value
  }
  return innerLoading ? loadingState.value : loading
})

/** 关闭加载状态 */
function close() {
  loadingState.value = false
}

/** 处理点击事件 */
async function handleClick() {
  if (asyncClick) {
    asyncLoading.value = true
    try {
      await asyncClick()
    }
    finally {
      asyncLoading.value = false
    }
    return
  }
  if (innerLoading) {
    loadingState.value = true
  }
  emit('click', close)
}
</script>

<template>
  <ElButton
    v-bind="attrs"
    class="hrt-button"
    :size="size"
    :class="{
      'hrt-button--block': block,
      'hrt-button--dashed': dashed,
    }"
    :type="type"
    :link="link"
    :text="text"
    :plain="type === 'default' || plain"
    :disabled="disabled"
    :loading="buttonLoading"
    @click="handleClick"
  >
    <slot />
  </ElButton>
</template>

<style lang="less">
@import url('./mixin.less');

.hrt-button {
  .hrt-button-mixin();
}
</style>
