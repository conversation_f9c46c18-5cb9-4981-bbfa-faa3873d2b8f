<script setup lang="ts">
export interface TextProps {
  /** 文本类型 */
  type?: 'success' | 'primary' | 'info' | 'warning' | 'danger' | 'default'
  /** 文本行数 */
  lineClamp?: number
  /** 是否截断 */
  truncated?: boolean
  /** 自定义元素标签 */
  tag?: string
}

defineOptions({
  name: 'HrtText',
})

const {
  type = 'default',
  lineClamp,
  truncated = false,
  tag = 'span',
} = defineProps<TextProps>()
</script>

<template>
  <component
    :is="tag"
    class="hrt-text-sm hrt-font-hrt"
    :class="{
      'hrt-text-neutral-200': type === 'default',
      'hrt-text-neutral-300': type === 'info',
      'hrt-text-blue': type === 'primary',
      'hrt-text-green': type === 'success',
      'hrt-text-orange': type === 'warning',
      'hrt-text-red': type === 'danger',
      'is-line-clamp hrt-overflow-hidden': lineClamp,
      'hrt-inline-block hrt-text-ellipsis hrt-whitespace-nowrap hrt-overflow-hidden hrt-max-w-full': truncated,
    }"
    :style="{
      '-webkit-line-clamp': lineClamp,
    }"
  >
    <slot />
  </component>
</template>

<style lang="css" scoped>
.is-line-clamp {
  display: -webkit-inline-box;
  -webkit-box-orient: vertical;
}
</style>
