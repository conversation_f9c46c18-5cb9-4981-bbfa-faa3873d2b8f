<script lang="ts" setup>
import type { inputProps, TagProps } from 'element-plus'
import type { ExtractPropTypes } from 'vue'
import { ElTag } from 'element-plus'
import { getCurrentInstance } from 'vue'

defineOptions({ name: 'HrtTag' })

// const props = defineProps({
//   ...tagProps,
//   prefixIcon: inputProps.prefixIcon,
//   suffixIcon: inputProps.suffixIcon,
// })
const props = defineProps<HrtTagProps>()

type HrtTagProps = Partial<TagProps> & {
  /** 前置图标 */
  prefixIcon?: ExtractPropTypes<typeof inputProps.prefixIcon>
  /** 后置图标 */
  suffixIcon?: ExtractPropTypes<typeof inputProps.suffixIcon>
}

const vm = getCurrentInstance()
function changeRef(el: any) {
  vm!.exposed = el || {}
  vm!.exposeProxy = el || {}
}
</script>

<template>
  <ElTag :ref="changeRef" class="hrt-tag" v-bind="{ ...$attrs, ...props }">
    <template v-for="(_, name) in $slots" :key="name" #[name]="scope">
      <template v-if="name === 'default'">
        <el-icon v-if="props.prefixIcon" class="hrt-tag-icon hrt-tag__prefix">
          <component :is="props.prefixIcon" />
        </el-icon>
        <slot v-bind="scope || {}" />
        <el-icon v-if="props.suffixIcon" class="hrt-tag-icon hrt-tag__suffix">
          <component :is="props.suffixIcon" />
        </el-icon>
      </template>
      <slot v-else :name="name" v-bind="scope || {}" />
    </template>
  </ElTag>
</template>

<style lang="less">
@import url('./mixin.less');

.hrt-tag.el-tag {
  .hrt-tag-mixin();
}
</style>
