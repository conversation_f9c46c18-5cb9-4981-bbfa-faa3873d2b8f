.hrt-tag-mixin() {
  border-radius: 2px;
  font-size: 14px;
  padding: 2px 4px;
  &.el-tag--large {
    padding: 4px 6px;
  }
  &.el-tag--small {
    padding: 0 4px;
    font-size: 12px;
  }
  &.is-round {
    padding: 4px 8px;
  }
  &.el-tag--primary {
    --el-tag-bg-color: var(--hrt-color-blue-100);
    --el-tag-border-color: var(--el-color-primary);
    &.el-tag--dark {
      --el-tag-bg-color: var(--el-color-primary);
    }
  }
  &.el-tag--info {
    --el-tag-bg-color: var(--hrt-color-neutral-700);
    --el-tag-border-color: #dcdfe6;
    --el-tag-text-color: var(--el-text-color-regular);
    &.el-tag--dark {
      --el-tag-bg-color: var(--hrt-color-neutral-700);
      --el-tag-border-color: transparent;
    }
  }
  &.el-tag--success {
    --el-tag-bg-color: #e3f5e1;
    --el-tag-border-color: var(--el-color-success);
    &.el-tag--dark {
      --el-tag-bg-color: var(--el-color-success);
    }
  }
  &.el-tag--warning {
    --el-tag-bg-color: #fcede3;
    --el-tag-border-color: var(--el-color-warning);
    &.el-tag--dark {
      --el-tag-bg-color: var(--el-color-warning);
    }
  }
  &.el-tag--danger {
    --el-tag-bg-color: #ffe6e7;
    --el-tag-border-color: var(--el-color-danger);
    &.el-tag--dark {
      --el-tag-bg-color: var(--el-color-danger);
    }
  }
  &.el-tag--plain {
    --el-tag-bg-color: #fff;
  }

  &.is-closable .el-icon {
    font-size: 10px;
  }

  .hrt-tag-icon {
    display: inline-block;
    vertical-align: middle;
    font-size: 12px;
    margin-left: 2px;
    margin-right: 4px;
    cursor: default;
    &.hrt-tag__suffix {
      margin-left: 4px;
      margin-right: 2px;
    }
  }
}
