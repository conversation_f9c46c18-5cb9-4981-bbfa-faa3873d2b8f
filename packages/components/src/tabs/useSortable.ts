import type { Options } from 'sortablejs'
import type { Ref } from 'vue'
import Sortable from 'sortablejs'
import { nextTick, onUnmounted, ref } from 'vue'

export function useSortable(
  el: Ref<HTMLElement | null>,
  options: Options = {},
) {
  const sortable = ref<Sortable | null>(null)
  console.log('$w_debug: el', el.value)

  const initSortable = () => {
    if (el.value) {
      sortable.value = new Sortable(el.value, {
        animation: 150,
        ghostClass: 'sortable-ghost',
        ...options,
      })
    }
  }

  nextTick(() => {
    initSortable()
  })

  onUnmounted(() => {
    sortable.value?.destroy()
  })
  return sortable
}
