<script setup lang="ts">
import type {
  UploadFile,
  UploadFiles,
  UploadProgressEvent,
  UploadRawFile,
  UploadRequestOptions,
  UploadUserFile,
} from 'element-plus'
import { Plus, UploadFilled } from '@element-plus/icons-vue'
import {
  ElButton,
  ElIcon,
  ElMessage,
  ElUpload,
  formItemContextKey,
  genFileId,
} from 'element-plus'
import * as qiniu from 'qiniu-js'
import { computed, inject, ref, watch } from 'vue'
import { HrtImgPreview } from '../img-preview'
import { getFileName, getFileType } from './utils'

export type IModelValue = (Omit<UploadUserFile, 'name'> & { name?: string })[]
interface IProps {
  modelValue?: IModelValue
  // 是否支持多个文件
  multiple?: boolean
  // 是否启用拖拽上传
  drag?: boolean
  // 是否禁用上传
  disabled?: boolean
  // 文件类型限制，如['jpg', 'jpeg', 'png', 'pdf']
  fileTypes?: string[]
  // 提示，可以用于提示文件限制格式或者文件限制大小
  tip?: string
  // 允许上传文件的最大数量
  limit?: number
  // 允许单个上传文件的大小
  limitSize?: number
  // 文件列表的类型，当值为picture和picture-card时，fileTypes的设置会失效，会被设置为只能上传图片
  listType?: 'text' | 'picture' | 'picture-card'
  // 是否自动上传文件
  autoUpload?: boolean
  // 是否显示已上传文件列表
  showFileList?: boolean
  // 七牛上传需要的token，autoUpload为true时必传
  qiniuToken?: string
  // 七牛域名，autoUpload为true时必传
  qiniuDomain?: string
}

defineOptions({ name: 'HrtUpload' })

const props = withDefaults(defineProps<IProps>(), {
  modelValue: () => [],
  multiple: true,
  drag: false,
  disabled: false,
  fileTypes: () => [],
  tip: '',
  limit: 10,
  limitSize: 50 * 1024 * 1024,
  listType: 'text',
  autoUpload: true,
  showFileList: true,
  qiniuToken: '',
  qiniuDomain: '',
})
const emits = defineEmits(['update:model-value', 'qiniuUploadError'])
const IMG_TYPES = ['jpg', 'jpeg', 'png']
const formItem = inject(formItemContextKey)
// 把modalValue转换成UploadUserFile数据格式
function transModalValue(value: IModelValue): UploadUserFile[] {
  value.forEach((item) => {
    if (item.name) {
      item.name = decodeURIComponent(item.name)
    }
    else {
      item.name = decodeURIComponent(getFileName(item.url))
    }
  })

  return value as UploadUserFile[]
}
const uploadRef = ref()
const fileList = ref<UploadUserFile[]>([])
const loading = ref<boolean>(false)
const imgVisible = ref<boolean>(false)
const imgIndex = ref<number>(0)

const computedFileTypes = computed(() => {
  if (['picture', 'picture-card'].includes(props.listType)) {
    return IMG_TYPES
  }

  if (props.fileTypes.length) {
    return props.fileTypes
  }

  return []
})
const imgFileList = computed(() => fileList.value.filter((file) => {
  if (file.url) {
    return isImg(file)
  }

  return false
}))

watch(
  () => props.modelValue,
  (newValue) => {
    fileList.value = transModalValue(newValue)
  },
  { immediate: true },
)

function validFile(rawFile: UploadRawFile): boolean {
  const fileType = getFileType(rawFile.name)

  // 判断文件上传类型
  if (
    computedFileTypes.value.length
    && !computedFileTypes.value.includes(fileType)
  ) {
    ElMessage.error(`请上传${computedFileTypes.value.join('、')}格式的文件`)

    return false
  }

  // 判断文件上传大小
  if (rawFile.size > props.limitSize) {
    ElMessage.error(`请上传${props.limitSize / (1024 * 1024)}MB以内的文件`)

    return false
  }

  return true
}
function beforeUpload(rawFile: UploadRawFile): boolean {
  return validFile(rawFile)
}
function onChange(uploadFile: UploadFile, uploadFiles: UploadFiles) {
  if (!props.autoUpload && uploadFile.raw) {
    if (!validFile(uploadFile.raw)) {
      const findIndex = uploadFiles.findIndex(
        item => item.uid === uploadFile.uid,
      )

      findIndex !== -1 && uploadFiles.splice(findIndex, 1)
    }

    updateModelValue(uploadFiles)
  }
}
function onExceed(files: File[]) {
  if (props.multiple) {
    ElMessage.error(`最多上传${props.limit}个文件`)
  }
  else {
    const file = files[0] as UploadRawFile

    file.uid = genFileId()
    uploadRef.value!.clearFiles()
    uploadRef.value!.handleStart(file)
    props.autoUpload && uploadRef.value!.submit()
  }
}
function onSuccess(response: any, uploadFile: UploadFile, uploadFiles: UploadFiles) {
  uploadFile.url = response
  updateModelValue(uploadFiles)
}
function onError(error: Error, uploadFile: UploadFile, uploadFiles: UploadFiles) {
  updateModelValue(uploadFiles)
  throw error
}
function onPreview(uploadFile: UploadFile) {
  if (uploadFile.url) {
    // 如果是图片则弹窗预览，其他类型文件新窗口打开预览
    if (isImg(uploadFile)) {
      imgVisible.value = true
      imgIndex.value = imgFileList.value.findIndex(file => file.uid === uploadFile.uid)
    }
    else {
      window.open(uploadFile.url, '_blank')
    }
  }
}
function onRemove(uploadFile: UploadFile, uploadFiles: UploadFiles) {
  updateModelValue(uploadFiles)
}
function updateModelValue(uploadFiles: UploadFiles) {
  if (props.autoUpload) {
    const hasLoadingFile = uploadFiles.some(item =>
      ['ready', 'uploading'].includes(item.status),
    )

    if (!hasLoadingFile) {
      const successFiles: UploadFiles = []

      uploadFiles.forEach((item) => {
        if (item.status === 'success' && item.url) {
          successFiles.push(item)
        }
      })
      emits('update:model-value', successFiles)
      formItem?.validate('change')
    }
  }
  else {
    emits('update:model-value', uploadFiles)
    formItem?.validate('change')
  }
}
function httpRequest(options: UploadRequestOptions) {
  return new Promise((resolve, reject) => {
    try {
      const file = options.file
      const key = `${Date.now()}/${file.name}`
      const putExtra = {
        // 文件原文件名
        fname: '',
        // 用来放置自定义变量
        params: {},
      }
      const config = {
        // 表示是否使用 cdn 加速域名，为布尔值，true 表示使用，默认为 false。
        useCdnDomain: true,
        // 根据具体提示修改上传地区,当为 null 或 undefined 时，自动分析上传域名区域
        region: qiniu.region.z2,
      }
      const observable = qiniu.upload(
        file,
        key,
        props.qiniuToken,
        putExtra,
        config,
      )

      observable.subscribe({
        next(response) {
          // 上传进度
          const progressEvt = {} as UploadProgressEvent

          progressEvt.percent = response.total.percent
          options.onProgress(progressEvt)
        },
        error: (errResult) => {
          emits('qiniuUploadError')
          ElMessage.error('网络错误，请稍后重试')
          reject(errResult)
        },
        complete: ({ key }) => {
          resolve(
            `${props.qiniuDomain.endsWith('/') ? props.qiniuDomain : `${props.qiniuDomain}/`}${key}`,
          )
        },
      })
    }
    catch (error) {
      reject(error)
    }
  })
}
function isImg(uploadFile: UploadFile | UploadUserFile) {
  return IMG_TYPES.includes(
    getFileType(uploadFile.raw ? uploadFile.raw.name : uploadFile.url),
  )
}
</script>

<template>
  <div class="hrt-upload-wrapper">
    <ElUpload
      ref="uploadRef"
      v-loading="loading"
      :file-list="fileList"
      class="hrt-upload"
      :multiple="props.multiple"
      :drag="props.drag"
      :disabled="props.disabled"
      :accept="`.${computedFileTypes.join(',.')}`"
      :limit="props.multiple ? props.limit : 1"
      :list-type="props.listType"
      :auto-upload="props.autoUpload"
      :show-file-list="props.showFileList"
      :http-request="httpRequest"
      :before-upload="beforeUpload"
      :on-change="onChange"
      :on-exceed="onExceed"
      :on-success="onSuccess"
      :on-error="onError"
      :on-preview="onPreview"
      :on-remove="onRemove"
    >
      <template v-if="$slots.default">
        <slot name="default" />
      </template>
      <ElIcon v-else-if="props.listType === 'picture-card'">
        <Plus />
      </ElIcon>
      <div v-else-if="props.drag" class="hrt-upload-drag">
        <ElIcon class="el-icon--upload">
          <UploadFilled />
        </ElIcon>
        <div class="el-upload__text">
          点击上传，或将文件拖动到此区域
        </div>
      </div>
      <ElButton v-else type="primary">
        点击上传
      </ElButton>
      <template v-if="props.tip" #tip>
        <div class="el-upload__tip">
          {{ props.tip }}
        </div>
      </template>
    </ElUpload>
    <HrtImgPreview v-model="imgVisible" :img-datas="imgFileList.map(item => item.url!)" :default-active-index="imgIndex" />
  </div>
</template>

<style lang="less">
.hrt-upload-wrapper {
  width: 100%;
  .hrt-upload {
    width: 100%;
    .el-upload-dragger {
      border-radius: 2px;
      padding: 0 11px;
      &.is-dragover {
        border-width: 1px;
      }
      .hrt-upload-drag {
        display: flex;
        align-items: center;
        min-height: 32px;
      }
      .el-icon--upload {
        color: var(--el-text-color-secondary);
        font-size: 20px;
        line-height: 1;
        margin-bottom: 0;
      }
      .el-upload__text {
        color: var(--el-text-color-secondary);
        text-align: left;
        margin-left: 6px;
      }
    }
    .el-upload__tip {
      line-height: 16px;
      margin-top: 5px;
      color: var(--el-color-warning);
    }
    .el-icon--close-tip {
      display: none !important;
    }
    .el-upload-list__item-status-label {
      display: none !important;
    }
    .el-upload-list {
      margin-top: 0;
    }
    .el-upload-list--text {
      .el-upload-list__item {
        line-height: 1.428572;
        margin: 10px 0 0 0;
        &.is-uploading .el-upload-list__item-name {
          padding-right: 37px;
        }
        .el-progress {
          position: relative;
          top: 0;
          margin-top: 2px !important;
        }
        .el-progress__text {
          min-width: auto;
        }
      }
    }
    .el-upload-list--picture,
    .el-upload-list--picture-card {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      .el-upload-list__item {
        width: 80px;
        height: 80px;
        padding: 0;
        margin: 10px 10px 0 0;
        overflow: visible;
        border: 1px solid #dcdfe6;
        border-radius: 2px;
      }
      .el-upload-list__item-thumbnail {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: inherit;
      }
      .el-icon--close {
        width: 16px;
        height: 16px;
        border-radius: 16px;
        background-color: var(--el-color-danger);
        top: -8px;
        right: -8px;
        font-size: 12px;
        color: #ffffff;
        opacity: 1;
        z-index: 9;
        transform: translateY(0);
      }
    }
    .el-upload-list--picture {
      .el-upload-list__item-info {
        position: absolute;
        width: 100%;
        height: 100%;
        z-index: 8;
        margin-left: 0;
        .el-upload-list__item-name {
          width: 100%;
          height: 100%;
          cursor: pointer;
          opacity: 0;
        }
        .el-progress {
          width: 90%;
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
          margin-top: 0 !important;
        }
      }
    }
    .el-upload-list--picture-card {
      margin-top: -10px;
      .el-upload--picture-card {
        --el-upload-picture-card-size: 80px;
        background-color: var(--hrt-color-neutral-700);
        border-radius: 2px;
        margin-top: 10px;
        .el-icon {
          font-size: 20px;
          color: var(--el-text-color-secondary);
        }
      }
      .el-upload-dragger {
        width: 100%;
        height: 100%;
        background-color: inherit;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border: none;
      }
      .el-upload-list__item {
        .el-progress {
          width: 70px;
          height: 70px;
        }
        .el-progress-circle {
          width: 100% !important;
          height: 100% !important;
        }
        .el-progress__text {
          font-size: 14px !important;
        }
        &:hover {
          .el-icon--close {
            display: inline-flex;
          }
        }
      }
      .el-upload-list__item-actions {
        opacity: 0;
        user-select: none;
        .el-upload-list__item-delete {
          display: none;
        }
        .el-upload-list__item-preview {
          display: inline-flex;
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}
</style>
