<script setup lang="ts">
import type { RadioProps } from 'element-plus'
import { ElRadio, radioGroupKey } from 'element-plus'
import { computed, inject, useAttrs } from 'vue'

defineOptions({
  name: 'HrtRadio',
})
const props = defineProps<HrtRadioProps>()
const emit = defineEmits<{
  'change': [value: string | number | boolean]
  'update:modelValue': [value: string | number | boolean]
}>()
const radioGroup = inject(radioGroupKey, undefined)
const isGroup = computed(() => !!radioGroup)
const curValue = computed(() => isGroup.value ? radioGroup!.modelValue : props.modelValue!)
export interface HrtRadioProps extends Partial<RadioProps> {
  /** 是否支持点击取消选中 */
  enableToggle?: boolean
}
const attrs = useAttrs()

function radioClickHandler() {
  const { enableToggle, value } = props
  if (enableToggle) {
    if (value === curValue.value) {
      setTimeout(() => {
        if (isGroup.value) {
          radioGroup!.changeEvent('')
        }
        else {
          emit('update:modelValue', '')
          emit('change', '')
        }
      })
    }
  }
}
</script>

<template>
  <ElRadio
    v-bind="{ ...props, ...attrs }"
    class="hrt-radio"
    @change="emit('change', $event as string | number | boolean)"
    @click="radioClickHandler"
  >
    <slot />
  </ElRadio>
</template>

<style lang="css">
.hrt-radio {
  margin-right: 32px;
  .el-radio__input.is-checked {
    .el-radio__inner {
      border-color: var(--hrt-color-blue);
      background-color: transparent !important;

      &:after {
        background-color: var(--hrt-color-blue);
        height: 8px;
        width: 8px;
      }
    }
    & + .el-radio__label {
      color: var(--hrt-color-neutral-200) !important;
    }
  }
}
</style>
