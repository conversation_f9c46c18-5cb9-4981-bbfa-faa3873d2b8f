<script lang="ts" setup>
import type { HrtSelectProps } from '../select/select'
import { ElTreeSelect } from 'element-plus'
import { computed, getCurrentInstance, nextTick, ref, watch } from 'vue'
import { getHighlightKeywords, getUuid } from '../../utils'
import { HrtOverflowTooltip } from '../overflow-tooltip'
import { DefaultSelectProps } from '../select/select'

defineOptions({
  name: 'HrtTreeSelect',
})

const props = withDefaults(defineProps<HrtSelectProps>(), DefaultSelectProps)

const emits = defineEmits<{
  visibleChange: [val: boolean]
  input: [evt: InputEvent]
}>()

const matchStr = ref('')
const uniqueClass = `hrt_${getUuid()}`
const popperVisible = ref(false)
const popClass = computed(() => `hrt-tree-select__dropdown ${uniqueClass} ${props.popperClass}`)

const vm = getCurrentInstance()

function changeRef(el: any) {
  vm!.exposed = el || {}
  vm!.exposeProxy = el || {}
}

async function setClassName() {
  await nextTick()
  const selectList: HTMLElement[] = Array.from(document.querySelectorAll(`.${uniqueClass} .el-select-dropdown__item.is-selected`));
  (Array.from(document.querySelectorAll(`.${uniqueClass} .el-tree-node__content`)) as HTMLElement[]).forEach((el) => {
    let flag = false
    for (let i = 0; i < selectList.length; i++) {
      if (el.contains(selectList[i])) {
        el.classList.add('is-selected')
        flag = true
        break
      }
    }
    if (!flag) {
      el.classList.remove('is-selected')
    }
  })
}

async function onVisibleChange(val: boolean) {
  popperVisible.value = val
  emits('visibleChange', val)
  if (!val) {
    matchStr.value = ''
    return
  }
  setClassName()
}

watch(() => props.modelValue, () => {
  if (!popperVisible.value)
    return
  setClassName()
})

function onInput(e: InputEvent) {
  if (props.filterable) {
    matchStr.value = (e.target as HTMLInputElement).value
  }
  emits('input', e)
}
</script>

<template>
  <ElTreeSelect
    class="hrt-tree-select"
    :class="[{ multiple: props.multiple }]"
    v-bind="{ ...$attrs, ...props, popperClass: popClass, ref: changeRef }"
    @visible-change="onVisibleChange"
    @input="onInput"
  >
    <template #default="scope">
      <slot v-bind="scope">
        <HrtOverflowTooltip render-slot-template>
          <span
            v-for="(item, i) in getHighlightKeywords(scope.data.label, matchStr)"
            :key="i"
            :class="{ 'hrt-text-highlight': i % 2 !== 0 }"
          >{{ item }}</span>
        </HrtOverflowTooltip>
      </slot>
    </template>

    <template v-for="(_, name) in $slots" :key="name" #[name]="scope">
      <slot :name="name" v-bind="scope || {}" />
    </template>
  </ElTreeSelect>
</template>

<style lang="less">
@import url('../select/mixin.less');

.hrt-tree-select {
  .hrt-select-mixin();
}

.hrt-tree-select__dropdown {
  .el-tree {
    --el-tree-node-content-height: 32px;
    --el-tree-node-hover-bg-color: var(--hrt-color-blue-100);
  }
  .el-tree-node__content {
    .el-select-dropdown__item:not(.is-disabled) {
      color: inherit;
      font-weight: normal;
    }
    &.is-selected {
      background-color: var(--hrt-color-neutral-700) !important;
    }
  }
  .el-select-dropdown__item::after {
    display: none;
  }
  .el-tree-node__expand-icon {
    color: var(--el-text-color-regular);
  }
}
</style>
