<script lang="ts" setup>
import type { PropType } from 'vue'
import type { HrtStepItemState } from '@/step/types'
import { stepProps } from 'element-plus'
import { computed, getCurrentInstance, provide, useSlots, watch } from 'vue'
import { useOrderedChildren } from '../../hooks/useOrderedChildren'
import { HRT_STEPS_INJECTION_KEY } from './injectKeys'

defineOptions({
  name: 'HrtSteps',
})

const props = defineProps({
  /** 卡片风格 */
  card: {
    type: Boolean,
    default: false,
  },
  /** 卡片模式下的步骤条是否展示序号 */
  cardSeq: {
    type: Boolean,
    default: false,
  },
  /** icon和文字排版是否为纵向居中排列 */
  alignCenter: {
    type: Boolean,
    default: false,
  },
  /** 步骤条排列方向 */
  direction: {
    type: String as PropType<'horizontal' | 'vertical'>,
    default: 'horizontal',
  },
  /** 子组件step的自定义图标 */
  icon: {
    ...stepProps.icon,
    default: '',
  },
})

const emits = defineEmits<{
  (e: 'change', val: number): void
}>()

const active = defineModel('active', { required: true, type: Number })

const vm = getCurrentInstance()
const slots = useSlots()
const iconSlot = computed(() => slots.icon)

const {
  children: steps,
  addChild: addStep,
  removeChild: removeStep,
  ChildrenSorter: StepsSorter,
} = useOrderedChildren<HrtStepItemState>(vm!, 'HrtStep')

function changeActive(index: number) {
  active.value = index
  emits('change', index)
}

provide(HRT_STEPS_INJECTION_KEY, { props, steps, iconSlot, addStep, removeStep, changeActive })

watch(steps, (arr) => {
  arr.forEach((instance: HrtStepItemState, index: number) => {
    instance.setIndex(index)
  })
})
</script>

<template>
  <div class="hrt-steps" :class="[{ 'is-vertical': !props.card && props.direction === 'vertical' }]">
    <slot />
    <div v-show="false">
      <slot name="icon" />
    </div>
    <StepsSorter />
  </div>
</template>

<style lang="less">
.hrt-steps {
  display: flex;
  &.is-vertical {
    flex-direction: column;
  }
}
</style>
