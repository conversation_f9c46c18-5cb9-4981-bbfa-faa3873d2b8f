<script lang="ts" setup>
import { ArrowDown } from '@element-plus/icons-vue'
import { HrtButton } from '@hrt/components'
import { dropdownProps, ElDropdown, ElIcon } from 'element-plus'
import { computed, getCurrentInstance, ref } from 'vue'

defineOptions({
  name: 'HrtDropdown',
})

const props = defineProps({
  ...dropdownProps,
  popperOptions: {
    ...dropdownProps.popperOptions,
    default: () => ({
      modifiers: [
        {
          name: 'offset',
          options: {
            offset: [0, 4],
          },
        },
      ],
    }),
  },
  isButton: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: '下拉菜单',
  },
})
const emits = defineEmits<{
  visibleChange: [val: boolean]
}>()

const vm = getCurrentInstance()
function changeRef(el: any) {
  vm!.exposed = el || {}
  vm!.exposeProxy = el || {}
}

const isOpen = ref(false)
const popClass = computed(() => `hrt-dropdown__popper ${props.popperClass}`)

function onVisibleChange(val: boolean) {
  emits('visibleChange', val)
  isOpen.value = val
}
</script>

<template>
  <ElDropdown
    class="hrt-dropdown"
    :class="[{ 'is-open': isOpen }, { 'is-split-button': props.splitButton }]"
    v-bind="{ ...$attrs, ...props, popperClass: popClass, ref: changeRef }"
    @visible-change="onVisibleChange"
  >
    <template #default>
      <slot>
        <div v-if="!props.splitButton" class="hrt-dropdown-trigger" style="outline: none;">
          <HrtButton v-if="props.isButton" :type="props.buttonProps?.type || undefined" class="hrt-dropdown-button">
            <span>{{ props.title }}</span>
            <ElIcon class="el-dropdown__icon el-icon--right">
              <ArrowDown />
            </ElIcon>
          </HrtButton>
          <span v-else class="el-dropdown-link">
            {{ props.title }}
            <ElIcon class="el-dropdown__icon el-icon--right">
              <ArrowDown />
            </ElIcon>
          </span>
        </div>
      </slot>
    </template>

    <template v-for="(_, name) in $slots" :key="name" #[name]="scope">
      <slot :name="name" v-bind="scope || {}" />
    </template>
  </ElDropdown>
</template>

<style lang="less">
@import url('../button/mixin.less');

.hrt-dropdown {
  .el-dropdown-link {
    &:hover {
      color: var(--hrt-color-blue-500);
    }
  }
  .el-dropdown__icon {
    transition: transform 0.2s;
  }
  &.is-open .el-dropdown__icon {
    transform: rotateX(180deg);
  }
  &.is-split-button {
    .el-button {
      .hrt-button-mixin();
    }
    .el-dropdown__caret-button {
      box-shadow: -1px 0 0px 0px transparent;
      &:hover {
        box-shadow: -1px 0 0px 0px var(--el-button-hover-border-color);
      }
      &:active {
        box-shadow: -1px 0 0px 0px var(--el-button-active-border-color);
      }
    }
  }
}

.hrt-dropdown__popper {
  &.el-dropdown__popper.el-popper {
    box-shadow: var(--hrt-shadow);
  }
  .el-popper__arrow {
    display: none;
  }
  .el-dropdown-menu {
    padding: 2px 0;
  }
  .el-dropdown-menu__item {
    padding: 0 12px;
    height: 32px;
    line-height: 32px;
    &:not(.is-disabled):hover,
    &:not(.is-disabled):focus {
      background-color: var(--hrt-color-blue-100);
      color: var(--el-text-color-regular);
    }
  }
}
</style>
