<script setup lang="ts">
import { computed } from 'vue'

export interface TextProps {
  /** 文本类型 */
  type?: 'success' | 'primary' | 'info' | 'warning' | 'danger' | 'default'
  size?: 'base' | 'xl' | 'sm'
  /** 文本行数 */
  lineClamp?: number
  /** 是否截断 */
  truncated?: boolean
}

defineOptions({
  name: 'HrtTitle',
})

const {
  type = 'default',
  size = 'base',
  lineClamp,
  truncated = false,
} = defineProps<TextProps>()

const tag = computed(() => {
  if (size === 'xl')
    return 'h1'
  if (size === 'base')
    return 'h2'
  if (size === 'sm')
    return 'h3'
  return 'h2'
})
</script>

<template>
  <component
    :is="tag"
    class="hrt-font-medium hrt-m-0 hrt-font-hrt"
    :class="{
      'hrt-text-neutral-100': type === 'default',
      'hrt-text-neutral-300': type === 'info',
      'hrt-text-blue': type === 'primary',
      'hrt-text-green': type === 'success',
      'hrt-text-orange': type === 'warning',
      'hrt-text-red': type === 'danger',
      'hrt-text-xl': size === 'xl',
      'hrt-text-base': size === 'base',
      'hrt-text-sm': size === 'sm',
      'is-line-clamp hrt-overflow-hidden': lineClamp,
      'hrt-inline-block hrt-text-ellipsis hrt-whitespace-nowrap hrt-overflow-hidden hrt-max-w-full': truncated,
    }"
    :style="{
      '-webkit-line-clamp': lineClamp,
    }"
  >
    <slot />
  </component>
</template>

<style lang="css" scoped>
.is-line-clamp {
  display: -webkit-inline-box;
  -webkit-box-orient: vertical;
}
</style>
