<script lang="ts" setup>
import { ElOptionGroup } from 'element-plus'
import { getCurrentInstance } from 'vue'

defineOptions({ name: 'HrtOptionGroup' })

const props = defineProps<{
  label?: string
  disabled?: boolean
}>()

const vm = getCurrentInstance()

function changeRef(el: any) {
  vm!.exposed = el || {}
  vm!.exposeProxy = el || {}
}
</script>

<template>
  <ElOptionGroup class="hrt-option-group" v-bind="{ ...$attrs, ...props, ref: changeRef }">
    <template v-for="(_, name) in $slots" :key="name" #[name]="scope">
      <slot :name="name" v-bind="scope || {}" />
    </template>
  </ElOptionGroup>
</template>

<style lang="less">
.hrt-option-group {
  .el-select-group__title {
    font-size: 14px;
    color: #15233f;
    font-weight: 600;
    padding: 0 12px;
    &::before {
      content: '';
      display: inline-block;
      width: 3px;
      height: 12px;
      background: #2e6be6;
      border-radius: 1px;
      margin-right: 8px;
      vertical-align: middle;
    }
  }
  .el-select-group .el-select-dropdown__item {
    padding-left: 12px;
  }
}
</style>
