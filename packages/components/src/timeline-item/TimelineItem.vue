<script lang="ts" setup>
import type { Component, CSSProperties } from 'vue'
import { ElTimelineItem } from 'element-plus'

export interface HrtTimelineItemProps {
  /** 时间戳 */
  timestamp?: string
  /** 是否隐藏时间戳 */
  hideTimestamp?: boolean
  /** 是否垂直居中 */
  center?: boolean
  /** 时间戳位置 */
  placement?: 'top' | 'bottom'
  /** 节点尺寸 */
  size?: 'normal' | 'large'
  /** 节点颜色 */
  color?: CSSProperties['color']
  /** 节点类型 */
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  /** 自定义图标 */
  icon?: string | Component
  /** 是否空心点 */
  hollow?: boolean
}

defineOptions({
  name: 'HrtTimelineItem',
})

defineProps<HrtTimelineItemProps>()
</script>

<template>
  <ElTimelineItem
    class="hrt-timeline-item"
    v-bind="$attrs"
    :timestamp="timestamp"
    :hide-timestamp="hideTimestamp"
    :center="center"
    :size="size"
    :placement="placement"
    :color="color"
    :type="type"
    :icon="icon"
    :hollow="hollow"
  >
    <template v-for="(_, key) in $slots" #[key]="slotProps" :key="key">
      <slot :name="key" v-bind="slotProps" />
    </template>
  </ElTimelineItem>
</template>

<style lang="css">
.hrt-timeline-item {
  .el-timeline-item__node {
    background-color: white;
    border-width: 2px;
    border-style: solid;
  }
  .el-timeline-item__node--info {
    border-color: var(--hrt-color-neutral-400);
  }
  .el-timeline-item__timestamp {
    font-size: 14px;
  }
}
</style>
