<script setup lang="ts">
import { ElSlider } from 'element-plus'

defineOptions({ name: 'HrtSlider' })
</script>

<template>
  <ElSlider
    v-bind="$attrs"
    class="hrt-slider"
  />
</template>

<style lang="less">
.hrt-slider.el-slider {
  --el-slider-height: 4px;
  --el-slider-border-radius: 2px;
  --el-slider-runway-bg-color: #e1e5ed;
  --el-slider-disabled-color: #b8becc;
  --el-slider-button-size: 14px;
  --el-slider-button-wrapper-size: 24px;
  --el-slider-button-wrapper-offset: -10px;
  .el-slider__runway {
    &:not(.is-disabled) {
      .el-slider__button-wrapper {
        border-radius: 50%;
        transition: background-color 0.2s;
        &.dragging,
        &.hover,
        &:hover {
          background-color: rgba(46, 107, 230, 0.2);
        }
      }
    }
    &.show-input {
      margin-right: 25px;
    }
  }
  .el-slider__button.dragging,
  .el-slider__button.hover,
  .el-slider__button:hover {
    transform: none;
  }
  .el-input-number__decrease,
  .el-input-number__increase {
    display: none;
  }
  .el-slider__input {
    width: auto;
    .el-input__wrapper {
      padding-left: 12px;
      padding-right: 12px;

      .el-input__inner {
        width: 38px;
        text-align: left;
      }
    }
    .el-input--large {
      .el-input__wrapper {
        padding-left: 16px;
        padding-right: 16px;

        .el-input__inner {
          width: 46px;
          text-align: left;
        }
      }
    }
    .el-input--small {
      .el-input__wrapper {
        padding-left: 8px;
        padding-right: 8px;

        .el-input__inner {
          width: 30px;
          text-align: left;
        }
      }
    }
  }
  .el-slider__marks-text {
    color: var(--el-text-color-primary);
    line-height: 20px;
    margin-top: 14px;
  }
}
</style>
