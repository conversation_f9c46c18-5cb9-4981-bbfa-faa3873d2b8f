<script lang="ts" setup>
import type { HrtSelectInjectState } from './inject<PERSON>eys'
import type { HrtSelectProps } from './select'
import { ElSelect } from 'element-plus'
import { computed, getCurrentInstance, provide, ref } from 'vue'
import { HrtSelectInjectKey } from './injectKeys'
import { DefaultSelectProps } from './select'

defineOptions({ name: 'HrtSelect' })

const props = withDefaults(defineProps<HrtSelectProps>(), DefaultSelectProps)

const emits = defineEmits<{
  (e: 'input', val: InputEvent): void
  (e: 'visibleChange', val: boolean): void
}>()

const vm = getCurrentInstance()
function changeRef(el: any) {
  vm!.exposed = el || {}
  vm!.exposeProxy = el || {}
}

const matchStr = ref('')
const popClass = computed(() => `hrt-select__dropdown ${props.popperClass}`)
function onInput(e: InputEvent) {
  if (props.filterable) {
    matchStr.value = (e.target as HTMLInputElement).value
  }
  emits('input', e)
}
function onVisibleChange(val: boolean) {
  if (!val) {
    matchStr.value = ''
  }
  emits('visibleChange', val)
}

const provideState: HrtSelectInjectState = computed(() => ({
  filterable: props.filterable,
  matchStr: matchStr.value,
}))
provide(HrtSelectInjectKey, provideState)
</script>

<template>
  <ElSelect
    class="hrt-select"
    :class="[{ multiple: props.multiple }]"
    v-bind="{ ...$attrs, ...props, popperClass: popClass, ref: changeRef }"
    @input="onInput"
    @visible-change="onVisibleChange"
  >
    <template v-for="(_, name) in $slots" :key="name" #[name]="scope">
      <slot :name="name" v-bind="scope || {}" />
    </template>
  </ElSelect>
</template>

<style lang="less">
@import url('./mixin.less');

.hrt-select {
  .hrt-select-mixin();
}

.hrt-select__dropdown {
  .hrt-select__dropdown-mixin();
}

.el-tooltip .el-select__selection {
  max-width: var(--hrt-tooltip-max-width);
  .el-tag {
    .hrt-select__tag-mixin();
  }
}
</style>
