@import url('../tag/mixin.less');

.hrt-select-mixin() {
  &.el-select {
    --el-border-color-hover: var(--el-color-primary);
    --hrt-select-padding: 4px 10px 4px 12px;
    --hrt-select-height: 32px;
    width: 100%;
    &.el-select--default {
      width: 240px;
    }
    &.el-select--large {
      width: 364px;
    }
    &.el-select--small {
      width: 116px;
    }
    .el-select__caret {
      font-size: 16px;
      color: var(--el-text-color-regular);
    }
    .is-disabled .el-select__caret {
      color: var(--hrt-color-neutral);
    }
    .el-select__placeholder {
      color: var(--el-text-color-regular);
      &.is-transparent {
        color: var(--hrt-color-neutral);
      }
    }
    .el-select__clear {
      color: #939cae;
      border-radius: 50%;

      &:hover {
        background: #eaecef;
      }
    }
    &.multiple {
      .el-select__wrapper {
        height: unset;
        padding-top: 3px;
        padding-bottom: 3px;
        max-height: 60px;
        overflow-y: auto;
        &::-webkit-scrollbar {
          width: 4px !important;
          height: 4px !important;
        }
        &::-webkit-scrollbar-thumb {
          background-color: var(--hrt-color-neutral);
          border-radius: 2px;
        }
      }
      .el-select__selection {
        gap: 4px;
        &.is-near {
          align-self: flex-start;
        }
      }
      .el-select__prefix,
      .el-select__suffix {
        position: sticky;
        top: 50%;
        transform: translateY(-4px);
      }
    }
  }
  &.el-select--large .el-select__wrapper,
  &.el-select--small .el-select__wrapper {
    height: var(--hrt-select-height);
    min-height: var(--hrt-select-height);
    padding: var(--hrt-select-padding);
  }
  .el-select__wrapper {
    padding: var(--hrt-select-padding);
    height: var(--hrt-select-height);
    min-height: var(--hrt-select-height);
    box-shadow: none !important;
    /** 必须使用border因为多选滚动时内部元素会遮挡box-shadow的线条 */
    border: 1px solid var(--el-border-color);
    &.is-hovering,
    &.is-focused {
      &:not(.is-disabled) {
        border-color: var(--el-border-color-hover);
      }
    }
    &.is-disabled {
      background-color: var(--hrt-color-neutral-700);
    }
  }
  .el-select__selection .el-tag {
    border-color: var(--el-tag-border-color);
  }
  .el-tag {
    .hrt-select__tag-mixin();
  }
}

.hrt-select__tag-mixin() {
  .hrt-tag-mixin();
  border-color: var(--el-tag-border-color);
  &.el-tag {
    padding-left: 6px;
    padding-right: 6px;
  }
}

.hrt-select__dropdown-mixin() {
  .el-select-dropdown__item {
    height: 32px;
    line-height: 32px;
    padding: 0 12px;
    &.is-hovering {
      background-color: var(--hrt-color-blue-100);
    }
    &.is-selected {
      background-color: var(--hrt-color-neutral-700);
      color: var(--el-color-text-regular);
      font-weight: normal;
    }
    &.is-disabled {
      color: var(--hrt-color-neutral);
    }
  }
  .el-select-dropdown__wrap {
    max-height: 330px;
  }
  &.el-select__popper.el-popper {
    box-shadow: var(--hrt-shadow);
  }
  &.el-select-dropdown.is-multiple .el-select-dropdown__item.is-selected::after {
    mask: unset;
    background: unset;
    content: '';
    width: 7px;
    height: 13px;
    border-radius: 2px;
    position: absolute;
    top: 50%;
    right: 16px;
    border: 2px solid var(--el-color-primary);
    border-top-color: transparent;
    border-left-color: transparent;
    transform: translateY(-65%) rotate(45deg);
  }
}
