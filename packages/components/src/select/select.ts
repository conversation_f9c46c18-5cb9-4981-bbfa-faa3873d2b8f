import type { SelectProps } from 'element-plus'
import { CaretBottom } from '@element-plus/icons-vue'

export const DefaultSelectProps = {
  suffixIcon: CaretBottom,
  placeholder: '请选择',
  offset: 4,
  tagEffect: 'light' as SelectProps['tagEffect'],
  showArrow: false,
  fitInputWidth: true,
  noMatchText: '暂无结果',
  popperClass: '',
}

export type HrtSelectProps = Pick<
SelectProps,
'suffixIcon'
| 'placeholder'
| 'offset'
| 'tagEffect'
| 'showArrow'
| 'fitInputWidth'
| 'noMatchText'
| 'filterable'
| 'multiple'
| 'modelValue'
| 'popperClass'
>
