<script setup lang="ts">
import type { BreadcrumbItemProps } from 'element-plus';
import { ElBreadcrumbItem } from 'element-plus';
import { inject } from 'vue';
import { hrtBreadcrumbKey } from './constant';

interface IProps extends Partial<BreadcrumbItemProps> {
  value: string;
}
defineOptions({
  name: 'HrtBreadcrumbItem',
});
const props = defineProps<IProps>();

const breadcrumbContext = inject(hrtBreadcrumbKey, undefined);

function clickHandler() {
  breadcrumbContext?.clickItemHandler?.(props.value);
}
</script>

<template>
  <ElBreadcrumbItem
    v-bind="props"
    class="hrt-breadcrumb-item"
    :class="{ active: breadcrumbContext?.active.value === props.value }"
    @click="() => clickHandler()"
  >
    <slot />
  </ElBreadcrumbItem>
</template>

<style lang="less">
.hrt-breadcrumb-item.el-breadcrumb__item,
.hrt-breadcrumb-item.el-breadcrumb__item&:last-child {
  .el-breadcrumb__inner {
    color: var(--hrt-color-neutral-400);
    &:hover {
      color: var(--hrt-color-blue-400);
    }
    &.is-link {
      color: var(--hrt-color-neutral-400);
      font-weight: normal;
    }
  }
  .el-breadcrumb__separator {
    color: var(--hrt-color-neutral-500);
  }
  &.active .el-breadcrumb__inner {
    font-weight: bold;
    color: var(--hrt-color-neutral-100);
  }
}
</style>
