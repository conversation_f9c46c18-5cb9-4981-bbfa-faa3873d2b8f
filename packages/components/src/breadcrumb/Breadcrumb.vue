<script setup lang="ts">
import type { BreadcrumbProps } from 'element-plus'
import { ElBreadcrumb, UPDATE_MODEL_EVENT } from 'element-plus'

import { provide } from 'vue'
import { hrtBreadcrumbKey } from './constant'

defineOptions({
  name: 'HrtBreadcrumb',
})

const props = defineProps<IProps>()
const emit = defineEmits<{
  'update:modelValue': [val: string]
  click: [key: string]
}>()

export interface IProps extends Partial<BreadcrumbProps> {}
const modelValue = defineModel<string>()

function clickItemHandler(val: string) {
  emit(UPDATE_MODEL_EVENT, val)
  emit('click', val)
}
provide(hrtBreadcrumbKey, { active: modelValue, clickItemHandler })
</script>

<template>
  <ElBreadcrumb
    v-bind="props"
    class="hrt-breadcrumb"
  >
    <slot />
  </ElBreadcrumb>
</template>

