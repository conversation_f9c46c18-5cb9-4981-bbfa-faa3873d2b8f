<script setup lang="ts">
import type { CheckboxProps } from 'element-plus'
import { ElCheckboxButton } from 'element-plus'

export interface HrtCheckboxButtonProps extends Partial<Omit<CheckboxProps, 'modelValue'>> {
  /**
   * 是否显示边框
   */
  outline?: boolean
}

defineOptions({
  name: 'HrtCheckboxButton',
})

const { outline = true, ...props } = defineProps<HrtCheckboxButtonProps>()
const emit = defineEmits<{
  change: [value: string | number | boolean]
  iconClick: []
}>()
const modelValue = defineModel<string | number | boolean>()

function handleIconClick() {
  emit('iconClick')
}
</script>

<template>
  <ElCheckboxButton
    v-model="modelValue"
    class="hrt-checkbox-button" :class="[{ 'is-outline': outline }]"
    v-bind="props"
    @change="emit('change', $event)"
  >
    <div class="hrt-flex">
      <template v-if="$slots.default">
        <div :class="[$slots.icon ? 'hrt-px-15 hrt-py-8' : '']">
          <slot />
        </div>
      </template>
      <template v-else>
        <div :class="[$slots.icon ? 'hrt-px-[15px] hrt-py-2' : '']">
          {{ props.label }}
        </div>
      </template>
      <div v-if="$slots.icon" class="hrt-checkbox-icon hrt-border-l hrt-px-2.5" @click.stop.prevent="handleIconClick">
        <div class="hrt-h-full hrt-flex hrt-items-center">
          <slot name="icon" />
        </div>
      </div>
    </div>
  </ElCheckboxButton>
</template>

<style lang="css">
.hrt-checkbox-button {
  .el-checkbox-button__inner {
    border-radius: 2px;
  }
  .hrt-checkbox-icon {
    border-color: var(--hrt-color-neutral-500);
  }

  &.is-checked {
    .el-checkbox-button__inner {
      border-left-color: var(--hrt-color-blue) !important;
    }
    &.is-outline {
      .el-checkbox-button__inner {
        background-color: var(--hrt-color-blue-100);
        color: var(--hrt-color-blue);
      }
    }
    &.is-disabled {
      .el-checkbox-button__inner {
        background-color: var(--hrt-color-neutral-500) !important;
        color: white !important;
      }
      .hrt-checkbox-icon {
        border-color: var(--hrt-color-neutral-700);
      }
    }
    .hrt-checkbox-icon {
      border-color: var(--hrt-color-blue);
    }
  }
  &.is-focus {
    .el-checkbox-button__inner {
      border-color: var(--hrt-color-blue) !important;
    }
  }

  &.is-disabled {
    .el-checkbox-button__inner {
      background-color: var(--hrt-color-neutral-700) !important;
      color: var(--hrt-color-neutral-400) !important;
    }
    .hrt-checkbox-icon {
      border-color: var(--hrt-color-neutral-500);
    }
  }

  .el-checkbox-button__inner:has(.hrt-checkbox-icon) {
    padding: 0;
  }

  + .hrt-checkbox-button {
    margin-left: 16px;
    .el-checkbox-button__inner {
      border-left-color: var(--hrt-color-neutral-500) !important;
    }
    &.is-checked {
      .el-checkbox-button__inner {
        border-left-color: var(--hrt-color-blue) !important;
      }
      &.is-disabled {
        .el-checkbox-button__inner {
          border-left-color: var(--hrt-color-neutral-500) !important;
        }
      }
    }
    &.is-focus {
      .el-checkbox-button__inner {
        border-color: var(--hrt-color-blue) !important;
      }
    }
  }
}
</style>
