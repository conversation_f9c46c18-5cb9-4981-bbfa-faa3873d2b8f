import type { MessageOptions, MessageParams, messageType } from 'element-plus'
import type { App } from 'vue'
import { ElMessage } from 'element-plus'

interface HrtMessageType {
  (options: MessageParams): ReturnType<typeof ElMessage>
  primary: (message: string, options?: MessageOptions) => ReturnType<typeof ElMessage>
  success: (message: string, options?: MessageOptions) => ReturnType<typeof ElMessage>
  warning: (message: string, options?: MessageOptions) => ReturnType<typeof ElMessage>
  error: (message: string, options?: MessageOptions) => ReturnType<typeof ElMessage>
  info: (message: string, options?: MessageOptions) => ReturnType<typeof ElMessage>
  closeAll: () => void
  config: (config: MessageOptions) => void
  install?: (app: App) => void
}

function createMessageWrapper(original: typeof ElMessage) {
  // 默认配置
  let defaultConfig: MessageOptions = {
    duration: 1500,
    plain: true,
  }
  const parseMessageParams = (args: any[]): MessageOptions => {
    // 处理字符串参数（直接消息）
    if (typeof args[0] === 'string') {
      return { ...defaultConfig, message: args[0] }
    }

    // 处理对象参数（完整配置）
    if (typeof args[0] === 'object' && args[0] !== null) {
      return { ...defaultConfig, ...args[0] }
    }

    // 处理无效参数
    console.warn('Invalid message parameters provided:', args)
    return { ...defaultConfig }
  }
  const messageWrapper = (...args: any[]) => {
    const options = parseMessageParams(args)
    return original(options)
  };

  // 添加快捷方法
  (['primary', 'success', 'warning', 'error', 'info'] as messageType[]).forEach((type) => {
    (messageWrapper as HrtMessageType)[type] = (message: string, options: MessageOptions = {}) => {
      // 使用 spread 运算符而不是 Object.assign，以获得更好的类型安全
      return original[type]({
        ...defaultConfig,
        ...options,
        message,
      })
    }
  })
  // 保留 closeAll 方法
  messageWrapper.closeAll = () => original.closeAll()
  // 拓展一个config方法用于配置默认配置
  messageWrapper.config = (config: MessageOptions) => {
    defaultConfig = { ...defaultConfig, ...config }
  }

  return messageWrapper
}

const HrtMessage = createMessageWrapper(ElMessage) as HrtMessageType

export default HrtMessage
