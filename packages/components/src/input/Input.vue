<script setup lang="ts">
import { ElInput } from 'element-plus'
import { useAttrs } from 'vue'

interface IProps {
  size?: 'mini' | 'small' | 'middle' | 'large' | 'super'
}
defineOptions({
  name: 'HrtInput',
  inheritAttrs: false,
})

withDefaults(defineProps<IProps>(), {
  size: 'large',
})

const sizeWidth = {
  mini: 80,
  small: 120,
  middle: 160,
  large: 240,
  super: 400,
}

const attrs = useAttrs()
</script>

<template>
  <ElInput
    v-bind="attrs"
    class="hrt-input"
    :style="{ width: `${sizeWidth[size]}px` }"
  >
    <template v-for="(_, key) in $slots" #[key]="slotProps" :key="key">
      <slot :name="key" v-bind="slotProps" />
    </template>
  </ElInput>
</template>

<style scoped lang="less">
.hrt-input {
  :deep(.el-input__wrapper) {
    border-radius: 2px;
    &:hover {
      box-shadow: 0 0 0 1px var(--el-color-primary) inset;
      caret-color: var(--el-color-primary);
    }
  }
}
</style>
