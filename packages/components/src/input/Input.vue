<script setup lang="ts">
import { ElInput } from 'element-plus'
import { getCurrentInstance, useAttrs } from 'vue'

export interface HrtInputProps {
  /** 输入框尺寸 */
  size?: 'mini' | 'small' | 'middle' | 'large' | 'super'
  /** 是否展示后缀背景 */
  showSuffixBg?: boolean
}

defineOptions({
  name: 'HrtInput',
})

const { size, showSuffixBg = false, ...restProps } = defineProps<HrtInputProps>()
const emit = defineEmits<{
  blur: [event: FocusEvent]
  focus: [event: FocusEvent]
  change: [value: number | string]
  input: [value: number | string]
  clear: []
}>()

const sizeWidth = {
  mini: 116,
  small: 240,
  middle: 364,
  large: 488,
  super: 612,
}

const attrs = useAttrs()
const vm = getCurrentInstance()!

function changeRef(el: any) {
  vm.exposed = el || {}
  vm.exposeProxy = el || {}
}
</script>

<template>
  <ElInput
    v-bind="{ ...restProps, ...attrs, ref: changeRef }"
    class="hrt-input"
    :class="{ 'hrt-input-suffix-bg': showSuffixBg }"
    :style="{ width: size ? `${sizeWidth[size]}px` : '100%' }"
    @blur="emit('blur', $event)"
    @clear="emit('clear')"
    @change="emit('change', $event)"
    @input="emit('input', $event)"
    @focus="emit('focus', $event)"
  >
    <template v-for="(_, key) in $slots" #[key]="slotProps" :key="key">
      <slot :name="key" v-bind="slotProps" />
    </template>
  </ElInput>
</template>

<style lang="css">
.el-input__wrapper:has(.el-input__prefix) {
  padding-left: 1px;
  .el-input__inner {
    padding-left: 12px;
  }
}

.hrt-input-suffix-bg {
  .el-input__wrapper:has(.el-input__suffix) {
    padding-right: 1px;
    .el-input__inner {
      padding-right: 12px;
    }
    .el-input__icon {
      margin-left: 0;
    }
  }

  .el-input__suffix {
    background-color: var(--el-fill-color-light);
    padding-left: 12px;
    padding-right: 12px;
    border-left: 1px solid var(--el-border-color);
  }
  .el-input__suffix-inner > :first-child {
    margin-left: 0;
  }
}

.hrt-input {
  .el-input__prefix {
    background-color: var(--el-fill-color-light);
    padding-left: 12px;
    padding-right: 12px;
    border-right: 1px solid var(--el-border-color);
  }

  .el-input__prefix-inner > :last-child {
    margin-right: 0 !important;
  }
}
</style>
