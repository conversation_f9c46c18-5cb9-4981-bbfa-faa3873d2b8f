<script lang="ts" setup>
import type { CheckTagProps } from 'element-plus'
import { ElCheckTag } from 'element-plus'
import { getCurrentInstance } from 'vue'

defineOptions({ name: 'HrtCheckTag' })

const props = defineProps<HrtCheckTagProps>()

type HrtCheckTagProps = Partial<CheckTagProps> & {}

const vm = getCurrentInstance()
function changeRef(el: any) {
  vm!.exposed = el || {}
  vm!.exposeProxy = el || {}
}
</script>

<template>
  <ElCheckTag class="hrt-check-tag" v-bind="{ ...$attrs, ...props, ref: changeRef }">
    <template v-for="(_, name) in $slots" :key="name" #[name]="scope">
      <slot :name="name" v-bind="scope || {}" />
    </template>
  </ElCheckTag>
</template>

<style lang="less">
.hrt-check-tag.el-check-tag {
  padding: 6px 10px;
  border-radius: 2px;
  color: var(--hrt-color-neutral-300);
  &.el-check-tag--primary.is-disabled {
    background: var(--hrt-color-neutral-700);
  }

  &.el-check-tag--primary.is-disabled,
  &.el-check-tag--success.is-disabled,
  &.el-check-tag--warning.is-disabled,
  &.el-check-tag--danger.is-disabled,
  &.el-check-tag--info.is-disabled {
    color: var(--hrt-color-neutral);
  }

  &.el-check-tag--primary.is-checked {
    background: var(--hrt-color-blue-100);
  }
  &.el-check-tag--success.is-checked {
    background: #e3f5e1;
  }
  &.el-check-tag--warning.is-checked {
    background: #fcede3;
  }
  &.el-check-tag--danger.is-checked {
    background: #ffe6e7;
  }
}
</style>
