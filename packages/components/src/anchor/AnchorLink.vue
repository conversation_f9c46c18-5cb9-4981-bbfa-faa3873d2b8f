<script setup lang="ts">
import type { AnchorLinkProps } from 'element-plus/lib/components/anchor/src/anchor-link'
import { ElAnchorLink } from 'element-plus'
import { anchorKey } from 'element-plus/es/components/anchor/src/constants'
import { inject } from 'vue'

defineOptions({
  name: 'HrtAnchorLink',
  inheritAttrs: false,
})
const props = defineProps<Partial<AnchorLinkProps>>()
const { direction } = inject<any>(anchorKey)!
</script>

<template>
  <ElAnchorLink v-bind="props" class="hrt-anchor-link" :class="direction">
    <slot />
  </ElAnchorLink>
</template>

<style lang="less">
.hrt-anchor-link {
  &.el-anchor__item {
    color: var(--hrt-color-neutral-400);
    overflow: unset;
    &:hover {
      color: var(--hrt-color-blue-400);
    }
    .el-anchor__link.is-active {
      color: var(--hrt-color-neutral-100);
      font-weight: bold;
    }
    &.horizontal {
      position: relative;

      &:not(:last-child)::after {
        content: '';
        position: absolute;
        right: -8px;
        top: 8px;
        height: 12px;
        width: 1px;
        background-color: var(--hrt-color-neutral-400);
      }
    }
    &.vertical {
      position: relative;
      padding-left: 16px;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        height: 6px;
        width: 6px;
        border-radius: 50%;
        background-color: var(--hrt-color-neutral-400);
        margin-right: 8px;
      }
      &:hover {
        &::before {
          background-color: var(--hrt-color-blue-400);
        }
      }
      &:has(.is-active) {
        &::before {
          background-color: var(--hrt-color-neutral-100);
        }
      }
    }
  }
}
</style>
