<script setup lang="ts">
import type { TableColumnCtx } from 'element-plus'
import { ElDropdown, ElDropdownItem, ElDropdownMenu, ElTableColumn } from 'element-plus'
import { h, useSlots } from 'vue'

defineOptions({
  name: 'HrtTableColumn',
})

const props = defineProps<IExtraProps & Partial<Omit<TableColumnCtx<any>, 'children'>>>()
interface IExtraProps {
  /** 操作按钮是否弹性折叠 */
  actionFlex?: boolean
  /** 固定操作按钮数量 */
  actionFixedNumber?: number
  /** 下拉展开文案 */
  axtionMoreText?: string
}

const slots = useSlots()

function renderDefaultSlot(scope: any) {
  if (!slots.default || !props.actionFlex) {
    return slots.default?.(scope)
  }
  const children = slots.default(scope)
  const splitNumber = props.actionFixedNumber ?? 0
  if (!children || children.length <= splitNumber) {
    return children
  }

  const fixedButtons = children.slice(0, splitNumber)
  const dropdownButtons = children.slice(splitNumber)

  const dropdown = h(ElDropdown, {
    trigger: 'click',
    size: 'small',
  }, {
    default: () => h('span', { class: 'el-dropdown-link' }, props.axtionMoreText ?? '···'),
    dropdown: () => h(ElDropdownMenu, {}, {
      default: () => dropdownButtons.map((btn, index) => {
        return h(ElDropdownItem, { key: index }, { default: () => btn })
      }),
    }),
  })
  return [...fixedButtons, dropdown]
}
</script>

<template>
  <ElTableColumn v-if="$slots.default" v-bind="props" v-on="$attrs">
    <template v-for="(_, name) in $slots" #[name]="slotData" :key="name">
      <template v-if="name !== 'default'">
        <slot :name="name" v-bind="slotData" />
      </template>
      <template v-else>
        <slot v-if="!actionFlex" v-bind="slotData" />
        <template v-else>
          <div class="custom-action">
            <template v-for="(item, _index) in renderDefaultSlot(slotData)" :key="_index">
              <component :is="item" />
            </template>
          </div>
        </template>
      </template>
    </template>
  </ElTableColumn>
  <ElTableColumn v-else v-bind="props" v-on="$attrs" />
</template>

<style lang="less">
.custom-action {
  display: flex;
  align-items: center;
  > .el-button.is-text {
    position: relative;
    margin: 0;
    &:not(:last-of-type):after {
      content: '/';
      font-size: 0;
      position: absolute;
      top: 4px;
      right: 0;
      height: 14px;
      width: 1px;
      background: var(--hrt-color-neutral-600);
    }
    & + .el-dropdown {
      position: relative;
      &:after {
        content: '/';
        font-size: 0;
        position: absolute;
        top: 0px;
        left: -10px;
        height: 14px;
        width: 1px;
        background: var(--hrt-color-neutral-600);
      }
    }
  }
}
.el-dropdown {
  margin-left: 8px;
}
.el-dropdown-link {
  cursor: pointer;
  color: var(--el-color-primary);
  display: flex;
  align-items: center;
}

.el-dropdown-menu {
  --el-dropdown-menuItem-hover-fill: var(--el-color-primary-light-9);
  --el-dropdown-menuItem-hover-color: var(--el-color-primary);
}
</style>
