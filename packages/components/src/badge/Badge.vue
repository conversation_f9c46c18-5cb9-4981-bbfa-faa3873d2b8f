<script setup lang="ts">
import type { CSSProperties } from 'vue'
import { computed, toRefs } from 'vue'

export interface HrtBadgeProps {
  value?: string | number
  max?: number
  isDot?: boolean
  hidden?: boolean
  color?: CSSProperties['color']
  offset?: [number, number] | number
  /** 是否绝对定位 */
  isAbsolute?: boolean
}

defineOptions({
  name: 'HrtBadge',
})

const props = withDefaults(defineProps<HrtBadgeProps>(), {
  isAbsolute: true,
  isDot: false,
})
const { offset } = toRefs(props)

const content = computed(() => {
  if (props.isDot)
    return ''
  if (typeof props.value === 'number' && typeof props.max === 'number') {
    return props.value > props.max ? `${props.max}+` : props.value
  }
  return props.value
})

const badgeOffset = computed(() => {
  if (typeof offset.value === 'number') {
    return [offset.value, offset.value]
  }
  else if (Array.isArray(offset.value)) {
    return offset.value
  }
  return [0, 0]
})
</script>

<template>
  <div
    class="hrt-relative hrt-inline-block"
    :class="{
      'hrt-flex hrt-gap-1 hrt-items-center': !props.isAbsolute,
    }"
  >
    <slot />
    <sup
      v-if="!props.hidden && (props.isDot || props.value !== undefined)"
      class="hrt-badge"
      :class="[
        { 'hrt-badge--dot': props.isDot },
        { 'is-absolute': props.isAbsolute },
      ]"
      :style="{
        backgroundColor: color,
        top: `${badgeOffset[0]}px`,
        right: `${badgeOffset[1]}px`,
      }"
    >
      <template v-if="!props.isDot">{{ content }}</template>
    </sup>
  </div>
</template>

<style lang="css">
.hrt-inline-block {
  display: inline-block;
}

.hrt-gap-1 {
  gap: 4px;
}

.hrt-badge {
  top: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 16px;
  padding: 0 3px;
  color: #fff;
  font-size: 12px;
  font-weight: 500;
  background: #f56c6c;
  border-radius: 10px;
  box-sizing: border-box;
  border: 1px solid white;
  z-index: 1;
  &.is-absolute {
    transform: translate(50%, -50%);
    position: absolute;
  }
}
.hrt-badge--dot {
  width: 12px;
  height: 12px;
  min-width: 0;
  padding: 0;
  border-radius: 50%;
  background: #f56c6c;
}
</style>
