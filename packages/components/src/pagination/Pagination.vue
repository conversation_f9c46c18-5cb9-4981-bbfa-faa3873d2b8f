<script setup lang="ts">
import { ElConfigProvider, ElInputNumber, ElPagination } from 'element-plus'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import { computed, ref, useAttrs, watch } from 'vue'

defineOptions({
  name: 'HrtPagination',
})

const props = withDefaults(defineProps<HrtPaginationProps>(), {
  /** 每页显示个数选择器的选项设置 */
  pageSizes: () => [10, 25, 50, 100],
  /** 组件布局，子组件名用逗号分隔 */
  layout: 'total, sizes, prev, pager, next, jumper',
  /** 是否为分页按钮添加背景色 */
  background: true,
  /** 是否为迷你版 */
  mini: false,
})

const emit = defineEmits<{
  (e: 'sizeChange', value: number): void
  (e: 'currentChange', value: number): void
  (e: 'change', currentPage: number, pageSize: number): void
  (e: 'prevClick', value: number): void
  (e: 'nextClick', value: number): void
}>()

export interface HrtPaginationProps {
  pageSizes?: number[]
  layout?: string
  background?: boolean
  mini?: boolean
}

const attrs = useAttrs()
const currentPage = ref<number>(1)

const pageCount = computed(() => {
  if (attrs.pageCount) {
    return attrs.pageCount as number
  }
  if (attrs.total) {
    return Math.ceil(attrs.total as number / props.pageSizes[0])
  }
  return 1
})

watch(currentPage, (val) => {
  if (val > pageCount.value) {
    currentPage.value = pageCount.value
  }
}, { immediate: true })
</script>

<template>
  <div v-if="mini" class="hrt-flex hrt-items-center">
    <ElPagination
      v-bind="$attrs"
      v-model:current-page="currentPage"
      layout="prev"
      :page-sizes="pageSizes"
      class="hrt-pagination"
      @size-change="emit('sizeChange', $event)"
      @current-change="emit('currentChange', $event)"
      @change="(currentPage, pageSize) => emit('change', currentPage, pageSize)"
      @next-click="emit('nextClick', $event)"
      @prev-click="emit('prevClick', $event)"
    />
    <ElInputNumber
      v-model="currentPage"
      class="hrt-w-12"
      :controls="false"
      :max="pageCount"
      :min="1"
      :step="1"
    /> &nbsp;&nbsp;/&nbsp;&nbsp;{{ pageCount }}
    <ElPagination
      v-bind="$attrs"
      v-model:current-page="currentPage"
      layout="next"
      :page-sizes="pageSizes"
      class="hrt-pagination"
    />
  </div>
  <ElConfigProvider
    v-else
    :locale="{
      ...zhCn,
      el: {
        pagination: {
          goto: '跳至',
          pagesize: '条/页',
          total: '共 {total} 条',
          pageClassifier: '页',
        },
      },
    }"
  >
    <ElPagination
      v-bind="$attrs"
      :page-sizes="pageSizes"
      :layout="layout"
      :background="background"
      class="hrt-pagination"
      @size-change="emit('sizeChange', $event)"
      @current-change="emit('currentChange', $event)"
      @change="(currentPage, pageSize) => emit('change', currentPage, pageSize)"
      @next-click="emit('nextClick', $event)"
      @prev-click="emit('prevClick', $event)"
    >
      <slot />
    </ElPagination>
  </ElConfigProvider>
</template>

<style lang="css">
.hrt-pagination.el-pagination {
  .el-select {
    width: 110px;
  }

  .btn-prev,
  .btn-next {
    .el-icon {
      font-size: 16px;
    }
  }

  .el-pager li {
    &.is-active {
      color: white;
      background-color: var(--hrt-color-blue);
    }
  }

  &.is-background {
    .btn-prev,
    .btn-next {
      background-color: transparent !important;
      border: 1px solid var(--hrt-color-neutral-600);
      &:disabled {
        background-color: transparent !important;
      }
      .el-icon {
        font-size: 16px;
      }
    }
    .el-pager li {
      background-color: white !important;
      border: 1px solid var(--hrt-color-neutral-500);
      border-radius: 2px;
      box-sizing: border-box;
      font-weight: 500;
      &.is-active {
        border-color: var(--hrt-color-blue) !important;
        background-color: var(--hrt-color-blue) !important;
      }
    }
  }
}
</style>
