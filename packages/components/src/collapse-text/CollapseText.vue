<script setup lang="ts">
import { nextTick, onMounted, ref, toRefs, useId } from 'vue'

interface HrtCollapseTextProps {
  /** 展开文本 */
  expandText?: string
  /** 折叠文本 */
  collapseText?: string
  /** 默认显示行数 */
  lineClamp?: number
  text: string
  maxLine?: number
}

defineOptions({
  name: 'HrtCollapseText',
})
const props = withDefaults(defineProps<HrtCollapseTextProps>(), {
  expandText: '展开',
  collapseText: '收起',
  maxLine: 1,
})

const { expandText, collapseText, maxLine, text } = toRefs(props)

const expanded = ref(false)
const showToggle = ref(true)
const textRef = ref<HTMLElement | null>(null)
const descBox = ref<HTMLElement | null>(null)
/** 是否展开 */
const isExpanded = ref(false)
const inputId = useId()
const shortText = ref('')

function toggle() {
  expanded.value = !expanded.value
  if (expanded.value) {
    textRef.value!.textContent = text.value
  }
  else {
    textRef.value!.textContent = shortText.value
  }
}

function isOverflowOneLine() {
  const el = descBox.value
  if (!el)
    return false
  // 判断是否超出一行
  return el.clientHeight > maxLine.value * 20
}

function findMaxFitText(text: string, el: HTMLElement) {
  let left = 0
  let right = text.length
  let result = ''
  /** 二分法查找文案，找到使得文案不超出一行的最大长度 */
  while (left <= right) {
    const mid = Math.floor((left + right) / 2)
    el.textContent = text.slice(0, mid)
    if (isOverflowOneLine()) {
      right = mid - 1
    }
    else {
      result = text.slice(0, mid)
      left = mid + 1
    }
  }
  return result
}

onMounted(() => {
  nextTick(() => {
    if (isOverflowOneLine()) {
      const fitText = findMaxFitText(text.value, textRef.value as HTMLElement)
      textRef.value!.textContent = fitText
      shortText.value = fitText
    }
    else {
      showToggle.value = false
    }
  })
})
</script>

<template>
  <div class="hrt-collapse-text">
    <input
      :id="inputId"
      v-model="isExpanded"
      type="checkbox"
      class="toggleInput"
      @change="toggle"
    >
    <div ref="descBox">
      <span ref="textRef" class="hrt-collapse-text__content">
        {{ text }}
      </span>
      <label
        v-if="showToggle"
        class="hrt-collapse-text__action"
        :for="inputId"
      >{{ isExpanded ? collapseText : expandText }}</label>
    </div>
  </div>
</template>

<style lang="css">
.hrt-collapse-text {
  display: flex;
  line-height: 20px;
  color: var(--hrt-color-neutral-200);
  font-size: 14px;

  .hrt-collapse-text__action {
    color: var(--hrt-color-blue);
    cursor: pointer;
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    margin-left: 12px;
    white-space: nowrap;
  }

  .toggleInput {
    display: none;
  }
}
</style>
