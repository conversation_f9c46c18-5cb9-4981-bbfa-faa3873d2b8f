import type { PropType } from 'vue'
import type { HrtChatToolType } from './type'

export const chatBoxProps = {
  title: {
    type: String,
    default: '聊天',
  },
  /** 双击内容区收起聊天框 */
  collapseOnDblclickContent: {
    type: Boolean,
    default: true,
  },
  /** 工具列表 */
  tools: {
    type: Array as PropType<HrtChatToolType[]>,
    default: () => ['image', 'video', 'emoji', 'history', 'medication', 'expression', 'answer'],
  },
}
