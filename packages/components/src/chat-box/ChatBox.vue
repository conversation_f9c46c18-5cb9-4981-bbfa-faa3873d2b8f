<script lang="ts" setup>
import type { HrtChatBoxFile } from './type'
import { HrtChatInput } from '@hrt/components'
import { ref } from 'vue'
import { chatBoxProps } from './chatBox'
import ChatBoxTools from './ChatBoxTools.vue'

defineOptions({
  name: 'HrtChatBox',
  inheritAttrs: false,
})

const props = defineProps(chatBoxProps)

const emits = defineEmits<{
  send: [val: string]
  fileChange: [val: HrtChatBoxFile]
  emojiChange: [val: string]
  chatHistoryClick: [e: MouseEvent]
  medicationClick: [e: MouseEvent]
  commonExpressionsClick: [e: MouseEvent]
  questionAnswerClick: [e: MouseEvent]
}>()

const modelValue = defineModel('modelValue', { type: String, default: '' })

const isCollapse = ref(true)
const inputRef = ref<InstanceType<typeof HrtChatInput>>()

function onDblclick() {
  if (!props.collapseOnDblclickContent)
    return
  isCollapse.value = true
}

async function onInputClick() {
  isCollapse.value = false
}
function onSend() {
  emits('send', modelValue.value)
  modelValue.value = ''
}

defineExpose({
  // textareaRef,
  get editor() {
    return inputRef.value?.editor
  },
})
</script>

<template>
  <div
    class="hrt-chat-box" :class="[
      {
        'hrt-chat-box__collapsed': isCollapse,
      },
    ]"
  >
    <div class="hrt-chat-box__title">
      <slot name="title">
        {{ props.title }}
      </slot>
    </div>
    <div class="hrt-chat-box__content" @dblclick="onDblclick">
      <slot />
    </div>
    <ChatBoxTools
      :tools="props.tools"
      :show-input="isCollapse"
      @input-click="onInputClick"
      @file-change="emits('fileChange', $event)"
      @emoji-change="emits('emojiChange', $event)"
      @chat-history-click="emits('chatHistoryClick', $event)"
      @medication-click="emits('medicationClick', $event)"
      @common-expressions-click="emits('commonExpressionsClick', $event)"
      @question-answer-click="emits('questionAnswerClick', $event)"
    >
      <slot name="tools" />
    </ChatBoxTools>
    <div v-if="!isCollapse" class="hrt-chat-box__input-wrapper">
      <!-- <HrtInput
        ref="textareaRef"
        v-model="modelValue"
        class="hrt-chat-box__input"
        type="textarea"
        :rows="3"
        resize="none"
        placeholder="请输入内容"
        v-bind="$attrs"
        @keydown="onKeydown"
      />
      <div class="hrt-chat-box__input-footer">
        <div class="hrt-chat-box__btn-wrapper">
          <span class="hrt-chat-box__enter-tip">Enter发送，Shift + Enter换行</span>
          <HrtButton type="primary" @click="onSend">
            发送
          </HrtButton>
        </div>
      </div> -->
      <slot name="input">
        <HrtChatInput ref="inputRef" v-model="modelValue" auto-focus v-bind="$attrs" @send="onSend" />
      </slot>
    </div>
  </div>
</template>

<style lang="less">
.hrt-chat-box {
  width: 100%;
  height: 520px;
  background: #ffffff;
  border-radius: 6px;
  border: 1px solid var(--hrt-color-neutral-500);
  padding: 0 16px 16px;
  display: flex;
  flex-direction: column;
}
.hrt-chat-box__collapsed {
  .hrt-chat-box__content,
  .hrt-chat-box__tools-wrapper {
    border-color: #dcdee0;
  }
}
.hrt-chat-box__title {
  height: 54px;
  line-height: 54px;
  font-weight: bold;
  font-size: 16px;
  color: #101b25;
}
.hrt-chat-box__content {
  flex: 1 1 0;
  background: var(--hrt-color-neutral-700);
  border-radius: 4px 4px 0 0;
  border: 1px solid transparent;
  border-bottom: 0;
}
.hrt-chat-box__input-wrapper {
  background: var(--hrt-color-neutral-700);
  border-radius: 4px;
  height: 120px;
  .hrt-editor {
    box-shadow: none !important;
  }
}
.hrt-chat-box__input {
  .el-textarea__inner {
    background: transparent !important;
    box-shadow: none !important;
  }
}
.hrt-chat-box__input-footer {
  display: flex;
  padding: 0 16px 16px;
}
.hrt-chat-box__btn-wrapper {
  flex: 1;
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
  column-gap: 8px;
}
.hrt-chat-box__enter-tip {
  font-size: 12px;
  color: var(--el-color-info);
}
</style>
