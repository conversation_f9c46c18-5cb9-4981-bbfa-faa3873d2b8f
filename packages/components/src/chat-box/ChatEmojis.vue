<script setup lang="ts">
import { onClickOutside } from '@vueuse/core'
import { computed, ref } from 'vue'

defineOptions({
  name: 'Emoji',
})

defineProps<{
  focused?: boolean
}>()

const emits = defineEmits<{
  emojiChange: [val: string]
}>()

const visible = defineModel('visible', { type: Boolean, default: false })

const emojiList = computed(() => {
  const res: (number | string)[] = [
    128512,
    128513,
    128514,
    128516,
    128517,
    128521,
    128522,
    128534,
    128536,
    128540,
    128550,
    128567,
    129300,
    129319,
    129325,
    9994,
    9996,
    128076,
    128079,
    128077,
    127752,
    127771,
    127774,
    127801,
    127802,
    127881,
    128150,
    129505,
    128262,
  ]
  // 生成常用表情
  for (let i = 0; i < res.length; i++) {
    res[i] = `&#${res[i]}`
  }
  return res
})

const emojisRef = ref<HTMLElement>()

onClickOutside(emojisRef, () => {
  visible.value = false
})
function chooseEmoji(val: string) {
  emits('emojiChange', val)
  visible.value = false
}
</script>

<template>
  <div ref="emojisRef" class="hrt-chat-emojis" :style="{ left: focused ? 0 : '-15px' }">
    <span
      v-for="item in emojiList"
      :key="item"
      class="hrt-chat-emoji"
      @click.stop="() => chooseEmoji(item as string)"
      v-html="item"
    />
  </div>
</template>

<style scoped lang="less">
.hrt-chat-emojis {
  width: 260px;
  background: #ffffff;
  box-shadow: 0px 2px 24px 0px rgba(200, 201, 204, 0.5);
  border-radius: 2px;
  position: absolute;
  bottom: 48px;
  left: -15px;
  display: flex;
  flex-wrap: wrap;
  max-height: 200px;
  .hrt-chat-emoji {
    width: 24px;
    height: 24px;
    text-align: center;
    line-height: 24px;
    margin: 4px;
    margin-bottom: 16px;
    font-size: 16px;
    cursor: pointer;
    &:hover {
      border-radius: 4px;
      background: rgba(200, 201, 204, 0.5);
    }
  }
}
</style>
