<script lang="ts" setup>
import type { HrtChatBoxFile, HrtChatFileType } from './type'
import { HrtMessage, HrtTooltip } from '@hrt/components'
import { ref, shallowRef } from 'vue'
import medicate from '../../assets/imgs/medicate.png'
import questionAnswer from '../../assets/imgs/questionAnswer.png'
import record from '../../assets/imgs/record.png'
import sendEmoji from '../../assets/imgs/sendEmoji.png'
import sendImg from '../../assets/imgs/sendImg.png'
import sendVideo from '../../assets/imgs/sendVideo.png'
import expression from '../../assets/imgs/usefulExpressions.png'
import { chatBoxProps } from './chatBox'
import ChatEmojis from './ChatEmojis.vue'

const props = defineProps({
  tools: {
    ...chatBoxProps.tools,
  },
  showInput: {
    type: Boolean,
    default: true,
  },
})

const emits = defineEmits<{
  fileChange: [val: HrtChatBoxFile]
  emojiChange: [val: string]
  chatHistoryClick: [e: MouseEvent]
  medicationClick: [e: MouseEvent]
  commonExpressionsClick: [e: MouseEvent]
  questionAnswerClick: [e: MouseEvent]
  inputClick: [e: MouseEvent]
}>()

const imageRef = shallowRef()
const videoRef = shallowRef()
const showEmoji = ref(false)

function onFileChange(type: HrtChatFileType, e: Event) {
  const files: File[] = Array.from((e.target as HTMLInputElement)?.files ?? [])
  if (!validFiles(type, files)) {
    HrtMessage.error(`文件格式错误，请选择${type === 'image' ? '图片' : '视频'}文件`)
    return
  }
  emits('fileChange', {
    type,
    files,
  })
}

function validFiles(type: 'image' | 'video', files: File[]) {
  let flag = true
  for (let i = 0; i < files.length; i++) {
    const file = files[i]
    if (!file.type.startsWith(type)) {
      flag = false
      break
    }
  }
  return flag
}
</script>

<template>
  <div class="hrt-chat-box__tools-wrapper">
    <div v-if="props.showInput" class="hrt-chat-box__input-flag" @click="emits('inputClick', $event)">
      请输入内容
    </div>
    <div class="hrt-chat-box__tools">
      <template v-for="tool in props.tools" :key="tool">
        <HrtTooltip v-if="tool === 'image'" content="发送图片" effect="light" placement="top">
          <div class="hrt-chat-box__tool-item" @click="imageRef.click()">
            <img :src="sendImg">
          </div>
        </HrtTooltip>
        <HrtTooltip v-else-if="tool === 'video'" content="发送视频" effect="light" placement="top">
          <div class="hrt-chat-box__tool-item" @click="videoRef.click()">
            <img :src="sendVideo">
          </div>
        </HrtTooltip>
        <HrtTooltip v-else-if="tool === 'emoji'" content="发送表情" effect="light" placement="top">
          <div class="hrt-chat-box__tool-item" @click.stop="showEmoji = true">
            <img :src="sendEmoji">
          </div>
        </HrtTooltip>
        <HrtTooltip v-else-if="tool === 'history'" content="聊天记录" effect="light" placement="top">
          <div class="hrt-chat-box__tool-item" @click.stop="emits('chatHistoryClick', $event)">
            <img :src="record">
          </div>
        </HrtTooltip>
        <HrtTooltip v-else-if="tool === 'medication'" content="用药助手" effect="light" placement="top">
          <div class="hrt-chat-box__tool-item" @click.stop="emits('medicationClick', $event)">
            <img :src="medicate">
          </div>
        </HrtTooltip>
        <HrtTooltip v-else-if="tool === 'expression'" content="常用语" effect="light" placement="top">
          <div class="hrt-chat-box__tool-item" @click.stop="emits('commonExpressionsClick', $event)">
            <img :src="expression">
          </div>
        </HrtTooltip>
        <HrtTooltip v-else-if="tool === 'answer'" content="问题答复" effect="light" placement="top">
          <div class="hrt-chat-box__tool-item" @click.stop="emits('questionAnswerClick', $event)">
            <img :src="questionAnswer">
          </div>
        </HrtTooltip>
      </template>

      <slot />
    </div>

    <ChatEmojis v-if="showEmoji" v-model:visible="showEmoji" @emoji-change="emits('emojiChange', $event)" />
    <input
      v-show="false"
      ref="imageRef"
      accept="image/*"
      multiple
      type="file"
      @change="onFileChange('image', $event)"
    >
    <input
      v-show="false"
      ref="videoRef"
      accept="video/*"
      multiple
      type="file"
      @change="onFileChange('video', $event)"
    >
  </div>
</template>

<style lang="less">
.hrt-chat-box__tools-wrapper {
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  background: #ffffff;
  position: relative;
  border-radius: 0 0 4px 4px;
  border: 1px solid transparent;
  border-top: 0;
}
.hrt-chat-box__input-flag {
  width: 160px;
  height: 32px;
  line-height: 32px;
  padding: 0 16px;
  font-size: 14px;
  color: var(--el-color-info);
  cursor: pointer;
  border-radius: 4px;
  background: #f6f8fb;
  user-select: none;
}
.hrt-chat-box__tools {
  height: 100%;
  display: flex;
  align-items: center;
  column-gap: 24px;
  color: #708293;
}
.hrt-chat-box__tool-item {
  cursor: pointer;
  display: flex;
  text-align: center;
  align-items: center;
  height: 20px;
  user-select: none;

  img {
    width: 18px;
  }
}
</style>
