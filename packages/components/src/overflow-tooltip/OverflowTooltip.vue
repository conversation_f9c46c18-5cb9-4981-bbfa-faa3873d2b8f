<script lang="ts" setup>
import { useDebounceFn, useMutationObserver, useResizeObserver } from '@vueuse/core'
import { ElTooltip } from 'element-plus'
import { computed, h, ref, useAttrs, useSlots } from 'vue'

defineOptions({
  name: 'HrtOverflowTooltip',
})

const props = withDefaults(defineProps<{
  content?: string | undefined
  rows?: number
  /** 传入slot时tooltip是否渲染slot模板结构 */
  renderSlotTemplate?: boolean
}>(), {
  rows: 1,
})

const slots = useSlots()
const attrs: any = useAttrs()
const contentRef = ref<HTMLElement>()
const isOverflow = ref(true)

// 过滤非样式属性给 tooltip
const filteredAttrs = computed(() => {
  const { class: _, style: __, ...rest } = attrs
  return rest
})

const textOverflow = computed(() => (isOverflow.value ? 'ellipsis' : 'clip'))
const lineClamp = computed(() => (props.rows > 1 ? props.rows : null))

// 检测溢出状态
const checkOverflow = useDebounceFn(() => {
  if (!contentRef.value)
    return
  const element = contentRef.value

  // 单行文本检测水平溢出
  if (props.rows === 1) {
    if (!element.scrollWidth || !element.clientWidth)
      return
    isOverflow.value = element.scrollWidth > element.clientWidth
  }
  // 多行文本检测垂直溢出
  else {
    if (!element.scrollHeight || !element.clientHeight)
      return
    // 检测垂直溢出（高度）
    const isVerticalOverflow = element.scrollHeight > element.clientHeight
    isOverflow.value = isVerticalOverflow
  }
}, 200)

useResizeObserver(contentRef, checkOverflow)

useMutationObserver(contentRef, checkOverflow, {
  childList: true,
  subtree: true,
  characterData: true,
})
</script>

<template>
  <div class="hrt-overflow-wrapper" :class="attrs.class" :style="attrs.style">
    <ElTooltip
      :disabled="!isOverflow"
      :content="content"
      placement="top"
      :show-after="200"
      popper-class="overflow-tooltip__popper"
      v-bind="filteredAttrs"
    >
      <div ref="contentRef" class="hrt-overflow-content" :class="{ 'is-multiline': !!lineClamp }">
        <!-- 传入内容如有标签结构需使用 行内元素 -->
        <slot>{{ props.content }}</slot>
      </div>

      <template v-if="slots.default" #content>
        <component :is="h(slots.default)" v-if="props.renderSlotTemplate" />
        <span v-else>{{ contentRef?.textContent }}</span>
      </template>
    </ElTooltip>
  </div>
</template>

<style lang="less">
.overflow-tooltip__popper.el-popper {
  max-width: var(--hrt-tooltip-max-width);
  font-size: 14px;
  padding: 8px 12px;
}
.hrt-overflow-wrapper {
  overflow: hidden;
}
.hrt-overflow-content {
  max-width: 100%;
  overflow: hidden;
  text-overflow: v-bind(textOverflow);
  white-space: nowrap;
  vertical-align: bottom;
  &.is-multiline {
    white-space: normal;
    display: -webkit-box;
    line-clamp: v-bind(lineClamp);
    -webkit-line-clamp: v-bind(lineClamp);
    -webkit-box-orient: vertical;
  }
}
</style>
