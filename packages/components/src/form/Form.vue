<script lang="ts" setup>
import { ElForm, formProps } from 'element-plus'
import { getCurrentInstance } from 'vue'

defineOptions({
  name: 'HrtForm',
})

const props = defineProps({
  ...formProps,
})

const vm = getCurrentInstance()

function changeRef(el: any) {
  vm!.exposed = el || {}
  vm!.exposeProxy = el || {}
}
</script>

<template>
  <ElForm class="hrt-form" v-bind="{ ...$attrs, ...props, ref: changeRef }">
    <template v-for="(_, name) in $slots" :key="name" #[name]="scope">
      <slot :name="name" v-bind="scope || {}" />
    </template>
  </ElForm>
</template>

<style lang="less">
</style>
