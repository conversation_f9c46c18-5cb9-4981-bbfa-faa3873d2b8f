<script setup lang="ts">
import type { Placement } from 'element-plus'
import { ElDatePicker } from 'element-plus'
import { computed, ref } from 'vue'

interface IProps {
  placement?: Placement
  modelValue?: number | string | Date | [Date, Date] | [string, string]
  clearable?: boolean
}

defineOptions({ name: 'HrtDatePicker' })

const props = withDefaults(defineProps<IProps>(), {
  placement: 'bottom-start',
  clearable: true,
})
const isHovering = ref<boolean>(false)

const showClear = computed(() => props.clearable && props.modelValue && isHovering.value)
</script>

<template>
  <div
    class="hrt-inline-flex"
    @mouseenter="isHovering = true"
    @mouseleave="isHovering = false"
  >
    <ElDatePicker
      v-bind="$attrs"
      class="hrt-date-picker"
      :class="showClear && 'hrt-date-picker-has-clear'"
      :model-value="props.modelValue"
      :placement="props.placement"
    />
  </div>
</template>

<style lang="less">
.hrt-date-picker {
  .el-input__prefix-inner > :last-child {
    margin-right: 0;
  }
  &.el-input .el-input__wrapper,
  &.el-range-editor.el-input__wrapper {
    --el-input-hover-border-color: var(--hrt-color-blue);
    padding-left: 1px;
    padding-right: 38px;
    position: relative;
    .el-input__inner {
      padding-left: 11px;
    }
    .el-range-separator {
      flex: none;
      padding: 0 4px;
      color: var(--hrt-color-neutral-400);
    }
    .el-input__prefix,
    .el-input__suffix,
    .el-range__icon,
    .el-range__close-icon {
      position: absolute;
      right: 1px;
      top: 1px;
      border: none;
      height: calc(100% - 2px);
      line-height: calc(100% - 2px);
      background-color: inherit;
      padding-left: 12px;
      padding-right: 12px;
      width: unset;
    }
    .el-input__suffix,
    .el-range__close-icon {
      display: none;
    }
  }
  &.el-range-editor.el-input__wrapper {
    justify-content: flex-start;
    .el-range-input {
      flex: none;
      padding: 0 12px;
    }
  }
  &.el-date-editor--datetimerange.el-input__wrapper {
    .el-range-input {
      width: 170px;
    }
  }
  &.el-date-editor--daterange.el-input__wrapper {
    .el-range-input {
      width: 106px;
    }
  }
  &.el-date-editor--monthrange.el-input__wrapper {
    .el-range-input {
      width: 83px;
    }
  }
  &.el-date-editor--yearrange.el-input__wrapper {
    .el-range-input {
      width: 80px;
    }
  }
  &.el-input--small .el-input__wrapper,
  &.el-range-editor--small.el-input__wrapper {
    padding-right: 26px;
    .el-input__inner {
      padding-left: 7px;
    }
    .el-input__prefix,
    .el-input__suffix,
    .el-range__icon,
    .el-range__close-icon {
      padding-left: 7px;
      padding-right: 7px;
    }
    .el-range-input {
      flex: none;
      padding: 0 7px;
    }
  }
  &.el-date-editor--datetimerange.el-range-editor--small.el-input__wrapper {
    .el-range-input {
      width: 140px;
    }
  }
  &.el-date-editor--daterange.el-range-editor--small.el-input__wrapper {
    .el-range-input {
      width: 85px;
    }
  }
  &.el-date-editor--monthrange.el-range-editor--small.el-input__wrapper {
    .el-range-input {
      width: 65px;
    }
  }
  &.el-date-editor--yearrange.el-range-editor--small.el-input__wrapper {
    .el-range-input {
      width: 62px;
    }
  }
  &.el-input--large .el-input__wrapper,
  &.el-range-editor--large.el-input__wrapper {
    padding-right: 44px;
    .el-input__inner {
      padding-left: 15px;
    }
    .el-input__prefix,
    .el-input__suffix,
    .el-range__icon,
    .el-range__close-icon {
      padding-left: 15px;
      padding-right: 15px;
    }
    .el-range-input {
      flex: none;
      padding: 0 15px;
    }
  }
  &.el-date-editor--datetimerange.el-range-editor--large.el-input__wrapper {
    .el-range-input {
      width: 176px;
    }
  }
  &.el-date-editor--daterange.el-range-editor--large.el-input__wrapper {
    .el-range-input {
      width: 113px;
    }
  }
  &.el-date-editor--monthrange.el-range-editor--large.el-input__wrapper {
    .el-range-input {
      width: 89px;
    }
  }
  &.el-date-editor--yearrange.el-range-editor--large.el-input__wrapper {
    .el-range-input {
      width: 86px;
    }
  }
  &.el-date-editor.is-disabled.el-input__wrapper {
    box-shadow: 0 0 0 1px var(--el-disabled-border-color) inset;
  }
}
.hrt-date-picker-has-clear:not(.is-disabled) {
  &.el-input .el-input__wrapper,
  &.el-range-editor.el-input__wrapper {
    .el-input__suffix,
    .el-range__close-icon {
      display: inline-flex;
    }
  }
}
.el-picker-panel {
  &.el-date-picker,
  &.el-date-range-picker {
    --el-datepicker-inrange-bg-color: var(--hrt-color-blue-100);
    --el-datepicker-inrange-hover-bg-color: var(--hrt-color-blue-100);
    width: auto;
    .el-picker-panel__icon-btn {
      margin-top: 0;
    }
    .el-date-picker__header {
      padding: 12px 16px 0 16px;
    }
    .el-picker-panel__content {
      margin-top: 2px;
      width: 252px;
    }
    .el-date-picker__editor-wrap,
    .el-date-range-picker__time-picker-wrap {
      width: 135px;
    }
    .el-time-spinner__item:hover:not(.is-disabled):not(.is-active) {
      background-color: #ffffff;
      color: var(--el-color-primary);
    }
    td {
      .el-date-table-cell {
        .el-date-table-cell__text {
          border-radius: 2px;
          font-weight: normal;
          border: 1px solid transparent;
        }
      }
      &.today {
        .el-date-table-cell__text {
          border: 1px solid var(--el-color-primary);
        }
      }
    }
    .el-date-table {
      tr {
        td:first-child {
          .el-date-table-cell {
            margin-left: 5px;
          }
        }
        td:last-child {
          .el-date-table-cell {
            margin-right: 5px;
          }
        }
      }
      td {
        height: 36px;
        padding: 6px 0;
        .el-date-table-cell {
          height: 24px;
          padding: 0;
        }
        &.disabled {
          .el-date-table-cell {
            color: var(--hrt-color-neutral-400);
            background-color: var(--hrt-color-neutral-700);
          }
        }
        &.start-date {
          .el-date-table-cell {
            border-bottom-left-radius: 2px;
            border-top-left-radius: 2px;
          }
        }
        &.end-date {
          .el-date-table-cell {
            border-bottom-right-radius: 2px;
            border-top-right-radius: 2px;
          }
        }
      }
      &.is-week-mode {
        .el-date-table__row {
          &:hover {
            td {
              &:first-child {
                .el-date-table-cell {
                  border-bottom-left-radius: 2px;
                  border-top-left-radius: 2px;
                }
              }
              &:last-child {
                .el-date-table-cell {
                  border-bottom-right-radius: 2px;
                  border-top-right-radius: 2px;
                }
              }
            }
          }
        }
      }
      &:not(.is-week-mode) {
        td {
          &.available:not(.start-date):not(.end-date):not(.current):not(.selected) {
            &:hover {
              color: var(--el-color-primary);
              .el-date-table-cell {
                color: inherit;
              }
              .el-date-table-cell__text {
                color: inherit;
                background-color: var(--hrt-color-blue-100);
              }
            }
          }
        }
      }
    }
    .el-month-table,
    .el-year-table {
      tr {
        td:first-child {
          .el-date-table-cell {
            margin-left: 6px;
          }
        }
        td:last-child {
          .el-date-table-cell {
            margin-right: 6px;
          }
        }
      }
      td {
        width: 63px;
        padding: 10px 0;
        .el-date-table-cell {
          height: 28px;
          padding: 0;
        }
        .el-date-table-cell__text {
          width: 51px;
          height: 28px;
          line-height: 28px;
        }
        &:not(.disabled):not(.start-date):not(.end-date):not(.current):not(.selected) {
          &:hover {
            color: var(--el-color-primary);
            .el-date-table-cell {
              color: inherit;
            }
            .el-date-table-cell__text {
              color: inherit;
              background-color: var(--hrt-color-blue-100);
            }
          }
        }
        &.disabled {
          .el-date-table-cell__text {
            color: var(--hrt-color-neutral-400);
            background-color: var(--hrt-color-neutral-700);
          }
        }
        &.start-date {
          .el-date-table-cell {
            border-bottom-left-radius: 2px;
            border-top-left-radius: 2px;
            margin-left: 6px;
          }
        }
        &.end-date {
          .el-date-table-cell {
            border-bottom-right-radius: 2px;
            border-top-right-radius: 2px;
            margin-right: 6px;
          }
        }
      }
    }
  }
  &.el-date-range-picker {
    .el-picker-panel__body {
      min-width: 564px;
    }
    .el-picker-panel__content {
      width: 282px;
    }
  }
}
</style>
