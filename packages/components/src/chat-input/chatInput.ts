import type { QuillOptions } from 'quill'
import type { PropType } from 'vue'

export const hrtChatInputProps = {
  options: {
    type: Object as PropType<QuillOptions>,
    default: () => ({}),
  },
  noFooter: {
    type: Boolean,
    default: false,
  },
  valueFormatter: {
    type: Function as PropType<(val: string) => string>,
    default: (val: string) => val,
  },
  beforeSend: {
    type: Function as PropType<() => boolean | Promise<boolean>>,
    default: () => true,
  },
  /** 初始化完成后自动聚焦 */
  autoFocus: {
    type: Boolean,
    default: false,
  },
}
