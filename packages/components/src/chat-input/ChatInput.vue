<script lang="ts" setup>
import type Quill from 'quill'
import { HrtButton } from '@hrt/components'
import { onMounted, onUnmounted, ref } from 'vue'
import { hrtChatInputProps } from './chatInput'
import 'quill/dist/quill.snow.css'

defineOptions({ name: 'HrtChatInput' })

const props = defineProps(hrtChatInputProps)
const emits = defineEmits<{
  send: [val: string]
  change: [val: string]
  focus: [val: FocusEvent]
  blur: [val: FocusEvent]
}>()

const modelValue = defineModel('modelValue', { type: String, default: '' })
console.log(import.meta.env)

let editor: Quill | null = null

const editorRef = ref<HTMLElement>()
const isFocus = ref(false)
const isHovering = ref(false)

const bindings = {
  0: {
    key: 'Enter',
    handler() {
      onSend()
      return false
    },
  },
  1: {
    key: 'Enter',
    shiftKey: true,
    handler() {
      return true
    },
  },
}

onMounted(() => {
  import('quill').then(({ default: Quill }) => {
    editor = new Quill(editorRef.value!, {
      theme: 'snow',
      formats: ['image'], // 空数组禁用所有格式
      modules: {
        toolbar: false,
        keyboard: {
          bindings,
        },
      },
      placeholder: '请输入...',
      ...props.options,
    })

    if (props.autoFocus) {
      editor.focus()
    }

    editor.on('text-change', getValue)
    editor.root.addEventListener('focus', onFocus)
    editor.root.addEventListener('blur', onBlur)
    editor.root.addEventListener('mouseenter', onMouseenter)
    editor.root.addEventListener('mouseleave', onMouseleave)
  })
})

const sendLoading = ref(false)

function onFocus(e: FocusEvent) {
  isFocus.value = true
  emits('focus', e)
}
function onBlur(e: FocusEvent) {
  isFocus.value = false
  emits('blur', e)
}
function onMouseenter() {
  isHovering.value = true
}
function onMouseleave() {
  isHovering.value = false
}

function getValue() {
  const formattedValue = props.valueFormatter(editor!.getSemanticHTML())
  modelValue.value = formattedValue
  emits('change', formattedValue)
}
async function onSend() {
  if (!props.beforeSend) {
    emits('send', modelValue.value)
    return
  }
  sendLoading.value = true
  try {
    await props.beforeSend()
    emits('send', modelValue.value)
  }
  catch (error) {
    console.error(error)
  }
  finally {
    sendLoading.value = false
  }
}

onUnmounted(() => {
  editor?.off('text-change', getValue)
  editor?.root.removeEventListener('focus', onFocus)
  editor?.root.removeEventListener('blur', onBlur)
  editor?.root.removeEventListener('mouseenter', onMouseenter)
  editor?.root.removeEventListener('mouseleave', onMouseleave)
  editor = null
})

defineExpose({
  get editor() {
    return editor
  },
})
</script>

<template>
  <div
    class="hrt-chat-input-wrapper" :class="[
      { 'hrt-chat-input--no-footer': props.noFooter },
      { 'hrt-chat-input--is-focused': isFocus },
      { 'hrt-chat-input--is-hovering': isHovering },
    ]"
  >
    <div ref="editorRef" class="hrt-chat-input" />
    <div v-if="!props.noFooter" class="hrt-chat-input-footer">
      <slot name="footer">
        <span class="hrt-chat-input-footer__tip">Enter发送，Shift + Enter换行</span>
        <HrtButton type="primary" :loading="sendLoading" @click="onSend">
          发送
        </HrtButton>
      </slot>
    </div>
  </div>
</template>

<style lang="less">
.hrt-chat-input-wrapper {
  --hrt-chat-input-footer-height: 40px;

  position: relative;
  height: 100%;
  color: var(--el-text-color-regular);
  padding-bottom: var(--hrt-chat-input-footer-height);
  box-shadow: 0 0 0 1px var(--el-border-color);
  border-radius: 2px;

  ::-webkit-scrollbar {
    width: 6px !important;
    height: 6px !important;
  }

  ::-webkit-scrollbar-thumb {
    background-color: var(--hrt-color-neutral);
    border-radius: 2px;
  }

  .hrt-chat-input {
    font-size: 14px;

    &.ql-container {
      border: none;
    }

    p {
      margin: 0;
      line-height: 20px;
    }
  }

  .ql-editor {
    padding: 5px 11px;

    &::before {
      font-style: normal;
      color: var(--hrt-color-neutral-400);
    }
  }

  img {
    width: 120px;
  }
}

.hrt-chat-input--no-footer {
  padding-bottom: 0;
}

.hrt-chat-input--is-focused,
.hrt-chat-input--is-hovering {
  box-shadow: 0 0 0 1px var(--el-color-primary);
}

.hrt-chat-input-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: var(--hrt-chat-input-footer-height);
  padding-right: 12px;
  padding-bottom: 6px;
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
  background: inherit;
}

.hrt-chat-input-footer__tip {
  font-size: 12px;
  color: var(--el-color-info);
  margin-right: 8px;
}
</style>
