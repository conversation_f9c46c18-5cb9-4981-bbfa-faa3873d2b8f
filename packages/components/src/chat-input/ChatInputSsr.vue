<script lang="ts" setup>
import { getCurrentInstance, onMounted, shallowRef } from 'vue'

defineOptions({ name: 'HrtChatInputSsr' })

const ChatInput = shallowRef()

onMounted(() => {
  import('./ChatInput.vue').then(({ default: comp }) => {
    ChatInput.value = comp
  })
})
const vm = getCurrentInstance()!
function changeRef(el: any) {
  vm.exposed = el || {}
  vm.exposeProxy = el || {}
}
</script>

<template>
  <component :is="ChatInput" v-bind="{ ...$attrs, ref: changeRef }">
    <template v-for="(_, slot) in $slots" :key="slot" #[slot]="scope">
      <slot :name="slot" v-bind="scope || {}" />
    </template>
  </component>
</template>

<style lang="less">

</style>
