<script setup lang="ts">
import { inject } from 'vue'

export interface ColProps {
  span?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12 | 0
  /** 栅格左侧的间隔格数 */
  offset?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12
  /** 栅格向右移动格数 */
  push?: number
  /** 栅格向左移动格数 */
  pull?: number
}

defineOptions({
  name: 'HrtCol',
})

const {
  span = 1,
  offset = 0,
  push = 0,
  pull = 0,
} = defineProps<ColProps>()

const gutter = inject<number>('gutter', 16)
</script>

<template>
  <div
    :class="{
      'w-full': span === 12,
      'w-11/12': span === 11,
      'w-5/6': span === 10,
      'w-3/4': span === 9,
      'w-2/3': span === 8,
      'w-7/12': span === 7,
      'w-1/2': span === 6,
      'w-5/12': span === 5,
      'w-1/3': span === 4,
      'w-1/4': span === 3,
      'w-1/6': span === 2,
      'w-1/12': span === 1,
      'ml-full': offset === 12,
      'ml-11/12': offset === 11,
      'ml-5/6': offset === 10,
      'ml-3/4': offset === 9,
      'ml-2/3': offset === 8,
      'ml-7/12': offset === 7,
      'ml-1/2': offset === 6,
      'ml-5/12': offset === 5,
      'ml-1/3': offset === 4,
      'ml-1/4': offset === 3,
      'ml-1/6': offset === 2,
      'ml-1/12': offset === 1,
      '-ml-full': pull === 12,
      '-ml-11/12': pull === 11,
      '-ml-5/6': pull === 10,
      '-ml-3/4': pull === 9,
      '-ml-2/3': pull === 8,
      '-ml-7/12': pull === 7,
      '-ml-1/2': pull === 6,
      '-ml-5/12': pull === 5,
      '-ml-1/3': pull === 4,
      '-ml-1/4': pull === 3,
      '-ml-1/6': pull === 2,
      '-ml-1/12': pull === 1,
      'mr-1/12': push === 1,
      'mr-1/6': push === 2,
      'mr-1/4': push === 3,
      'mr-1/3': push === 4,
      'mr-5/12': push === 5,
      'mr-1/2': push === 6,
      'mr-7/12': push === 7,
      'mr-2/3': push === 8,
      'mr-3/4': push === 9,
      'mr-5/6': push === 10,
      'mr-11/12': push === 11,
      'mr-full': push === 12,
    }"
    :style="{
      paddingLeft: gutter ? `${gutter / 2}px` : undefined,
      paddingRight: gutter ? `${gutter / 2}px` : undefined,
    }"
  >
    <slot />
  </div>
</template>

<style scoped>
.ml-1\/12 {
  margin-left: 8.33333%;
}
.ml-1\/6 {
  margin-left: 16.66667%;
}
.ml-1\/4 {
  margin-left: 25%;
}
.ml-1\/3 {
  margin-left: 33.33333%;
}
.ml-5\/12 {
  margin-left: 41.66667%;
}
.ml-1\/2 {
  margin-left: 50%;
}
.ml-7\/12 {
  margin-left: 58.33333%;
}
.ml-2\/3 {
  margin-left: 66.66667%;
}
.ml-3\/4 {
  margin-left: 75%;
}
.ml-5\/6 {
  margin-left: 83.33333%;
}
.ml-11\/12 {
  margin-left: 91.66667%;
}
.ml-full {
  margin-left: 100%;
}

.-ml-1\/12 {
  margin-left: -8.33333%;
}
.-ml-1\/6 {
  margin-left: -16.66667%;
}
.-ml-1\/4 {
  margin-left: -25%;
}
.-ml-1\/3 {
  margin-left: -33.33333%;
}
.-ml-5\/12 {
  margin-left: -41.66667%;
}
.-ml-1\/2 {
  margin-left: -50%;
}
.-ml-7\/12 {
  margin-left: -58.33333%;
}
.-ml-2\/3 {
  margin-left: -66.66667%;
}
.-ml-3\/4 {
  margin-left: -75%;
}
.-ml-5\/6 {
  margin-left: -83.33333%;
}
.-ml-11\/12 {
  margin-left: -91.66667%;
}
.-ml-full {
  margin-left: -100%;
}

/* 添加 mr-* 类的定义 */
.mr-1\/12 {
  margin-right: 8.33333%;
}
.mr-1\/6 {
  margin-right: 16.66667%;
}
.mr-1\/4 {
  margin-right: 25%;
}
.mr-1\/3 {
  margin-right: 33.33333%;
}
.mr-5\/12 {
  margin-right: 41.66667%;
}
.mr-1\/2 {
  margin-right: 50%;
}
.mr-7\/12 {
  margin-right: 58.33333%;
}
.mr-2\/3 {
  margin-right: 66.66667%;
}
.mr-3\/4 {
  margin-right: 75%;
}
.mr-5\/6 {
  margin-right: 83.33333%;
}
.mr-11\/12 {
  margin-right: 91.66667%;
}
.mr-full {
  margin-right: 100%;
}
</style>
