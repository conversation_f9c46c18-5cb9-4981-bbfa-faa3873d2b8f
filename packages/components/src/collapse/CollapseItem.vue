<script setup lang="ts">
import { ElCollapseItem } from 'element-plus'
import { useAttrs } from 'vue'

interface HrtCollapseItemProps {
  /** 面板标题 */
  title?: string
  /** 唯一标识符 */
  name?: string | number
  /** 是否禁用 */
  disabled?: boolean
}

defineOptions({
  name: 'HrtCollapseItem',
  inheritAttrs: false,
})
defineProps<HrtCollapseItemProps>()
const attrs = useAttrs()
</script>

<template>
  <ElCollapseItem
    v-bind="attrs"
    :title="title"
    :name="name"
    :disabled="disabled"
  >
    <slot />
  </ElCollapseItem>
</template>
