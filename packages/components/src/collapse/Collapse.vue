<script setup lang="ts">
import type { CollapseModelValue } from 'element-plus'
import { ElCollapse } from 'element-plus'
import { useAttrs } from 'vue'

interface HrtCollapseProps {
  /** 无底色模式 */
  ghost?: boolean
  /** 手风琴模式 */
  accordion?: boolean
}
defineOptions({
  name: 'HrtCollapse',
  inheritAttrs: false,
})
defineProps<HrtCollapseProps>()
const emit = defineEmits<{
  (e: 'change', value: CollapseModelValue): void
}>()
const attrs = useAttrs()

function handleChange(value: CollapseModelValue) {
  emit('change', value)
}
</script>

<template>
  <ElCollapse
    v-bind="attrs"
    expand-icon-position="left"
    class="collapse-wrapper"
    :accordion="accordion"
    :class="[ghost ? 'ghost' : '']"
    @change="handleChange"
  >
    <slot />
  </ElCollapse>
</template>

<style lang="css">
.collapse-wrapper {
  .el-collapse-item__header {
    background: var(--hrt-color-neutral-700);
    color: var(--hrt-color-neutral-100);
    padding: 12px 12px 12px 36px;
    border: 1px solid var(--hrt-color-neutral-500);
    border-top-color: transparent;
    .el-collapse-item__arrow {
      position: absolute;
      left: 36px;
    }
    &.is-active {
      border-bottom-color: var(--hrt-color-neutral-500);
    }
  }
  .el-collapse-item__content {
    padding: 12px 12px 12px 36px;
  }
  &.ghost {
    .el-collapse-item__header {
      background: #fff;
    }
  }
}
</style>
