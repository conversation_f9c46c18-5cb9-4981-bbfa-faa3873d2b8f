<script lang="ts" setup>
import type { PropType } from 'vue'
import type { HrtStepsInject } from './types'
import { SuccessFilled, Warning } from '@element-plus/icons-vue'
import { stepProps } from 'element-plus'
import { computed, getCurrentInstance, inject, onBeforeUnmount, reactive, ref } from 'vue'
import useReusableTemplate from '../../hooks/useReusableTemplate'
import { HRT_STEPS_INJECTION_KEY } from '../steps/injectKeys'

defineOptions({
  name: 'HrtStep',
})

const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  description: {
    type: String,
    default: '',
  },
  /** 步骤结束的状态 */
  finishStatus: {
    type: String as PropType<'error' | 'success'>,
    default: 'success',
  },
  /** 自定义图标 */
  icon: {
    ...stepProps.icon,
    default: '',
  },
})

const emits = defineEmits<{
  (e: 'click', ev: MouseEvent): void
}>()

const index = ref(-1)
const parent = inject(HRT_STEPS_INJECTION_KEY) as HrtStepsInject
const vm = getCurrentInstance()!

function setIndex(val: number) {
  index.value = val
}

const { DefineTemplate, ReusableTemplate } = useReusableTemplate()

const status = computed(() => {
  if (index.value > parent.props.active) {
    return 'is-wait'
  }
  if (index.value === parent.props.active) {
    return 'is-running'
  }
  return `is-${props.finishStatus}`
})

function onClick(e: MouseEvent) {
  emits('click', e)
  if (!parent.props.card || parent.props.active === index.value)
    return
  parent.changeActive(index.value)
}

const stepItemState = reactive({
  uid: vm.uid,
  getVnode: () => vm.vnode,
  setIndex,
})
parent.addStep(stepItemState)

onBeforeUnmount(() => {
  parent.removeStep(stepItemState)
})
</script>

<template>
  <DefineTemplate>
    <div class="hrt-step__icon-wrapper">
      <slot name="icon">
        <component :is="parent.iconSlot.value" v-if="parent.iconSlot.value" />
        <el-icon v-else-if="props.icon" class="hrt-step__status-icon">
          <component :is="props.icon" />
        </el-icon>
        <el-icon v-else-if="parent.props.icon" class="hrt-step__status-icon">
          <component :is="parent.props.icon" />
        </el-icon>
        <el-icon v-else-if="status === 'is-success'" class="hrt-step__status-icon">
          <SuccessFilled />
        </el-icon>
        <el-icon v-else-if="status === 'is-error'" class="hrt-step__status-icon">
          <Warning />
        </el-icon>
        <span v-else-if="index < 0 || index >= parent.props.active" class="num">{{ index + 1 }}</span>
      </slot>
    </div>
  </DefineTemplate>

  <!-- 卡片风格 -->
  <div
    v-if="parent.props.card" class="hrt-step hrt-step-card" :class="[
      { 'is-first': index === 0 },
      { 'is-last': index === parent.steps.value.length - 1 },
      status,
    ]" @click="onClick"
  >
    <div v-if="index > 0" class="hrt-step-triangle hrt-step-triangle__before" />
    <div class="hrt-step__main">
      <ReusableTemplate v-if="parent.props.cardSeq" />
      <slot name="title">
        {{ props.title }}
      </slot>
    </div>
    <div v-if="index < parent.steps.value.length - 1" class="hrt-step-triangle hrt-step-triangle__after" />
  </div>

  <!-- 图文纵向排列 -->
  <div v-else-if="parent.props.alignCenter" class="hrt-step hrt-step-center" :class="status">
    <div class="hrt-step__head">
      <ReusableTemplate />
      <div v-if="index < parent.steps.value.length - 1" class="hrt-step__progress-line">
        <i class="hrt-step__progress-line__inner" />
      </div>
    </div>
    <div class="hrt-step__content">
      <div class="hrt-step__title">
        <slot name="title">
          {{ props.title }}
        </slot>
      </div>
      <div class="hrt-step__desc">
        <slot name="description">
          {{ props.description }}
        </slot>
      </div>
    </div>
  </div>

  <!-- 垂直的步骤条 -->
  <div
    v-else-if="parent.props.direction === 'vertical'"
    class="hrt-step hrt-step-vertical"
    :class="[{ 'is-last': index === parent.steps.value.length - 1 }, status]"
  >
    <div class="hrt-step__head">
      <ReusableTemplate />
      <div v-if="index < parent.steps.value.length - 1" class="hrt-step__progress-line" />
    </div>
    <div class="hrt-step__content">
      <div class="hrt-step__title">
        <slot name="title">
          {{ props.title }}
        </slot>
      </div>
      <slot name="description">
        {{ props.description }}
      </slot>
    </div>
  </div>

  <!-- 图文横向排列 -->
  <div v-else class="hrt-step hrt-step-normal" :class="status">
    <div class="hrt-step__head">
      <ReusableTemplate />
      <div class="hrt-step__title">
        <slot name="title">
          {{ props.title }}
        </slot>
      </div>
      <div v-if="index < parent.steps.value.length - 1" class="hrt-step__progress-line" />
    </div>
    <div class="hrt-step__content">
      <slot name="description">
        {{ props.description }}
      </slot>
    </div>
  </div>
</template>

<style lang="less">
.hrt-step {
  flex: 1 0 0%;
}

.hrt-step-card {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  height: var(--hrt-step-height);
  cursor: pointer;
  --hrt-step-height: 44px;
  --hrt-step-border-color: var(--hrt-color-neutral-500);
  --hrt-step-hover-border-color: var(--el-color-primary-light-3);
  --hrt-step-active-border-color: var(--el-color-primary);
  --hrt-step-triangle-border-width: calc(var(--hrt-step-height) / 2);
  --hrt-step-triangle-width: 8px;

  & + .hrt-step {
    margin-left: -10px;
  }

  &:hover {
    .hrt-step__main {
      background: var(--hrt-step-hover-border-color);
      color: #fff;
    }

    .hrt-step-triangle__before {
      border-left-color: var(--hrt-step-hover-border-color);
    }

    .hrt-step-triangle__after::before {
      border-left-color: var(--hrt-step-hover-border-color);
    }

    .hrt-step__main .hrt-step__icon-wrapper {
      color: inherit;
    }
  }

  &:active,
  &.is-running {
    .hrt-step__main {
      background: var(--hrt-step-active-border-color);
      color: #fff;
    }

    .hrt-step-triangle__before {
      border-left-color: var(--hrt-step-active-border-color);
    }

    .hrt-step-triangle__after::before {
      border-left-color: var(--hrt-step-active-border-color);
    }
  }

  .hrt-step__main {
    height: 100%;
    flex: 1;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    column-gap: 8px;
    border: 1px solid var(--hrt-step-border-color);
    border-right-width: 0;
    border-left-width: 0;
    padding-left: calc(var(--hrt-step-triangle-width) * 2);
  }

  &.is-first {
    .hrt-step__main {
      border-left-width: 1px;
      padding-left: 0;
    }
  }

  &.is-last {
    .hrt-step__main {
      border-right-width: 1px;
    }
  }

  .hrt-step-triangle {
    width: 8px;
    height: 100%;
    position: relative;
    border-top: var(--hrt-step-triangle-border-width) solid transparent;
    border-bottom: var(--hrt-step-triangle-border-width) solid transparent;
    border-left: calc(calc((var(--hrt-step-triangle-border-width) / 2)) + 2px) solid var(--hrt-step-hover-border-color);
    border-left-color: var(--hrt-step-border-color);

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -1px;
      display: inline-block;
      width: 100%;
      height: 100%;
      border-top: calc(var(--hrt-step-triangle-border-width) - 1px) solid transparent;
      border-bottom: calc(var(--hrt-step-triangle-border-width) - 1px) solid transparent;
      border-left: calc(calc((var(--hrt-step-triangle-border-width) / 2)) + 2px) solid #fff;
      transform: translate(-100%, -50%);
    }
  }

  .hrt-step-triangle__before {
    transform: translateX(100%);
  }
}
.hrt-step-card.is-wait,
.hrt-step-card.is-running {
  .hrt-step__main .hrt-step__icon-wrapper {
    color: inherit;
  }
}

.hrt-step-vertical {
  display: flex;
  &:not(.is-last) {
    min-height: 120px;
  }
  .hrt-step__head {
    position: relative;
  }
  .hrt-step__progress-line {
    position: absolute;
    width: 2px;
    top: 0;
    bottom: 0;
    left: 11px;
    border-right: 2px solid #e1e5ed;
    height: calc(100% - 40px);
    transform: translateY(32px);
  }
  .hrt-step__content {
    padding-left: 8px;
  }
}

.hrt-step-center {
  .hrt-step__head {
    text-align: center;
    position: relative;
  }
  .hrt-step__icon-wrapper {
    display: inline-flex;
    vertical-align: top;
  }
  .hrt-step__content {
    text-align: center;
  }
  .hrt-step__desc {
    margin-top: 4px;
  }

  .hrt-step__progress-line {
    position: absolute;
    top: 11px;
    left: 50%;
    right: -50%;
    width: calc(100% - 48px);
    transform: translateX(24px);
  }
}

.hrt-step-normal {
  .hrt-step__head {
    display: flex;
    column-gap: 8px;
  }
  .hrt-step__content {
    padding-left: 32px;
    padding-right: 16px;
    margin-top: 4px;
  }
  .hrt-step__progress-line {
    flex: 1;
    margin-right: 16px;
    margin-top: 11px;
  }
}

.hrt-step__content {
  color: #939cae;
  font-size: 12px;
}

.hrt-step__title {
  color: #939cae;
  font-size: 14px;
}

.hrt-step__progress-line {
  height: 2px;
  border-bottom: 2px solid #e1e5ed;
}

.hrt-step.is-success .hrt-step__progress-line {
  border-color: var(--el-color-primary);
}

.hrt-step.is-error .hrt-step__progress-line {
  border-color: var(--el-color-danger);
}

.hrt-step.is-running {
  .num {
    background: #fff;
    color: var(--el-color-primary);
    border-color: var(--el-color-primary);
  }
  .hrt-step__icon-wrapper {
    color: var(--el-color-primary);
  }
}

.hrt-step.is-success .hrt-step__icon-wrapper {
  color: var(--el-color-primary);
}

.hrt-step.is-error {
  .hrt-step__icon-wrapper {
    color: var(--el-color-danger);
  }

  .hrt-step__title {
    color: var(--el-color-danger);
  }
}

.hrt-step.is-running,
.hrt-step.is-success {
  .hrt-step__title {
    color: #15233f;
    font-weight: bold;
  }
}

.hrt-step__align-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  column-gap: 8px;
}

.hrt-step__icon-wrapper {
  flex-shrink: 0;
}

.hrt-step__icon-wrapper .num {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: var(--hrt-color-neutral-400);
  font-size: 14px;
  color: #fff;
  font-weight: bold;
  border: 2px solid var(--hrt-color-neutral-400);
}

.hrt-step__icon-wrapper .hrt-step__status-icon {
  font-size: 24px;
  vertical-align: top;
}
</style>
