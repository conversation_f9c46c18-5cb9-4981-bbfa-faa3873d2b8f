import type { ComputedR<PERSON>, Ref, SetupContext, VNode } from 'vue'

export interface HrtStepItemState {
  uid: number
  getVnode: () => VNode
  setIndex: (val: number) => void
}

export interface HrtStepsInject {
  props: InstanceType<typeof import('../steps/Steps.vue')['default']>['$props']
  steps: Ref<HrtStepItemState[]>
  iconSlot: ComputedRef<SetupContext['slots']['icon']>
  addStep: (item: HrtStepItemState) => void
  removeStep: (item: HrtStepItemState) => void
  changeActive: (val: number) => void
}
