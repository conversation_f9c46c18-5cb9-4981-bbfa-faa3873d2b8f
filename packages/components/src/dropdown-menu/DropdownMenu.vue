<script lang="ts" setup>
import { dropdownMenuProps, ElDropdownMenu } from 'element-plus'
import { getCurrentInstance } from 'vue'

defineOptions({
  name: 'HrtDropdownMenu',
})

const props = defineProps({
  ...dropdownMenuProps,
})

const vm = getCurrentInstance()

function changeRef(el: any) {
  vm!.exposed = el || {}
  vm!.exposeProxy = el || {}
}
</script>

<template>
  <ElDropdownMenu class="hrt-dropdown-menu" v-bind="{ ...$attrs, ...props, ref: changeRef }">
    <template v-for="(_, name) in $slots" :key="name" #[name]="scope">
      <slot :name="name" v-bind="scope || {}" />
    </template>
  </ElDropdownMenu>
</template>

<style lang="less">
</style>
