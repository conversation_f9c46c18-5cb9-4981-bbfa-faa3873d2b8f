<script setup lang="ts">
import { CircleCheckFilled, CircleCloseFilled } from '@element-plus/icons-vue'
import { ElIcon, ElProgress } from 'element-plus'

export interface HrtProgressProps {
  /** 进度百分比 */
  percentage?: number
  /** 进度条类型 */
  type?: 'line' | 'circle' | 'dashboard'
  /** 进度条状态 */
  status?: 'success' | 'exception' | 'warning'
}

defineOptions({
  name: 'HrtProgress',
})

withDefaults(defineProps<HrtProgressProps>(), {
  type: 'line',
  percentage: 0,
})
</script>

<template>
  <ElProgress
    v-bind="$attrs"
    :percentage="percentage"
    :type="type"
    :status="status"
    class="hrt-progress"
  >
    <template v-if="status === 'success'">
      <ElIcon><CircleCheckFilled /></ElIcon>
    </template> <template v-else-if="status === 'exception'">
      <ElIcon><CircleCloseFilled /></ElIcon>
    </template>
    <slot v-else />
  </ElProgress>
</template>

<style lang="css">
.hrt-progress {
  .el-progress-bar__outer {
    background-color: var(--hrt-color-neutral-600);
  }
}
</style>
