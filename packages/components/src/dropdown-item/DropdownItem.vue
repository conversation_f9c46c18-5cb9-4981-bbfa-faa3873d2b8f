<script lang="ts" setup>
import { dropdownItemProps, ElDropdownItem } from 'element-plus'
import { getCurrentInstance } from 'vue'

defineOptions({
  name: 'HrtDropdownItem',
})

const props = defineProps({
  ...dropdownItemProps,
})

const vm = getCurrentInstance()

function changeRef(el: any) {
  vm!.exposed = el || {}
  vm!.exposeProxy = el || {}
}
</script>

<template>
  <ElDropdownItem class="hrt-dropdown-item" v-bind="{ ...$attrs, ...props, ref: changeRef }">
    <template v-for="(_, name) in $slots" :key="name" #[name]="scope">
      <slot :name="name" v-bind="scope || {}" />
    </template>
  </ElDropdownItem>
</template>

<style lang="less">
</style>
