import type { DirectiveBinding } from 'vue'
import { createApp } from 'vue'
import Spin from './Spin.vue'

interface SpinOptions {
  text?: string
  size?: 'small' | 'medium' | 'large'
  direction?: 'horizontal' | 'vertical'
  show?: boolean
}

type SpinBinding = boolean | SpinOptions

const SPIN_APP_KEY = '__hrt_spin_app__'
const SPIN_EL_KEY = '__hrt_spin_el__'
const SPIN_ORIGINAL_STYLES_KEY = '__hrt_spin_original_styles__'

function saveOriginalStyles(el: HTMLElement) {
  const originalStyles = {
    position: el.style.position,
    transform: el.style.transform,
    overflow: el.style.overflow,
  }
  ;(el as any)[SPIN_ORIGINAL_STYLES_KEY] = originalStyles
}

function restoreOriginalStyles(el: HTMLElement) {
  const originalStyles = (el as any)[SPIN_ORIGINAL_STYLES_KEY]
  if (originalStyles) {
    el.style.position = originalStyles.position
    el.style.transform = originalStyles.transform
    el.style.overflow = originalStyles.overflow
    delete (el as any)[SPIN_ORIGINAL_STYLES_KEY]
  }
}

function createSpinEl() {
  const container = document.createElement('div')
  container.style.position = 'absolute'
  container.style.top = '0'
  container.style.left = '0'
  container.style.width = '100%'
  container.style.height = '100%'
  container.style.display = 'flex'
  container.style.justifyContent = 'center'
  container.style.alignItems = 'center'
  container.style.background = 'rgba(0, 0, 0, 0.2)'
  container.style.zIndex = '9999'
  return container
}

const vSpin = {
  mounted(el: HTMLElement, binding: DirectiveBinding<SpinBinding>) {
    const show = typeof binding.value === 'boolean'
      ? binding.value
      : binding.value.show
    if (show) {
      const options = typeof binding.value === 'object' ? binding.value : {}
      const app = createApp(Spin, { ...options })
      const spinEl = createSpinEl()

      // 保存原始样式
      saveOriginalStyles(el)

      // 设置父元素样式，创建新的包含块使 fixed 定位相对于父元素
      el.style.position = 'relative'
      el.style.transform = 'translateZ(0)' // 创建新的层叠上下文和包含块
      el.style.overflow = 'hidden'

      app.mount(spinEl)
      el.appendChild(spinEl);
      (el as any)[SPIN_APP_KEY] = app;
      (el as any)[SPIN_EL_KEY] = spinEl
    }
  },
  updated(el: HTMLElement, binding: DirectiveBinding<SpinBinding>) {
    const app = (el as any)[SPIN_APP_KEY]
    const spinEl = (el as any)[SPIN_EL_KEY]
    const show = typeof binding.value === 'boolean'
      ? binding.value
      : binding.value.show
    if (show) {
      if (!app) {
        // 新增
        const options = typeof binding.value === 'object' ? binding.value : {}
        const newApp = createApp(Spin, { ...options })
        const newSpinEl = createSpinEl()

        // 保存原始样式
        saveOriginalStyles(el)

        // 设置父元素样式，创建新的包含块使 fixed 定位相对于父元素
        el.style.position = 'relative'
        el.style.transform = 'translateZ(0)' // 创建新的层叠上下文和包含块
        el.style.overflow = 'hidden'

        newApp.mount(newSpinEl)
        el.appendChild(newSpinEl)
        ;(el as any)[SPIN_APP_KEY] = newApp
        ;(el as any)[SPIN_EL_KEY] = newSpinEl
      }
    }
    else {
      // 移除
      if (app && spinEl) {
        app.unmount()
        el.removeChild(spinEl)
        // 恢复原始样式
        restoreOriginalStyles(el)
        delete (el as any)[SPIN_APP_KEY]
        delete (el as any)[SPIN_EL_KEY]
      }
    }
  },
  unmounted(el: HTMLElement) {
    const app = (el as any)[SPIN_APP_KEY]
    const spinEl = (el as any)[SPIN_EL_KEY]
    if (app && spinEl) {
      app.unmount()
      el.removeChild(spinEl)
      // 恢复原始样式
      restoreOriginalStyles(el)
      delete (el as any)[SPIN_APP_KEY]
      delete (el as any)[SPIN_EL_KEY]
    }
  },
}

export default vSpin
