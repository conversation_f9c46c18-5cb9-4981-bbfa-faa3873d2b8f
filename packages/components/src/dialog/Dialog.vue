<script setup lang="ts">
import type { DialogProps } from 'element-plus'
import { ElDialog } from 'element-plus'
import { computed, useSlots, watch } from 'vue'

export interface HrtDialogProps extends Partial<DialogProps> {
  /** 弹窗大小 */
  size?: 'small' | 'middle' | 'large' | 'extraLarge' | 'huge'
  /** 弹窗高度 */
  height?: number
  /** 事件透传 */
  disablePointerEvents?: boolean
}
defineOptions({
  name: 'HrtDialog',
})
const {
  size = 'large',
  modalClass,
  showClose = true,
  modelValue,
  modal = true,
  draggable = false,
  title,
  height,
  disablePointerEvents = false,
  ...restProps
} = defineProps<HrtDialogProps>()
const emit = defineEmits<{
  open: []
  opened: []
  close: []
  closed: []
  openAutoFocus: []
  closeAutoFocus: []
}>()
const slots = useSlots()
const show = defineModel({ default: false })
const contentHeight = computed(() => {
  if (height) {
    return `${height}px`
  }
  switch (size) {
    case 'small':
      return '160px'
    case 'middle':
      return '240px'
    case 'large':
      return '400px'
    case 'extraLarge':
      return '560px'
    case 'huge':
      return '800px'
    default:
      return '400px'
  }
})

const contentWidth = computed(() => {
  switch (size) {
    case 'small':
      return '320px'
    case 'middle':
      return '400px'
    case 'large':
      return '600px'
    case 'extraLarge':
      return '800px'
    case 'huge':
      return '1200px'
    default:
      return '400px'
  }
})

const modalClassValue = computed(() => {
  let sizeClass
  switch (size) {
    case 'small':
      sizeClass = 'hrt-dialog-xs'
      break
    case 'middle':
      sizeClass = 'hrt-dialog-md'
      break
    case 'large':
      sizeClass = 'hrt-dialog-lg'
      break
    case 'extraLarge':
      sizeClass = 'hrt-dialog-xl'
      break
    case 'huge':
      sizeClass = 'hrt-dialog-huge'
      break
    default:
      sizeClass = ''
      break
  }
  if (disablePointerEvents) {
    sizeClass += ' hrt-dialog-pointer-events-none'
  }
  return `hrt-dialog ${sizeClass} ${modalClass ?? ''} ${title ? 'hrt-dialog-has-title' : ''}`
})

const columnSlots = computed(() => {
  if (slots) {
    return Object.keys(slots)
      .map((t) => {
        return { name: t, slot: slots[t] }
      })
  }
  return []
})

function openModal() {
  document.body.classList.add('hrt-body-no-scroll')
}

function closeModal() {
  document.body.classList.remove('hrt-body-no-scroll')
}

watch(show, (val) => {
  if (val) {
    openModal()
  }
  else {
    closeModal()
  }
})
</script>

<template>
  <ElDialog
    v-model="show"
    v-bind="restProps"
    :modal-class="modalClassValue"
    :show-close="showClose"
    :modal="modal"
    :draggable="modal ? false : draggable"
    :style="{ '--hrt-dialog-height': contentHeight, '--hrt-dialog-width': contentWidth }"
    :title="title"
    @open="emit('open')"
    @opened="emit('opened')"
    @close="emit('close')"
    @closed="emit('closed')"
    @open-auto-focus="emit('openAutoFocus')"
    @close-auto-focus="emit('closeAutoFocus')"
  >
    <template v-for="slot in columnSlots" :key="slot.name" #[slot.name]="{ record }">
      <slot :name="slot.name" :record="record" />
    </template>
  </ElDialog>
</template>

<style lang="css">
.hrt-dialog {
  &.hrt-dialog-pointer-events-none {
    pointer-events: none;
  }
  .el-overlay-dialog {
    background-color: var(--el-overlay-color-lighter);
  }
  &.hrt-dialog-has-title {
    .el-dialog__header {
      border-bottom: 1px solid var(--el-border-color);
    }
  }

  .el-dialog {
    @apply hrt-rounded hrt-shadow-lg hrt-flex hrt-flex-col;
    padding: 0;
    pointer-events: auto;

    .el-dialog__header {
      padding: 16px 16px 12px;
      color: var(--hrt-color-neutral-100);
      font-weight: 500;
      font-size: 16px;
      .el-dialog__headerbtn {
        top: 5px;
      }
    }

    .el-dialog__body {
      flex: 1;
      overflow: auto;
      padding: 16px;
    }

    .el-dialog__footer {
      padding: 16px;
      /* border-top: 1px solid var(--el-border-color); */
    }
  }
}

.hrt-dialog-huge {
  .el-dialog {
    width: 1200px;
    height: var(--hrt-dialog-height, 800px);
    width: var(--hrt-dialog-width, 1200px);
    margin: calc(50vh - 400px) auto;
  }
}

.hrt-dialog-xl {
  .el-dialog {
    height: var(--hrt-dialog-height, 560px);
    width: var(--hrt-dialog-width, 800px);
  }
}

.hrt-dialog-lg {
  .el-dialog {
    width: var(--hrt-dialog-width, 600px);
    height: var(--hrt-dialog-height, 400px);
  }
}

.hrt-dialog-md {
  .el-dialog {
    width: var(--hrt-dialog-width, 400px);
    height: var(--hrt-dialog-height, 240px);
  }
}

.hrt-dialog-xs {
  .el-dialog {
    width: var(--hrt-dialog-width, 320px);
    height: var(--hrt-dialog-height, 160px);
  }
}

.hrt-body-no-scroll {
  overflow: hidden;
}
</style>
