<script lang="ts" setup>
import type { HrtSelectInjectState } from '../select/injectKeys'
import { HrtOverflowTooltip } from '@hrt/components'
import { ElOption } from 'element-plus'
import { computed, getCurrentInstance, inject } from 'vue'
import { getHighlightKeywords } from '../../utils'
import { HrtSelectInjectKey } from '../select/injectKeys'

defineOptions({ name: 'HrtOption' })

const props = defineProps<{
  value: string | number | boolean | object
  label?: string | number
  disabled?: boolean
  /** 辅助文字  二封属性 */
  description?: string
  /** 辅助文字是否多行展示，仅在description存在时生效  二封属性 */
  multiline?: boolean | { rows: number }
}>()

const rows = computed(() => {
  if (props.multiline) {
    if (typeof props.multiline === 'object') {
      return props.multiline.rows
    }
    return 2
  }
  return 1
})

const vm = getCurrentInstance()
function changeRef(el: any) {
  vm!.exposed = el || {}
  vm!.exposeProxy = el || {}
}

const renderedLabel = computed(() => props.label || props.value)
const selectState = inject<HrtSelectInjectState>(HrtSelectInjectKey)!
</script>

<template>
  <ElOption class="hrt-option" :class="{ 'is-multiline': props.multiline }" v-bind="{ ...$attrs, ...props, ref: changeRef }">
    <slot>
      <div
        :class="[
          { 'hrt-option-description__wrapper': props.description },
          { 'hrt-option-match__wrapper': selectState.filterable },
        ]"
      >
        <!-- 默认展示 -->
        <HrtOverflowTooltip render-slot-template>
          <span
            v-for="(item, i) in getHighlightKeywords(renderedLabel as string, selectState.matchStr!)"
            :key="i"
            :class="{ 'hrt-text-highlight': i % 2 !== 0 }"
          >{{ item }}</span>
        </HrtOverflowTooltip>

        <!-- 辅助文字 -->
        <HrtOverflowTooltip
          v-if="props.description"
          :rows="rows"
          :content="props.description"
          class="hrt-option-description"
        />
      </div>
    </slot>
  </ElOption>
</template>

<style lang="less">
.hrt-option-description__wrapper {
  height: 100%;
  display: flex;
  align-items: center;
}
.hrt-option-description {
  color: #939cae;
  margin-left: 8px;
  flex: 1;
}
.hrt-option.el-select-dropdown__item.is-multiline {
  height: unset;
  line-height: 20px;
  padding-top: 6px;
  padding-bottom: 6px;
}
.el-select-dropdown__item.is-multiline .hrt-option-description__wrapper {
  height: unset;
  flex-direction: column;
  align-items: unset;
  justify-content: center;
}
.el-select-dropdown__item.is-multiline .hrt-option-description {
  margin-left: 0;
  margin-top: 4px;
  line-height: 17px;
  font-size: 12px;
  white-space: wrap;
}
</style>
