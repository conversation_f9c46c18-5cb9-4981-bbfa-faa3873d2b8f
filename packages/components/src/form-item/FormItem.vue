<script lang="ts" setup>
import { ElFormItem, formItemProps } from 'element-plus'
import { computed, getCurrentInstance } from 'vue'

defineOptions({
  name: 'HrtFormItem',
})

const props = defineProps({
  ...formItemProps,
  /** label 是否显示冒号 默认true */
  colon: {
    type: Boolean,
    default: true,
  },
})

const renderLabel = computed(() => {
  if (!props.label)
    return ''
  return props.colon ? `${props.label}：` : props.label
})

const vm = getCurrentInstance()
function changeRef(el: any) {
  vm!.exposed = el || {}
  vm!.exposeProxy = el || {}
}
</script>

<template>
  <ElFormItem class="hrt-form-item" v-bind="{ ...$attrs, ...props, label: renderLabel, ref: changeRef }">
    <template v-for="(_, name) in $slots" :key="name" #[name]="scope">
      <slot :name="name" v-bind="scope || {}" />
    </template>
  </ElFormItem>
</template>

<style lang="less">
.hrt-form-item.el-form-item {
  margin-bottom: 24px;
}
.hrt-form-item .el-form-item__label {
  padding-right: 16px;
}

.hrt-form-item.el-form-item.is-error {
  .hrt-select .el-select__wrapper,
  .hrt-tree-select .el-select__wrapper,
  .hrt-cascader {
    border-color: var(--el-color-danger);
  }
}
</style>
