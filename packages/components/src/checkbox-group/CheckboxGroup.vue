<script setup lang="ts">
import { ElCheckboxGroup } from 'element-plus'

const { direction = 'horizontal' } = defineProps<HrtCheckboxGroupProps>()

export interface HrtCheckboxGroupProps {
  /** 排列方向 */
  direction?: 'horizontal' | 'vertical'
}
</script>

<template>
  <ElCheckboxGroup
    v-bind="$attrs"
    class="hrt-checkbox-group"
    :class="{
      'is-vertical': direction === 'vertical',
    }"
  >
    <template v-for="(_, key) in $slots" #[key]="slotProps" :key="key">
      <slot :name="key" v-bind="slotProps" />
    </template>
  </ElCheckboxGroup>
</template>

<style lang="css">
.hrt-checkbox-group {
  &.is-vertical {
    display: flex;
    flex-direction: column;
  }
}
</style>
