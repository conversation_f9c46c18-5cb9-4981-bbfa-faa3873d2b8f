import type { App } from 'vue'
import {
  ElAffix,
  ElAlert,
  ElAutocomplete,
  ElButton,
  ElCollapse,
  ElCollapseItem,
  ElMessage,
  ElTabPane,
  ElTabs,
} from 'element-plus'
import * as components from './components'
import AutoComplete from './src/autocomplete'
import Button from './src/button'
import Collapse from './src/collapse'

import Input from './src/input'
import 'element-plus/dist/index.css'
import './style/element-plus.less'
import './style/variables.less'

export * from './components'

export {
  AutoComplete,
  // hrt components
  Button,
  Collapse,
  ElAffix,
  ElAlert,
  ElAutocomplete,
  // todo
  ElButton,
  ElCollapse,
  ElCollapseItem,
  ElMessage,
  ElTabPane,
  ElTabs,
  Input,
}

export default {
  install: (app: App) => {
    Object.entries(components).forEach(([key, value]: [string, any]) => {
      app.component(key, value)
    })
  },
}
