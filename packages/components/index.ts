import type { App } from 'vue'
import * as components from './components'
import { HrtDirectives } from './directives'
import * as plugins from './plugins'
import './style/index.css'

export * from './components'
export * from './plugins'

export default {
  install: (app: App) => {
    Object.entries(components).forEach(([key, value]: [string, any]) => {
      app.component(key, value)
    })

    Object.entries(HrtDirectives).forEach(([key, value]: [string, any]) => {
      app.directive(key, value)
    })

    Object.entries(plugins).forEach(([, value]: [string, any]) => {
      app.use(value)
    })
  },
}
