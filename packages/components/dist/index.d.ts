import { App } from 'vue';
import { ElAffix, ElAlert, ElAutocomplete, ElButton, ElCollapse, ElCollapseItem, ElMessage, ElTabPane, ElTabs } from 'element-plus';
import { default as AutoComplete } from './src/autocomplete';
import { default as Collapse } from './src/collapse';
import { default as Input } from './src/input';
import { default as Button } from './src/button';
export * from './components';
export { AutoComplete, Button, Collapse, ElAffix, ElAlert, ElAutocomplete, ElButton, ElCollapse, ElCollapseItem, ElMessage, ElTabPane, ElTabs, Input, };
declare const _default: {
    install: (app: App) => void;
};
export default _default;
