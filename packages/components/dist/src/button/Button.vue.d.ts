import { ButtonProps } from 'element-plus';
export interface HrtButtonProps extends Omit<Partial<ButtonProps>, 'size' | 'type' | 'plain'> {
    size?: 'small' | 'large' | 'default';
    /** 按钮是否为块级元素 */
    block?: boolean;
    type?: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'text' | 'default';
    plain?: boolean;
    disabled?: boolean;
    dashed?: boolean;
}
declare function __VLS_template(): {
    attrs: Partial<{}>;
    slots: {
        default?(_: {}): any;
    };
    refs: {};
    rootEl: any;
};
type __VLS_TemplateResult = ReturnType<typeof __VLS_template>;
declare const __VLS_component: import('vue').DefineComponent<HrtButtonProps, {}, {}, {}, {}, import('vue').ComponentOptionsMixin, import('vue').ComponentOptionsMixin, {}, string, import('vue').PublicProps, Readonly<HrtButtonProps> & Readonly<{}>, {}, {}, {}, {}, string, import('vue').ComponentProvideOptions, false, {}, any>;
declare const _default: __VLS_WithTemplateSlots<typeof __VLS_component, __VLS_TemplateResult["slots"]>;
export default _default;
type __VLS_WithTemplateSlots<T, S> = T & {
    new (): {
        $slots: S;
    };
};
