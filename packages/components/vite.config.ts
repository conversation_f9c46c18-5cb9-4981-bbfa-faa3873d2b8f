import vue from '@vitejs/plugin-vue'
import { defineConfig } from 'vite'
import dts from 'vite-plugin-dts'
import removeConsole from 'vite-plugin-remove-console'

export default defineConfig({
  base: './',
  plugins: [
    vue(),
    removeConsole(),
    dts({
      tsconfigPath: './tsconfig.json',
    }),
  ],
  build: {
    lib: {
      entry: ['./index.ts', './auto-import-resolver.ts'],
      name: 'HrtUI',
    },
    rollupOptions: {
      external: ['vue', 'element-plus'],
      output: [
        {
          format: 'es',
          entryFileNames: (chunkInfo) => {
            const name = chunkInfo.name || ''
            return name.includes('node_modules')
              ? `${name.replace('node_modules', 'external')}.mjs`
              : '[name].mjs'
          },
          chunkFileNames: (chunkInfo) => {
            const name = chunkInfo.name || ''
            return name.includes('node_modules')
              ? `${name.replace('node_modules', 'external')}.mjs`
              : '[name].mjs'
          },
          assetFileNames: (assetInfo) => {
            const assetName = assetInfo.name || ''
            if (assetName?.startsWith('src') && assetName?.endsWith('.css')) {
              const lastSlashIndex = assetName.lastIndexOf('/')
              const filePath = assetName.slice(0, lastSlashIndex)
              return `${filePath}/style/index.css`
            }
            return 'index.css'
          },
          preserveModules: true,
          preserveModulesRoot: './',
          exports: 'named',
          dir: 'dist',
        },
      ],
    },
    cssCodeSplit: true,
    target: 'chrome87',
  },
})
