import vue from '@vitejs/plugin-vue'
import { defineConfig } from 'vite'
import dts from 'vite-plugin-dts'
import removeConsole from 'vite-plugin-remove-console'

export default defineConfig({
  base: './',
  plugins: [
    vue(),
    removeConsole(),
    dts({
      tsconfigPath: './tsconfig.json',
    }),
  ],
  build: {
    lib: {
      entry: './index.ts',
      name: 'HrtUI',
    },
    rollupOptions: {
      external: ['vue'],
      output: [
        {
          format: 'es',
          entryFileNames: (chunkInfo) => {
            const name = chunkInfo.name || ''
            return name.includes('node_modules')
              ? `${name.replace('node_modules', 'external')}.mjs`
              : '[name].mjs'
          },
          chunkFileNames: (chunkInfo) => {
            const name = chunkInfo.name || ''
            return name.includes('node_modules')
              ? `${name.replace('node_modules', 'external')}.mjs`
              : '[name].mjs'
          },
          assetFileNames: (assetInfo) => {
            if (assetInfo.name?.endsWith('.css')) {
              return 'index.css'
            }
            return '[name][extname]'
          },
          preserveModules: true,
          preserveModulesRoot: './',
          exports: 'named',
          dir: 'dist',
        },
      ],
    },
  },
})
