import type { SetupContext } from 'vue'

export default function useReusableTemplate() {
  let render: SetupContext['slots']['default']
  const DefineTemplate = {
    setup(_: any, { slots }: { slots: SetupContext['slots'] }) {
      return () => {
        render = slots.default
      }
    },
  }

  const ReusableTemplate = (props: any) => {
    return render?.(props)
  }

  return { DefineTemplate, ReusableTemplate }
}
