{"name": "@hrt/components", "type": "module", "version": "0.0.18", "description": "哈瑞特前端组件库", "main": "index.ts", "module": "index.ts", "typings": "dist/index.d.ts", "files": ["dist/", "locale/"], "scripts": {"build": "vite build", "publish": "pnpm publish --no-git-checks"}, "publishConfig": {"access": "public", "main": "dist/index.mjs", "module": "dist/index.mjs"}, "peerDependencies": {"element-plus": ">=2.9.0", "vue": ">=3.5.0"}}