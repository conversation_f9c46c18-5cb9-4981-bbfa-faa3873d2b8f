import antfu from '@antfu/eslint-config'
import oxlint from 'eslint-plugin-oxlint'

export default antfu({
  ignores: [
    'build',
    '**/build/**',
    'config/*.js',
    'config/*.js/**',
    'static/dist',
    'static/dist/**',
    'static/js',
    'static/js/**',
    'config',
    '**/config/**',
    'dist',
    '**/dist/**',
    '*.env*',
    '**/*.env*/**',
    'src/interface/generate.js',
    'src/interface/generate.js/**',
    'src/interface/swaggerApi.json',
    'src/interface/swaggerApi.json/**',
    '.eslintrc-auto-import.json',
    '**/.eslintrc-auto-import.json/**',
    '.eslintrc.js',
    '**/.eslintrc.js/**',
    '.prettierrc.js',
    '**/.prettierrc.js/**',
    '.tailwind.config.js',
    '**/.tailwind.config.js/**',
  ],
  formatters: true,
  unocss: false,
  vue: true,
  rules: {
    'no-console': 'off',
  },
  plugins: [
    ...oxlint.configs['flat/recommended'],
  ],
})
