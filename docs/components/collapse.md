# Collapse 折叠面板

折叠面板用于将内容区域折叠/展开。

## 基本用法（有底色样式）

:::demo
collapse/basic
:::

## 无底色样式

:::demo
collapse/ghost
:::

## 手风琴模式

:::demo
collapse/accordion
:::

## 属性

### Collapse

| 属性      | 说明               | 类型                          | 默认值 |
| --------- | ------------------ | ----------------------------- | ------ |
| v-model   | 绑定当前激活的面板 | `string` / `number` / `Array` | -      |
| ghost     | 幽灵模式           | `boolean`                     | false  |
| accordion | 手风琴模式         | `boolean`                     | false  |

### HrtCollapse Slots

| 插槽名  | 说明           | 子标签          |
| ------- | -------------- | --------------- |
| default | 自定义默认内容 | HrtCollapseItem |

### HrtCollapse 事件

| 事件名 | 说明                                                                      | 类型                                     |
| ------ | ------------------------------------------------------------------------- | ---------------------------------------- |
| change | 切换当前活动面板，在手风琴模式下其类型是 `string`，在其他模式下是 `array` | `(activeNames: array \| string) => void` |

### HrtCollapseItem

| 属性     | 说明     | 类型                | 默认值 |
| -------- | -------- | ------------------- | ------ |
| title    | 面板标题 | `string`            | -      |
| name     | 唯一标识 | `string` / `number` | -      |
| disabled | 是否禁用 | `boolean`           | false  |

### HrtCollapseItem Slots

| 名称    | 说明     |
| ------- | -------- |
| default | 面板内容 |
