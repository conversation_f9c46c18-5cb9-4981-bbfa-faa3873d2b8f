# Chat Box 聊天框

聊天框通用组件

## 基础用法

:::demo
chat-box/basic
:::

## 自定义工具栏

:::demo 可通过 `tools属性` 指定工具栏要渲染的工具图标列表，可接收的参数详见 [Chat Box Attributes tools](/components/chat-box.html#chat-box-attributes)，同时可通过 `tools插槽` 扩展自定义工具图标。
chat-box/custom-tools
:::

## Chat Box API

### Chat Box Attributes

| 属性名                    | 说明                     | 类型                                                                     | 默认值                                                                         |
| ------------------------- | ------------------------ | ------------------------------------------------------------------------ | ------------------------------------------------------------------------------ |
| tools                     | 工具图标列表             | ^[Array<string>]                                                         | `['image', 'video', 'emoji', 'history', 'medication', 'expression', 'answer']` |
| collapseOnDblclickContent | 双击内容区收起聊天框     | ^[boolean]                                                               | ^[true]                                                                        |
| 其他                      | `HrtChantInput` 所有属性 | [HrtChantInput Props](/components/chat-input.html#chat-input-attributes) | [HrtChantInput Props](/components/chat-input.html#chat-input-attributes)       |

### Chat Box Events

| 事件名                 | 说明                     | 类型                                                                  |
| ---------------------- | ------------------------ | --------------------------------------------------------------------- |
| send                   | 发送消息时触发           | ^[Function]`() => void`                                               |
| fileChange             | 选择图片或视频           | ^[Function]`(type: 'image' \| 'video', files: File[]) => void`        |
| emojiChange            | 点击emoji图标            | ^[Function]`(val: string) => void`                                    |
| chatHistoryClick       | 点击聊天记录tool         | ^[Function]`(e: MouseEvent) => void`                                  |
| medicationClick        | 点击用药助手tool         | ^[Function]`(e: MouseEvent) => void`                                  |
| commonExpressionsClick | 点击常用语tool           | ^[Function]`(e: MouseEvent) => void`                                  |
| questionAnswerClick    | 点击问题答复tool         | ^[Function]`(e: MouseEvent) => void`                                  |
| 其他                   | `HrtChantInput` 所有事件 | [HrtChantInput Events](/components/chat-input.html#chat-input-events) |

### Chat Box slots

| 插槽名  | 说明                                                                                       |
| ------- | ------------------------------------------------------------------------------------------ |
| default | 消息区域插槽                                                                               |
| title   | 自定义聊天标题                                                                             |
| tools   | 自定义工具栏，默认是排列在内置工具之后，若要完全自定义请传入 props `tools=[] \| undefined` |
| input   | 自定义输入框                                                                               |

### Chat Box Exposes

| 方法名 | 说明                                             |
| ------ | ------------------------------------------------ |
| editor | 获取输入框HrtChatInput的实例，自定义输入框时无效 |

## 更多参考

无

## 贡献者

| 责任人 | 贡献者 |
| ------ | ------ |
| 黄涛涛 | 黄涛涛 |
