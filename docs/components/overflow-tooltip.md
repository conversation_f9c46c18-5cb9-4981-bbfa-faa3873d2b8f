# OverflowTooltip 文字溢出提示

用于超长文本溢出时显示tooltip提示。

## 基础用法

:::demo 传入 `content` 即可，会继承 `class` 的样式，可对文本进行无损替换。

overflow-tooltip/basic

:::

## 多行文本

:::demo 由 `rows` 属性来定义展示的行数。

overflow-tooltip/rows

:::

## 插槽slot

:::demo 使用时插槽内容必须为 `行内元素`，可通过 `renderSlotTemplate` 属性使 `tooltip内容` 继承 `插槽Template结构`。

overflow-tooltip/slot

:::

:::warning 大多数情况下，你不需要使用插槽，请使用 `content` 属性。
:::

## OverflowTooltip API

### OverflowTooltip Attributes

| 属性名             | 说明                                  | 类型       | 默认值   |
| ------------------ | ------------------------------------- | ---------- | -------- |
| content            | 展示内容                              | ^[string]  | —        |
| rows               | 展示行数                              | ^[number]  | ^[1]     |
| renderSlotTemplate | 传入slot时tooltip是否渲染slot模板结构 | ^[boolean] | ^[false] |

### OverflowTooltip Slots

| 名称    | 说明           |
| ------- | -------------- |
| default | 自定义默认内容 |

## 贡献者

| 责任人 | 贡献者 |
| ------ | ------ |
| 黄涛涛 | 黄涛涛 |
