# Alert 提示

用于页面中展示重要的提示信息。

## 基础用法

Alert 组件不属于浮层元素，不会自动消失或关闭。Alert 组件提供5种类型，由 type 属性指定，默认值为 info。

:::demo
alert/basic
:::

## 自定义关闭按钮

你可以自定义关闭按钮为文字或其他符号。

:::demo 你可以设置 Alert 组件是否为可关闭状态，关闭按钮的内容以及关闭时的回调函数同样可以定制。closable 属性决定 Alert 组件是否可关闭，该属性接受一个 Boolean，默认为 true。你可以设置 close-text 属性来代替右侧的关闭图标，需要注意的是 close-text 必须是一个字符串。当 Alert 组件被关闭时会触发 close 事件。

alert/close
:::

## 使用图标

你可以通过为 Alert 组件添加图标来提高可读性。

:::demo 通过设置 show-icon 属性来显示 Alert 的 icon，这能更有效地向用户展示你的显示意图。或者你可以使用 icon slot 自定义 icon 内容。

alert/with-icon
:::

## 卡片样式

你可以设置 Alert 组件为卡片样式。

:::demo 通过设置 isCard 属性为 true 来使用卡片样式的 Alert。卡片样式的 Alert 具有阴影效果和顶部边框色，更加突出显示。

alert/card
:::

## 属性

### Alert 属性

| 属性名      | 说明               | 类型                                                       | 默认值    |
| ----------- | ------------------ | ---------------------------------------------------------- | --------- |
| title       | Alert 标题         | `string`                                                   | —         |
| type        | Alert 类型         | `'primary' \| 'success' \| 'warning' \| 'info' \| 'error'` | `'info'`  |
| description | 描述性文本         | `string`                                                   | —         |
| closable    | 是否可以关闭       | `boolean`                                                  | `true`    |
| center      | 文字是否居中       | `boolean`                                                  | `false`   |
| close-text  | 自定义关闭按钮文本 | `string`                                                   | —         |
| show-icon   | 是否显示类型图标   | `boolean`                                                  | `false`   |
| effect      | 主题样式           | `'light' \| 'dark'`                                        | `'light'` |
| isCard      | 是否卡片样式       | `boolean`                                                  | false     |

### Alert 事件

| 事件名 | 说明                    | 类型                      |
| ------ | ----------------------- | ------------------------- |
| close  | 关闭 Alert 时触发的事件 | `(event?: Event) => void` |
| open   | 打开 Alert 时触发的事件 | `() => void`              |

### Alert 插槽

| 插槽名  | 说明           |
| ------- | -------------- |
| default | Alert 内容描述 |
| title   | 标题的内容     |
| icon    | 图标的内容     |

## 更多参考

Element-Plus [Alert](https://element-plus.org/zh-CN/component/alert.html)

## 贡献者

| 责任人 | 贡献者 |
| ------ | ------ |
|        |        |
