# Breadcrumb 面包屑

显示当前页面的路径，快速返回之前的任意页面。

## 基础用法

在 `hrt-breadcrumb` 中使用 `hrt-breadcrumb-item` 标签表示从首页开始的每一级。 该组件接受一个 String 类型的参数 separator 来作为分隔符。 默认值为 '/'。

:::demo
breadcrumb/basic
:::

## 图标分隔符

通过设置 `separator-class` 可使用相应的 `iconfont` 作为分隔符，注意这将使 `separator` 失效。

:::demo
breadcrumb/seperator
:::

## 属性

### Breadcrumb 新增属性

| 属性    | 说明          | 类型     | 默认值 |
| ------- | ------------- | -------- | ------ |
| v-model | 当前选中value | `string` |        |

### BreadcrumbItem 新增属性

| 属性  | 说明              | 类型     | 默认值 |
| ----- | ----------------- | -------- | ------ |
| value | 当前选项绑定value | `string` |        |

### Breadcrumb 事件

| 事件名称 | 说明               | 类型                   |
| -------- | ------------------ | ---------------------- |
| click    | 面包屑点击点击事件 | `(key:string) => void` |
