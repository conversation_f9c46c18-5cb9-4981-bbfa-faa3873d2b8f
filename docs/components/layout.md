# Layout 布局

通过基础的 12 分栏，可以快速简便地创建布局。

> 列的基本单位为1，最多12个，最少0个

## 基础布局

使用列创建基础网格布局。

通过 `row` 和 `col` 组件，并通过 `col` 组件的 `span` 属性我们就可以自由地组合布局。

::: demo
layout/basic
:::

## 分栏间隔

支持列间距。

行提供 `gutter` 属性来指定列之间的间距，其默认值为16。

::: demo
layout/gutter
:::

## 偏移列

您可以指定列偏移量。

通过制定 `col` 组件的 `offset` 属性可以指定分栏偏移的栏数。

:::demo
layout/offset
:::

## 对齐方式

默认使用 `flex` 布局来对分栏进行灵活的对齐。

您可以通过 `justify` 属性来定义子元素的排版方式，其取值为 `start`、`center`、`end`、`space-between`、`space-around`或`space-evenly`。

:::demo
layout/justify
:::
