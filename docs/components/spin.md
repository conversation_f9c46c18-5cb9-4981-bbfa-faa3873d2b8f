# Spin 加载

## 基本用法

:::demo
spin/basic
:::

## 尺寸

`size` 属性用于设置加载图标的大小。

:::demo
spin/size
:::

## 排版方式

`direction` 属性用于设置加载图标的方向。

:::demo
spin/direction
:::

## 文本

`text` 属性用于设置加载图标的文本。

:::demo
spin/text
:::

## 指令

Spin 组件也可以通过指令的方式使用。

可以通过 `v-spin` 指令来控制元素的显示和隐藏。v-spin 指令可以接受一个布尔值或者一个对象作为参数。

如果 `v-spin` 指令的值为 `true`，则会显示加载图标的元素；如果 `v-spin` 指令的值为 `false`，则会隐藏加载图标的元素。

如果 `v-spin` 指令的值为一个对象，则可以通过 `size`、`direction` 和 `text` 属性来设置加载图标的大小、方向和文本。`show` 属性用于控制元素的显示和隐藏。

:::demo
spin/directive
:::

## API

### 属性

| 属性名       | 说明   | 类型                               | 默认值          |
|-----------|------|----------------------------------|--------------|
| size      | 尺寸   | `'small' \| 'medium' \| 'large'` | `'medium'`   |
| direction | 排版方式 | `'horizontal' \| 'vertical'`     | `'vertical'` |
| text      | 文本   | `string`                         | `'加载中...'`   |

## 贡献者

| 责任人 | 贡献者 |
|:----|:----|
| 刘浪  | 刘浪  |
