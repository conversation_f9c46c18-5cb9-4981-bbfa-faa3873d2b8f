# Transfer 穿梭框

## 基础用法

Transfer 的数据通过 data 属性传入。 数据需要是一个对象数组，每个对象有以下属性：key 为数据的唯一性标识，label 为显示文本，disabled 表示该项数据是否禁止被操作。 目标列表中的数据项会同步到绑定至 v-model 的变量，值为数据项的 key 所组成的数组。 当然，如果希望在初始状态时目标列表不为空，可以像本例一样为 v-model 绑定的变量赋予一个初始值。

:::demo
transfer/basic
:::

## 可搜索过滤

在数据很多的情况下，可以对数据进行搜索和过滤。

设置 filterable 为 true 即可开启搜索模式。 默认情况下，若数据项的 label 属性包含搜索关键字，则会在搜索结果中显示。 你也可以使用 filter-method 定义自己的搜索逻辑。 filter-method 接收一个方法，当搜索关键字变化时，会将当前的关键字和每个数据项传给该方法。 若方法返回 true，则会在搜索结果中显示对应的数据项。

:::demo
transfer/filter
:::

## 简单模式

设置 simple 为 true 即可开启简单模式。简单模式点击数据列表，即可完成数据穿梭。

:::demo
transfer/simple
:::

## 其他

更多用法参考Element-Plus [Transfer](https://element-plus.org/zh-CN/component/transfer.html)

## API

### Props

| 参数                | 说明         | 类型      | 默认值  |
| ------------------- | ------------ | --------- | ------- |
| `simple`^(二封属性) | 是否简单模式 | `boolean` | `false` |

其他属性参考Element-Plus [Transfer](https://element-plus.org/zh-CN/component/transfer.html#transfer-attributes)

### Events

参考Element-Plus [Transfer](https://element-plus.org/zh-CN/component/transfer.html#transfer-events)

### Slots

参考Element-Plus [Transfer](https://element-plus.org/zh-CN/component/transfer.html#transfer-slots)
