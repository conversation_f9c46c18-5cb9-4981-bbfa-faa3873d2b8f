# CollapseText 折叠文本

折叠文本用于将文本区域折叠/展开。

## 基本用法

通过 `maxLine` 属性设置最大行数，超过最大行数后，文本会被折叠。文案内容通过 `text` 属性设置。

:::demo
collapse-text/basic
:::

## 自定义文本

通过 `expandText` 和 `collapseText` 属性设置展开和收起文本。

:::demo
collapse-text/custom
:::

## 属性

### HrtCollapseText 属性

| 属性         | 说明         | 类型     | 默认值 |
| ------------ | ------------ | -------- | ------ |
| text         | 文本         | `string` | -      |
| maxLine      | 最大行数     | `number` | 1      |
| expandText   | 展开文本文案 | `string` | 展开   |
| collapseText | 收起文本文案 | `string` | 收起   |

## 贡献者

| 负责人 | 贡献者 |
| ------ | ------ |
| 刘浪   | 刘浪   |
