# TreeSelect 树形选择

含有下拉菜单的树形选择器，结合了 `tree` 和 `select` 两个组件的功能。

## 基础用法

树状选择器

:::demo

tree-select/basic
:::

## 禁用状态

:::demo 使用 `disabled` 属性禁用树形选择器。

tree-select/disabled
:::

## 有禁用选项

:::demo 在传入的`data`中指定`disabled`属性为`true`的选项将会被禁用。

tree-select/disabled-option
:::

## 多选

:::danger 在使用 show-checkbox 时，因为 `check-on-click-leaf` 默认值为 true， 最后一个树节点可以通过点击节点进行勾选。
:::

:::demo 通过点击或复选框选择多个选项。

tree-select/multiple
:::

## 可筛选

:::demo 使用关键字筛选或自定义筛选方法。 `filterMethod` 可以自定义数据筛选的方法， `filterNodeMethod` 可以自定义节点数据筛选的方法。

tree-select/filterable
:::

## TreeSelect API

### TreeSelect Attributes

由于这个组件是 `tree` 和 `select` 的结合体，他们的原始属性未被更改，故不在此重复。请跳转查看原组件的相应文档。

| 属性                                                            | 方法                                                            | 事件                                                            | 插槽                                                            |
| --------------------------------------------------------------- | --------------------------------------------------------------- | --------------------------------------------------------------- | --------------------------------------------------------------- |
| [tree](https://element-plus.org/zh-CN/component/tree.html#属性) | [tree](https://element-plus.org/zh-CN/component/tree.html#方法) | [tree](https://element-plus.org/zh-CN/component/tree.html#事件) | [tree](https://element-plus.org/zh-CN/component/tree.html#插槽) |
| [select](/components/select.html#select-api)                    | [select](/components/select.html#select-exposes)                | [select](/components/select.html#select-events)                 | [select](/components/select.html#select-slots)                  |

## 更多参考

Element-Plus

- [tree](https://element-plus.org/zh-CN/component/tree.html)
- [select](https://element-plus.org/zh-CN/component/select.html)
- [tree-select](https://element-plus.org/zh-CN/component/tree-select.html)

## 贡献者

| 责任人 | 贡献者 |
| ------ | ------ |
| 黄涛涛 | 黄涛涛 |
