# Notice 通知

基于Element-Plus的Notification组件，悬浮出现在页面角落，显示全局的通知提醒消息。

## 不同主题 ^(二封属性)

:::demo 使用 `theme` 属性来定义通知的主题，可选值有 `light(默认)`、`dark`。

notice/basic

:::

## 自定义图标

:::demo 使用 `icon` 属性来自定义图标。

notice/with-icon

:::

## 不同类型的通知

:::demo `HrtNotice` 准备了5种通知类型：`primary`,`success`, `warning`, `info`, `error`。 他们可以设置 `type` 字段来修改，除上述的五个值之外的值会被忽略。 同时，我们也为 `HrtNotice` 的各种 `type` 注册了单独的方法，可以在不传入 `type` 字段的情况下像 `open3` 和 `open4` 那样直接调用。 `primary` 已被添加到 ^(2.9.11)。

notice/different-types

:::

## 快捷通知

:::demo 可通过调用 `HrtNotice(string)` `HrtNotice.success(string)` 、`HrtNotice.info(string)`、`HrtNotice.warning(string)`、`HrtNotice.error(string)`、`HrtNotice.primary(string)`方法来快捷不同类型的快捷通知。

notice/quick

:::

## 手动关闭

:::demo `HrtNotice` 会返回一个实例，可调用实例的 `close()` 方法来手动关闭。

notice/close

:::

## 手动关闭所有通知

:::demo 可以通过调用 `HrtNotice.closeAll()` 来手动关闭系统内所有通知。

notice/close-all

:::

## Notice API

### Notice Attributes

| 属性名            | 说明                                             | 类型                                                                             | 默认值   |
| ----------------- | ------------------------------------------------ | -------------------------------------------------------------------------------- | -------- |
| theme ^(二封属性) | 主题                                             | ^[light/dark]                                                                    | ^[light] |
| icon              | 自定义图标。 若设置了 `type`，则 `icon` 会被覆盖 | ^[string/Component]                                                              | —        |
| type              | 通知的类型                                       | ^[enum]`'primary' (2.9.11) \| 'success' \| 'warning' \| 'info' \| 'error' \| ''` | —        |

## 更多参考

Element-Plus [notification](https://element-plus.org/zh-CN/component/notification.html)

## 贡献者

| 责任人 | 贡献者 |
| ------ | ------ |
| 黄涛涛 | 黄涛涛 |
