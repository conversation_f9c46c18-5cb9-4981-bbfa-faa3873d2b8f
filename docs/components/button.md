# Button 按钮

常用的操作按钮。

## 基础用法

基础的按钮用法。

:::demo 使用 `type`、`plain`、`round` 和 `circle` 来定义按钮的样式。
button/basic
:::

## 禁用状态

你可以使用 `disabled` 属性来定义按钮是否被禁用。

:::demo 使用 `disabled` 属性来控制按钮是否为禁用状态。 该属性接受一个 `Boolean` 类型的值。
button/disabled
:::

## 调整尺寸

除了默认的大小，按钮组件还提供了几种额外的尺寸可供选择，以便适配不同的场景。

使用 `size` 属性额外配置尺寸，可使用 `large` 和 `small` 两种值。

:::demo
button/size
:::

## 块级按钮

::: demo 按钮可以通过设置 `block` 属性来变为块级元素。
button/block
:::

## 加载状态

对于加载状态的处理有2种方式：

使用 `loading` 属性来控制按钮的加载状态。

使用 `inner-loading` 属性设置为 `true` 来开启由按钮内部控制自己的加载状态，当点击按钮时会自动进入加载状态, `click` 会抛出 `close` 方法用于关闭加载状态。

使用 `asyncClick` 属性传入一个异步函数，有按钮本身控制加载状态，函数执行中加载状态为 `true`，异步执行完成后加载状态为 `false`

:::demo
button/loading
:::
