# Steps 步骤条

引导用户按照流程完成任务的分步导航条， 可根据实际应用场景设定步骤，步骤不得少于 2 步。

## 横向步骤条-左图右文

默认横向步骤条，根据界面宽度选择合适的排版方式

:::demo 设置 `active` 属性，接受一个 `Number`，表明步骤的 index，从 `0` 开始。对 `HrtStep` 设置 `finish-status` 属性以改变已完成步骤的状态，值为 `success` 或 `error`。

steps/horizontal
:::

## 横向步骤条-上图下文

:::demo 通过 `align-center` 属性设置icon和文案的排版方式，接收一个`boolean`值，默认为`false`。

steps/align-center
:::

## 卡片风格的步骤条

::: warning 设置 `card` 可应用卡片风格，该条件下 `align-center` /` description` / `direction` 都将失效
:::

:::demo 可设置 `card-seq` 属性启用序号和icon，接收一个`boolean`值，默认为`false`。

steps/card
:::

## 纵向步骤条

:::demo 通过 `direction` 属性设置步骤条的排列方向，可选值为 `horizontal` 或 `vertical`。

steps/vertical
:::

## 自定义图标

:::demo 通过设置 `hrt-steps` 的 `icon` 属性来设置所有步骤的图标， 图标的类型可以参考 Icon 组件的文档， 除此以外，还能通过具名 `slot` 来使用自定义的图标。

steps/icon1
:::

## 为每个步骤单独自定义图标

:::demo 通过设置 `hrt-step` 的 `icon` 属性来为每个步骤单独设置图标， 图标的类型可以参考 Icon 组件的文档， 除此以外，还能通过具名 `slot` 来使用自定义的图标。

steps/icon2
:::

## Steps API

### Steps Attributes

| 属性名                  | 说明                                                    | 类型                                | 默认值        |
| ----------------------- | ------------------------------------------------------- | ----------------------------------- | ------------- |
| active / v-model:active | 设置当前激活步骤，卡片模式下必须为 ^[v-model:active]    | ^[number]                           | ^[0]          |
| direction               | 显示方向                                                | ^[enum]`'vertical' \| 'horizontal'` | ^[horizontal] |
| alignCenter             | 图标与文字是否上下排列并居中对齐                        | ^[boolean]                          | ^[false]      |
| card                    | 是否为卡片模式                                          | ^[boolean]                          | ^[false]      |
| cardSeq                 | 卡片模式是否展示步骤序号，`为false时同时会禁用icon展示` | ^[boolean]                          | ^[false]      |
| icon                    | 为所有Step组件自定义图标，也支持slot方式写入            | ^[string/Component]                 | —             |

### Steps Events

| 事件名 | 说明                                     | 类型                               |
| ------ | ---------------------------------------- | ---------------------------------- |
| change | 步骤改变的时候触发，`仅在卡片模式下生效` | ^[function]`(val: number) => void` |

### Steps Slots

| 插槽名  | 说明                     | 子标签  |
| ------- | ------------------------ | ------- |
| default | 默认插槽                 | HrtStep |
| icon    | 所有Step的自定义图标插槽 | —       |

## Step API

### Step Attributes

| 属性名       | 说明                                         | 类型                          | 默认值     |
| ------------ | -------------------------------------------- | ----------------------------- | ---------- |
| title        | 标题                                         | ^[string]                     | ''         |
| description  | 描述文案                                     | ^[string]                     | ''         |
| finishStatus | 步骤结束后的状态                             | ^[enum]`'success' \| 'error'` | ^[success] |
| icon         | Step 组件的自定义图标。 也支持 slot 方式写入 | ^[string/Component]           | —          |

### Step Slots

| 插槽名      | 说明           |
| ----------- | -------------- |
| icon        | 自定义图标     |
| title       | 自定义标题     |
| description | 自定义描述文案 |

## 更多参考

完全重写的 Steps 组件，无其他文档参考。

## 贡献者

| 责任人 | 贡献者 |
| ------ | ------ |
| 黄涛涛 | 黄涛涛 |
