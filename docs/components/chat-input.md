# Chat Input 聊天输入框

基于 `quill` 封装的聊天输入框。

## 基础用法

:::demo

chat-input/basic

:::

## 发送拦截

:::demo 使用 `beforeSend` 属性拦截发送，返回一个 `boolean` 或 `Promise<boolean>`，为 `false` 时取消发送。
chat-input/before-send
:::

## Chat Input API

### Chat Input Attributes

| 属性名         | 说明                                                                 | 类型                                           | 默认值                 |
| -------------- | -------------------------------------------------------------------- | ---------------------------------------------- | ---------------------- |
| config         | 编辑器配置项 [quill options](https://quilljs.com/docs/configuration) | ^[object]                                      | —                      |
| noFooter       | 不渲染编辑器footer                                                   | ^[boolean]                                     | ^[false]               |
| valueFormatter | 编辑器值的格式化函数                                                 | ^[Function]`(val: string) => string`           | `(val: string) => val` |
| beforeSend     | 发送前的回调，返回 `false` 取消发送                                  | ^[Function]`() => boolean \| Promise<boolean>` | `() => true`           |
| autoFocus      | 初始化完成后自动聚焦                                                 | ^[boolean]                                     | ^[false]               |

### Chat Input Events

| 事件名 | 说明                  | 类型                                 |
| ------ | --------------------- | ------------------------------------ |
| send   | enter或点击发送时触发 | ^[function]`(val: string) => void`   |
| change | 输入内容变化时触发    | ^[function]`(val: string) => void`   |
| focus  | 输入框获取焦点时触发  | ^[function]`(e: FocusEvent) => void` |
| blur   | 输入框失去焦点时触发  | ^[function]`(e: FocusEvent) => void` |

### Chat Input Slots

| 插槽名 | 说明               |
| ------ | ------------------ |
| footer | 自定义编辑器footer |

### Chat Input Exposes

| 属性名 | 说明                                           | 类型      |
| ------ | ---------------------------------------------- | --------- |
| editor | [quill](https://quilljs.com/docs/api) 实例对象 | ^[object] |

## 更多参考

[quill](https://quilljs.com/docs/api)

## 贡献者

| 责任人 | 贡献者 |
| ------ | ------ |
| 黄涛涛 | 黄涛涛 |
