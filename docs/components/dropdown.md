# Dropdown 下拉菜单

将动作或菜单折叠到下拉菜单中。

## 基础用法

悬停在下拉菜单上以展开更多操作。

:::demo 通过组件 `slot` 来设置下拉触发的元素以及需要通过具名 `slot` 为 `dropdown` 来设置下拉菜单。 默认情况下，只需要悬停在触发菜单的元素上即可，无需点击也会显示下拉菜单。

dropdown/basic
:::

## 按钮样式

:::demo 通过 `is-button` 属性可设置触发对象为按钮样式，同时适配element原生 `button-props` 属性，可通过该属性定义button的属性。

dropdown/button
:::

## Dropdown API

### Dropdown Attributes

| 属性名              | 说明                   | 类型       | 默认值      |
| ------------------- | ---------------------- | ---------- | ----------- |
| title^(二封属性)    | 下拉菜单文案           | ^[string]  | ^[下拉菜单] |
| isButton^(二封属性) | 下拉菜单是否为按钮形式 | ^[boolean] | ^[false]    |

## 更多参考

Element-Plus [dropdown](https://element-plus.org/zh-CN/component/dropdown.html)

## 贡献者

| 责任人 | 贡献者 |
| ------ | ------ |
| 黄涛涛 | 黄涛涛 |
