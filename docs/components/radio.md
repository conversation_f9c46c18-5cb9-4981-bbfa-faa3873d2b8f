# Radio 单选框

在一组备选项中进行单选

## 基础用法

:::demo
radio/basic
:::

## 支持点击取消选中

:::demo
radio/toggle
:::

## 禁用

`disabled` 属性可以用来控制单选框的禁用状态。

你只需要为单选框设置 `disabled` 属性就能控制其禁用状态。

:::demo
radio/disabled
:::

## 排列方向

你可以通过 `direction` 属性设置单选框的排列方向。

:::demo
radio/direction
:::

## 胶囊按钮

你可以让单选框看起来像一个按钮一样。

只需要把 `HrtRadio` 元素换成 `HrtRadioButton` 元素即可， 此外 还提供了 `size` 属性用来控制单选框的大小。

:::demo
radio/button
:::

## API

### Radio Attributes

| 属性名                | 说明                                                     | 类型                                  | 默认值  |
| --------------------- | -------------------------------------------------------- | ------------------------------------- | ------- |
| model-value / v-model | 选中项绑定值                                             | `string` / `number` / `boolean`       | —       |
| value                 | 单选框的值                                               | `string` / `number` / `boolean`       | —       |
| label                 | 单选框的 label，如果 value 没有值，label 作为 value 使用 | `string` / `number` / `boolean`       | —       |
| disabled              | 是否禁用单选框                                           | `boolean`                             | `false` |
| border                | 是否显示边框                                             | `boolean`                             | `false` |
| size                  | 单选框的尺寸                                             | `'large'` \| `'default'` \| `'small'` | —       |
| name                  | 原始 name 属性                                           | string                                | —       |
| enableToggle          | 是否能点击取消                                           | `boolean`                             | `false` |

### Radio Events

| 事件名 | 说明                   | 类型                                           |
| ------ | ---------------------- | ---------------------------------------------- |
| change | 绑定值变化时触发的事件 | `(value: string \| number \| boolean) => void` |

### Radio Slots

| 插槽名  | 说明           |
| ------- | -------------- |
| default | 自定义默认内容 |

### RadioGroup Attributes

| 属性名                | 说明                                     | 类型                            | 默认值         |
| --------------------- | ---------------------------------------- | ------------------------------- | -------------- |
| model-value / v-model | 绑定值                                   | `string` / `number` / `boolean` | —              |
| size                  | 单选框按钮或边框按钮的大小               | `string`                        | `'default'`    |
| disabled              | 是否禁用                                 | `boolean`                       | `false`        |
| validate-event        | 输入时是否触发表单的校验                 | `boolean`                       | `true`         |
| aria-label            | 与 RadioGroup 中的 `aria-label` 属性相同 | `string`                        | —              |
| name                  | 原生 `name` 属性                         | `string`                        | —              |
| id                    | 原生 `id` 属性                           | `string`                        | —              |
| direction             | 排列方向                                 | `'horizontal' \| 'vertical'`    | `'horizontal'` |

### RadioGroup Events

| 事件名 | 说明                   | 类型                                           |
| ------ | ---------------------- | ---------------------------------------------- |
| change | 绑定值变化时触发的事件 | `(value: string \| number \| boolean) => void` |

### RadioButton Attributes

| 属性名       | 说明                                                  | 类型                            | 默认值    |
| ------------ | ----------------------------------------------------- | ------------------------------- | --------- |
| value        | 单选框的值                                            | `string` / `number` / `boolean` | —         |
| label        | 单选框的 label，如果没有 value，label 作为 value 使用 | `string` / `number` / `boolean` | —         |
| disabled     | 是否禁用单选框                                        | `boolean`                       | `false`   |
| name         | 原生 name 属性                                        | `string`                        | —         |
| text-color   | 按钮形式的 Radio 激活时的文本颜色                     | `string`                        | `#ffffff` |
| fill         | 按钮形式的 Radio 激活时的填充色和边框色               | `string`                        | `#409eff` |
| selectedType | 按钮类型                                              | `'fill' \| 'outline'`           | `'fill'`  |

### RadioButton Slots

| 插槽名  | 说明         |
| ------- | ------------ |
| default | 默认插槽内容 |

## 贡献者

| 责任人 | 贡献者 |
| ------ | ------ |
| 刘浪   | 刘浪   |
