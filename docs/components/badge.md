# Badge 徽标数

按钮和图标上的数字或状态标记。

## 基础用法

可以用来展示新消息的数量。

数量值可接受 Number

:::demo
badge/basic
:::

## 最大值

你还可以自定义最大值

由 `max` 属性定义，接受 `Number` 值。 请注意，仅在值也是 `Number` 时起作用。

:::demo
badge/max
:::

## 小红点

通过一个小红点标记来告知用户有新内容。

使用 `is-dot` 属性。 是个布尔值。

:::demo
badge/is-dot
:::

## 偏移量

设置徽章点的偏移，格式是[顶部,左]， 代表状态点从顶部和默认位置左侧的偏移。

:::info
当顶部和左侧偏移量数值一致时可直接传数值 `:offset="4"`
:::

:::demo
badge/offset
:::

## 颜色

徽标颜色可以通过 `color` 属性来设置。

:::demo
badge/color
:::

## 文字图标

:::demo
badge/is-absolute
:::

## 属性

| 属性       | 说明                    | 类型                     | 默认值  |
| ---------- | ----------------------- | ------------------------ | ------- |
| value      | 显示值                  | `number`                 | -       |
| max        | 最大值，超出显示为 max+ | `number`                 | -       |
| isDot      | 是否显示为小圆点        | `boolean`                | `false` |
| hidden     | 是否隐藏                | `boolean`                | `false` |
| isAbsolute | 是否绝对定位            | `boolean`                | `true`  |
| color      | 背景颜色                | `CSSProperties['color']` | -       |
| offset     | 偏移量                  | `number[] \| number`     | -       |

## 插槽

| 名称    | 说明         |
| ------- | ------------ |
| default | 徽标包裹内容 |
