# Cascader 级联选择器

当一个数据集合有清晰的层级结构时，可通过级联选择器逐级查看并选择。

:::tip 默认情况下， Cascader的宽度为100%
:::

## 基础用法

有两种触发子菜单的方式

:::demo 只需为Cascader的 `options` 属性指定选项数组即可渲染出一个级联选择器。 通过 `props.expandTrigger` 属性控制子节点的展开方式

cascader/basic

:::

## 尺寸

:::demo Cascader提供了三种尺寸供选择，分别是 `large`、`default`、`small`， 默认为 `''`

cascader/sizes

:::

## 禁用状态

:::demo 使用 `disabled` 属性禁用级联选择器

cascader/disabled

:::

## 有禁用选项

通过在数据源中设置 `disabled` 字段来声明该选项是禁用的

:::demo 在默认情况下，Cascader 会检查数据中每一项的`disabled`字段是否为`true`，如果你的数据中表示禁用含义的字段名不为`disabled`，可以通过`props.disabled`属性来指定（详见下方 API 表格）。 当然，`value`、`label`和`children`这三个字段名也可以通过同样的方式指定。

cascader/option-disabled

:::

## 下拉选项最多展示行数

:::demo 在默认情况下，Cascader 下拉项最多展示8行

cascader/max-rows

:::

## 下拉框位置

:::demo 在默认情况下，Cascader 下拉框位置为 `bottom-start`，可通过设置 `placement` 属性来改变下拉框位置，在某些场景下，下拉框可能超出边界，此时会自动移动到正确的位置。

cascader/placement

:::

## 多选

在标签中添加 `:props="props"` 并设置 `props = { multiple: true }` 来开启多选模式。

正确用法:

```vue
<script lang="ts" setup>
const props = { multiple: true }
</script>

<template>
  <el-cascader :props="props" />
</template>
```

错误用法:

```vue
<template>
  <!--  Object literal binging here is invalid syntax for cascader  -->
  <el-cascader :props="{ multiple: true }" />
</template>
```

:::demo 使用多选时，所有选中的标签将默认显示。 您可以设置 `collapse = true` 将选中的标签折叠。 您可以设置 `max-collapse-tags` 来显示最大tag数量，默认1。 您可以使用 `collapse-tags-tooltip` 属性来启用鼠标悬停折叠文字以显示具体所选值的行为。

cascader/multiple

:::

## 可搜索

可以快捷地搜索选项并选择。

:::demo 通过添加`filterable`来启用过滤。 Cascader 会匹配所有节点的标签和它们的亲节点的标签，是否包含有输入的关键字。 你也可以用`filter-method`自定义搜索逻辑，接受一个函数，第一个参数是节点`node`，第二个参数是搜索关键词`keyword`，通过返回布尔值表示是否命中。

cascader/filterable

:::

## Cascader API

### Cascader Attributes

| 属性名                           | 说明                            | 类型                                     | 默认值  |
| -------------------------------- | ------------------------------- | ---------------------------------------- | ------- |
| size                             | cascader尺寸                    | ^[enum]`'large'\|'default'\|'small'\|''` | ^['']   |
| fit-input-width ^(二封属性)      | 下拉框宽度是否与输入框保持一致  | ^[boolean]                               | ^[true] |
| max-collapse-tags-tooltip-height | 多选时折叠标签tooltip的最大高度 | ^[number/string]                         | ^[300]  |

## 更多参考

Element-Plus [cascader](https://element-plus.org/zh-CN/component/cascader.html)

## 贡献者

| 责任人 | 贡献者 |
| ------ | ------ |
| 黄涛涛 | 黄涛涛 |
