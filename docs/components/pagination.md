# Pagination 分页

当数据量过多时，使用分页分解数据。

## 基础用法

最多显示 5 页内容，当数量多时默认显示前 5 页以及最后 1 页。若选中的是中间的页数，则除了固定显示第 1 页和最后 1 页之外，显示以选中的页数为中间数，向前和向后各 2 页。

:::demo
pagination/basic
:::

## 精简分页器

如无需要可以舍去页面显示数量调整的组件

:::demo
pagination/streamline
:::

## 简约分页器

如无需要可以舍去页面显示数量调整的组件

:::demo
pagination/simple
:::

## 迷你分页器

只显示当前页面总数，可直接跳转到输入

:::demo
pagination/mini
:::

## API

### 属性

| 属性名     | 说明                         | 类型       | 默认值                                    |
| ---------- | ---------------------------- | ---------- | ----------------------------------------- |
| pageSizes  | 每页显示个数选择器的选项设置 | `number[]` | [10, 25, 50, 100]                         |
| layout     | 组件布局，子组件名用逗号分隔 | `string`   | 'total, sizes, prev, pager, next, jumper' |
| background | 是否为分页按钮添加背景色     | `boolean`  | true                                      |
| mini       | 是否为迷你版                 | `boolean`  | false                                     |

其他属性可见<https://element-plus.org/zh-CN/component/pagination.html#api>
