# Tabs 标签页

## 基本用法

:::demo
tabs/basic
:::

## 可拖拽

通过 `sortable` 属性，开启拖拽排序功能， 拖拽成功触发 `sort(newIndex, oldIndex)` 事件, 当前组件只支持未被禁用和可关闭tab的拖拽功能
:::demo
tabs/sortable
:::

## 卡片样式

:::demo
tabs/card
:::

## 图标样式

:::demo
tabs/icon
:::

## 自定义标签页

:::demo
tabs/custom-label
:::

## 标签页翻页

可通过 `pageTurning` 属性开启标签页翻页，开启后标签页会显示翻页按钮，点击翻页按钮可以切换到上一个或下一个未被禁用的标签页。

:::demo
tabs/page
:::

## 可关闭标签页

:::demo
tabs/closeable
:::

## 胶囊样式

可通过设置 `type` 属性值为 `capsule` 设置标签页为胶囊样式。

:::demo
tabs/capsule
:::

## 文字标签样式

可通过设置 `type` 属性值为 `text` 设置标签页为胶囊样式。

:::demo
tabs/text
:::

## 竖向标签样式

可通过设置 `type` 属性值为 `slide` 设置标签页为胶囊样式。

:::demo
tabs/slide
:::

## API

### HrtTabs Props

| 属性              | 说明               | 类型                                                  | 默认值    |
| ----------------- | ------------------ | ----------------------------------------------------- | --------- |
| v-model           | 当前激活的标签页   | `string`                                              | --        |
| type              | 标签页类型         | `'basic' \| 'card' \| 'capsule' \| 'text' \| 'slide'` | `'basic'` |
| pageTurning       | 是否开启标签页翻页 | `boolean`                                             | `false`   |
| pageTurningNumber | 标签页翻页数量     | `number`                                              | `3`       |
| closable          | 是否可关闭标签页   | `boolean`                                             | `false`   |
| sortable          | 是否开启标签拖拽   | `boolean`                                             | `false`   |

### HrtTabs Slot

| 名称  | 说明       |
| ----- | ---------- |
| label | 标签页导航 |

### HrtTabPane Props

| 属性      | 说明             | 类型      | 默认值  |
| --------- | ---------------- | --------- | ------- |
| label     | 标签页标题       | `string`  | --      |
| name      | 标签页名称       | `string`  | --      |
| disabled  | 是否禁用标签页   | `boolean` | `false` |
| closeable | 是否可关闭标签页 | `boolean` | `false` |

### HrtTabPane Slot

| 名称    | 说明       |
| ------- | ---------- |
| default | 标签页内容 |
