# Slider 滑块

通过拖动滑块在一个固定区间内进行选择

## 基础用法

在拖动滑块时，显示当前值

通过设置绑定值自定义滑块的初始值

:::demo
slider/basic
:::

## 带有输入框的滑块

通过输入框输入来改变当前的值。

设置 `show-input` 属性会在右侧显示一个输入框

:::demo
slider/input
:::

## 显示标记

设置 `marks` 属性可以在滑块上显示标记。

:::demo
slider/marks
:::

## 范围选择

你还可以选择一个范围值

配置 `range` 属性以激活范围选择模式，该属性的绑定值是一个数组，由最小边界值和最大边界值组成。

:::demo
slider/range
:::

## 其他

更多用法参考Element-Plus [Slider](https://element-plus.org/zh-CN/component/slider.html)

## API

### Props

参考Element-Plus [Slider](https://element-plus.org/zh-CN/component/slider.html#%E5%B1%9E%E6%80%A7)

### Events

参考Element-Plus [Slider](https://element-plus.org/zh-CN/component/slider.html#%E4%BA%8B%E4%BB%B6)
