---
layout: ComponentLayout
---

# Table 表格

用于展示多条结构类似的数据， 可对数据进行排序、筛选、对比或其他自定义操作。

## 基础表格

基础的表格展示用法。

当 `HrtTable` 元素中注入 `data` 对象数组后，在 `HrtTableColumn` 中用 `prop` 属性来对应对象中的键名即可填入数据，用 `label` 属性来定义表格的列名。 可以使用 `width` 属性来定义列宽。

:::demo
table/basic
:::

## 操作列

表格中常常需要对数据进行操作，可以通过自定义列内容来实现操作按钮。
设置`action-flex`开启弹性操作列， 通过`action-fxied-number`设置固定显示按钮的数量， `axtion-more-text`可修改展开按钮文案，默认是`···`

注意， 操作按钮建议使用 `HrtButton,  type="text"`, 使用其他自定义实现，可能需要自己处理部分样式问题

:::demo
table/action
:::

## 斑马线

设置 `stripe` 属性可以开启斑马线。

:::demo
table/stripe
:::

## 复选框样式

你也可以选择多行。

实现多选非常简单: 手动添加一个 `HrtTableColumn`，设 `type` 属性为 `selection` 即可；并且可以通过 `selectable` 属性来指定哪些行可以被选择。

:::demo
table/multi-select
:::

## 批量操作

对表格数据进行选择， 并进行批量操作。

在表格中设置 `selection` 属性即可开启批量选择功能， 接受一个 `Boolean`，默认为 `false`。
可通过设置 `#footer` 插槽来显示批量操作的按钮。

:::demo
table/select
:::

## 可排序

对表格进行排序，可快速查找或对比数据。

在列中设置 `sortable` 属性即可实现以该列为基准的排序， 接受一个 `Boolean`，默认为 `false`。 可以通过 Table 的 `default-sort` 属性设置默认的排序列和排序顺序。 可以使用 `sort-method` 或者 `sort-by` 使用自定义的排序规则。 如果需要后端排序，需将 `sortable` 设置为 `custom`，同时在 Table 上监听 `sort-change` 事件， 在事件回调中可以获取当前排序的字段名和排序顺序，从而向接口请求排序后的表格数据。 在本例中，我们还使用了 `formatter` 属性，它用于格式化指定列的值， 接受一个 `Function`，会传入两个参数：`row` 和 `column`， 可以根据自己的需求进行处理。

:::demo
table/sortable
:::

## 树形表格

支持树类型的数据的显示。 当 `row` 中包含 `children` 字段时，被视为树形数据。 渲染嵌套数据需要 `prop` 的 `row-key`。 此外，子行数据可以异步加载。 设置 Table 的 `lazy` 属性为 `true` 与加载函数 `load` 。 通过指定 `row` 中的 `hasChildren` 字段来指定哪些行是包含子节点。 `children` 与 `hasChildren` 都可以通过 `tree-props` 配置。

:::demo
table/tree-table
:::

## 固定列

横向内容过多时，可选择固定列。

固定列需要使用 `fixed` 属性，它接受 `Boolean` 值。 如果为 `true`, 列将被左侧固定. 它还接受传入字符串，`left` 或 `right`，表示左边固定还是右边固定。

:::demo
table/scroll
:::

## API

### HrtTable 属性

| 属性名    | 说明                                                                                                                                                                                                            | 类型                            | 默认值  |
| --------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------- | ------- |
| data      | 表数据                                                                                                                                                                                                          | `any[]`                         | `[]`    |
| stripe    | 是否为斑马纹 table                                                                                                                                                                                              | `boolean`                       | `false` |
| selection | 是否批量操作                                                                                                                                                                                                    | `boolean`                       | `false` |
| row-key   | 行数据的 Key，用来优化 Table 的渲染； 在使用`reserve-selection`功能与显示树形数据时，该属性是必填的。 类型为 `String` 时，支持多层访问：`user.info.id`，但不支持 `user.info[0].id`，此种情况请使用 `Function`。 | `(row: any) => string`/`string` | --      |

### HrtTable 事件

| 事件名           | 说明                           | 类型                         |
| ---------------- | ------------------------------ | ---------------------------- |
| selection-change | 当选择项发生变化时会触发该事件 | `(selection: any[]) => void` |

### HrtTable 插槽

| 插槽名  | 说明                                                                                                                                    | 子标签       |
| ------- | --------------------------------------------------------------------------------------------------------------------------------------- | ------------ |
| default | 自定义默认内容                                                                                                                          | Table-column |
| append  | 插入至表格最后一行之后的内容， 如果需要对表格的内容进行无限滚动操作，可能需要用到这个 slot。 若表格有合计行，该 slot 会位于合计行之上。 | -            |
| empty   | 当数据为空时自定义的内容                                                                                                                | -            |
| footer  | 自定义底部内容，主要用于批量操作                                                                                                        | -            |

### HrtTableColumn 属性

| 属性名            | 说明                                                                                                                                               | 类型                                              | 默认值      |
| ----------------- | -------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------- | ----------- |
| prop              | 字段名称 对应列内容的字段名， 也可以使用 `property` 属性                                                                                           | `string`                                          | -           |
| type              | 对应列的类型。 如果设置了`selection`则显示多选框； 如果设置了`index`则显示该行的索引（从 1 开始计算）； 如果设置了`expand`则显示为一个可展开的按钮 | `'default' \| 'selection' \| 'index' \| 'expand'` | `'default'` |
| index             | 如果设置了 `type=index`，可以通过传递 `index` 属性来自定义索引                                                                                     | `number` /`(index: number) => number`             | -           |
| label             | 显示的标题                                                                                                                                         | `string`                                          | -           |
| width             | 列宽， 支持 `px` 和 `%` 单位                                                                                                                       | `string`                                          | -           |
| align             | 对齐方式                                                                                                                                           | `'left' \| 'center' \| 'right'`                   | `'left'`    |
| fixed             | 列是否固定                                                                                                                                         | `'left' \| 'right'` \ `false`                     | `false`     |
| actionFlex        | 操作列自定义                                                                                                                                       | `boolean`                                         | `false`     |
| actionFixedNumber | 固定操作按钮数量                                                                                                                                   | `number`                                          | `0`         |
| axtionMoreText    | 展开文案                                                                                                                                           | `string`                                          | `···`       |

### HrtTableColumn 插槽

| 插槽名  | 说明             | 类型                                        |
| ------- | ---------------- | ------------------------------------------- |
| default | 自定义列的内容   | `{ row: any, column: any, $index: number }` |
| header  | 自定义表头的内容 | `{ filterOpened: boolean }`                 |
