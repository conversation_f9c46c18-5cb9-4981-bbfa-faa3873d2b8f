# 设计规范 Design

## tailwindcss 预设

项目通过 tailwindcss 进行样式设计，要在其他项目使用组件库的设计规范，可以通过提供的 tailwindcss 预设引入组件样式定义。

### 安装

::: code-group

```bash
$ pnpm add -D @hrt/tailwindcss
```

```bash
$ npm install -D @hrt/tailwindcss
```

```bash
$ yarn add -D @hrt/tailwindcss
```

:::

### 配置

在项目根目录下创建 `tailwind.config.js` 文件，并添加以下内容：

```js
/** @type {import('tailwindcss').Config} */
module.exports = {
  presets: [
    require('@hrt/tailwindcss')
  ],
  // ...
}
```

## 色彩 Colors

<HrtColors/>
