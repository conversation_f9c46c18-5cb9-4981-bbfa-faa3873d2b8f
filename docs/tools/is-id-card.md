# isIDCard 身份证验证

验证一个值是否为身份证格式。

## 说明

- 身份证号码为18位，前17位为数字，最后一位为数字或X/x。
- 前17位数字分别代表：
  - 前6位为地址码，代表省、市、区县。
  - 中间6位为出生日期码，代表年、月、日。
  - 后4位为顺序码，代表在同一地址码下的顺序号。
- 最后一位为校验码，用于校验身份证号码的有效性。

## 签名

```ts
function isIDCard(value: string): boolean
```

## 参数

- `value`：要验证的值。

## 返回值

- 如果值是身份证格式，返回 `true`，否则返回 `false`。

## 示例

```ts
isIDCard('210124198508162281') // true
isIDCard('210905197807210546') // true
isIDCard('37078119790127719X') // true
isIDCard('37142819800508053x') // true
isIDCard('37010219680709292X') // true
isIDCard('370781197901277191') // false
```
