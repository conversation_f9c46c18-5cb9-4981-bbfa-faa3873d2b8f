# formatDate 日期格式化

将日期对象或时间戳转换为指定格式的字符串。

## 签名

```typescript
function formatDate(date: Date | number | string | null, format: string): string
```

## 参数

- `date`：要格式化的日期对象、时间戳或字符串。如果为 `null` 或 `undefined`，则返回空字符串。
- `format`：指定的日期格式字符串，用于定义输出的日期格式。

## 返回值

返回格式化后的日期字符串。

## 示例

```typescript
const date = new Date('2023-05-15 12:30:00')
formatDate(date, 'YYYY-MM-DD HH:mm:ss') // 2023-05-15 12:30:00
formatDate(date, 'YYYY-MM-DD') // 2023-05-15
formatDate(date, 'HH:mm:ss') // 12:30:00
formatDate(date, 'YYYY-MM-DD HH:mm:ss.SSS') // 2023-05-15 12:30:00.000
formatDate(1684156800000, 'YYYY-MM-DD HH:mm:ss') // 2023-05-15 12:00:00
formatDate('2023-05-15 12:30:00', 'YYYY-MM-DD HH:mm:ss') // 2023-05-15 12:30:00
formatDate(null, 'YYYY-MM-DD HH:mm:ss.SSSSSS') // ''
formatDate(undefined, 'YYYY-MM-DD HH:mm:ss') // ''
```
