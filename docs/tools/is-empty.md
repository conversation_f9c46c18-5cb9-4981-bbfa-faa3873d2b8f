# isEmpty 空值验证

## 说明

验证一个值是否为空值。

## 签名

```ts
function isEmpty(value: any): boolean
```

## 参数

- `value`：要验证的值。

## 返回值

- 如果值为空值，返回 `true`，否则返回 `false`。

## 示例

```ts
isEmpty('') // true
isEmpty(' ') // true
isEmpty(0) // true
isEmpty(null) // true
isEmpty(undefined) // true
isEmpty({}) // true
isEmpty([]) // true
isEmpty(true) // false
isEmpty(false) // false
isEmpty(123) // false
isEmpty('hello') // false
```

## 注意

- 输入值为对象或数组时，会递归检查其属性或元素是否为空值。
- 输入值为其他类型时，会直接判断是否为空值。
