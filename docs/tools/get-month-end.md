# getMonthEnd

获取指定日期所在月份的最后一天时间（23:59:59.999）

## 类型定义

```typescript
function getMonthEnd(date: Date | string | number): Date | null
```

## 参数说明

- `date`：可以是Date对象、日期字符串或时间戳

## 返回值

返回处理后的Date对象，如果日期无效则返回`null`

## 使用示例

```typescript
// Date对象输入
const date = new Date(2023, 0, 15) // 2023年1月15日
const result = getMonthEnd(date)
// 返回: 2023年1月31日 23:59:59.999

// 字符串输入
const strResult = getMonthEnd('2023-02-15')
// 返回: 2023年2月28日 23:59:59.999

// 时间戳输入
const timestamp = new Date(2023, 3, 10).getTime() // 2023年4月10日
const tsResult = getMonthEnd(timestamp)
// 返回: 2023年4月30日 23:59:59.999

// 无效日期
const invalid = getMonthEnd('invalid-date')
// 返回: null
```
