# getYearStart

获取当年的开始时间（1月1号 00:00:00.000）

## 签名

```typescript
function getYearStart(date: Date | string | number): Date | null
```

## 参数

- `date`：日期对象或可被Date解析的字符串/数字

## 返回值

返回当年开始的Date对象，如果输入无效则返回null

## 示例

```typescript
// 使用Date对象
const date = new Date('2023-05-15')
const yearStart = getYearStart(date) // 返回 2023-01-01 00:00:00.000

// 使用日期字符串
const yearStart2 = getYearStart('2023-05-15') // 返回 2023-01-01 00:00:00.000

// 使用时间戳
const timestamp = new Date('2023-05-15').getTime()
const yearStart3 = getYearStart(timestamp) // 返回 2023-01-01 00:00:00.000

// 无效日期
const invalid = getYearStart('invalid-date') // 返回 null
```
