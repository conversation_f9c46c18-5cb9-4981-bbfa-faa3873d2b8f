# parseSeconds 秒数解析

将秒转换为对应的时、分、秒的对象

## 签名

```ts
function parseSeconds(seconds: number): {
  hours: number
  minutes: number
  seconds: number
}
```

## 参数

- `seconds`：要解析的秒数。

## 返回值

返回一个对象，包含时、分、秒的属性。

## 示例

```ts
parseSeconds(3661) // { hours: 1, minutes: 1, seconds: 1 }
parseSeconds(3600) // { hours: 1, minutes: 0, seconds: 0 }
parseSeconds(60) // { hours: 0, minutes: 1, seconds: 0 }
parseSeconds(0) // { hours: 0, minutes: 0, seconds: 0 }
```
