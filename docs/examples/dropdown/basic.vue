<script lang="ts" setup>
import { HrtDropdown, HrtDropdownItem, HrtDropdownMenu } from '@hrt/components'
</script>

<template>
  <HrtDropdown title="下拉菜单">
    <template #dropdown>
      <HrtDropdownMenu>
        <HrtDropdownItem>
          Action 1
        </HrtDropdownItem>
        <HrtDropdownItem>Action 2</HrtDropdownItem>
        <HrtDropdownItem>Action 3</HrtDropdownItem>
        <HrtDropdownItem disabled>
          Action 4
        </HrtDropdownItem>
        <HrtDropdownItem divided>
          Action 5
        </HrtDropdownItem>
      </HrtDropdownMenu>
    </template>
  </HrtDropdown>
</template>

<style scoped>
</style>
