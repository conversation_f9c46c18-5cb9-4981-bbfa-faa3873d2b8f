<script lang="ts" setup>
import { HrtDropdown, HrtDropdownItem, HrtDropdownMenu } from '@hrt/components'
</script>

<template>
  <HrtDropdown title="下拉菜单" is-button :button-props="{ type: 'primary' }">
    <template #dropdown>
      <HrtDropdownMenu>
        <HrtDropdownItem>Action 1</HrtDropdownItem>
        <HrtDropdownItem>Action 2</HrtDropdownItem>
        <HrtDropdownItem>Action 3</HrtDropdownItem>
        <HrtDropdownItem disabled>
          Action 4
        </HrtDropdownItem>
        <HrtDropdownItem divided>
          Action 5
        </HrtDropdownItem>
      </HrtDropdownMenu>
    </template>
  </HrtDropdown>
  <span class="hrt-mx-6" />
  <HrtDropdown split-button>
    split-button
    <template #dropdown>
      <HrtDropdownMenu>
        <HrtDropdownItem>Action 1</HrtDropdownItem>
        <HrtDropdownItem>Action 2</HrtDropdownItem>
        <HrtDropdownItem>Action 3</HrtDropdownItem>
        <HrtDropdownItem disabled>
          Action 4
        </HrtDropdownItem>
        <HrtDropdownItem divided>
          Action 5
        </HrtDropdownItem>
      </HrtDropdownMenu>
    </template>
  </HrtDropdown>
</template>

<style scoped>
</style>
