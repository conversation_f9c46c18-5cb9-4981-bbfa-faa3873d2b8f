<script lang="ts" setup>
import { HrtButton, HrtNotice } from '@hrt/components'

function open1() {
  HrtNotice({
    title: '通知',
    message: '通知的内容通知的内容通知的内容通知的内容通知的内容通知的内容通知的内容通知的内容',
  })
}

function open2() {
  HrtNotice({
    title: '通知',
    message: '通知的内容通知的内容通知的内容通知的内容通知的内容通知的内容通知的内容通知的内容',
    theme: 'dark',
  })
}
</script>

<template>
  <HrtButton plain @click="open1">
    浅色
  </HrtButton>
  <HrtButton type="primary" @click="open2">
    深色
  </HrtButton>
</template>
