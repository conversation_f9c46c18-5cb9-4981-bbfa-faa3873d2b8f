<script lang="ts" setup>
import { HrtButton, HrtNotice } from '@hrt/components'

function open() {
  HrtNotice('普通快捷通知')
}
function open1() {
  HrtNotice.success('success快捷通知')
}
function open2() {
  HrtNotice.warning('warning快捷通知')
}
function open3() {
  HrtNotice.info('info快捷通知')
}
function open4() {
  HrtNotice.error('error快捷通知')
}
function open5() {
  HrtNotice.primary('primary快捷通知')
}
</script>

<template>
  <HrtButton plain @click="open">
    普通
  </HrtButton>
  <HrtButton plain @click="open1">
    Success
  </HrtButton>
  <HrtButton plain @click="open2">
    Warning
  </HrtButton>
  <HrtButton plain @click="open3">
    Info
  </HrtButton>
  <HrtButton plain @click="open4">
    Error
  </HrtButton>
  <HrtButton plain @click="open5">
    Primary
  </HrtButton>
</template>
