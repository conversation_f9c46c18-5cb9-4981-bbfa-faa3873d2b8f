<script lang="ts" setup>
import { HrtButton, HrtNotice } from '@hrt/components'

function open1(theme?: 'light' | 'dark') {
  HrtNotice({
    title: 'Success',
    message: 'This is a success message',
    type: 'success',
    theme,
  })
}

function open2(theme?: 'light' | 'dark') {
  HrtNotice({
    title: 'Warning',
    message: 'This is a warning message',
    type: 'warning',
    theme,
  })
}

function open3(theme?: 'light' | 'dark') {
  HrtNotice.info({
    title: 'Info',
    message: 'This is an info message',
    theme,
  })
}

function open4(theme?: 'light' | 'dark') {
  HrtNotice.error({
    title: 'Error',
    message: 'This is an error message',
    theme,
  })
}

function open5(theme?: 'light' | 'dark') {
  HrtNotice({
    title: 'Primary',
    message: 'This is a primary message',
    type: 'primary',
    theme,
  })
}
</script>

<template>
  <HrtButton plain @click="open1()">
    Success
  </HrtButton>
  <HrtButton plain @click="open2()">
    Warning
  </HrtButton>
  <HrtButton plain @click="open3()">
    Info
  </HrtButton>
  <HrtButton plain @click="open4()">
    Error
  </HrtButton>
  <HrtButton plain @click="open5()">
    Primary
  </HrtButton>

  <br><br>

  <HrtButton type="primary" @click="open1('dark')">
    Success
  </HrtButton>
  <HrtButton type="primary" @click="open2('dark')">
    Warning
  </HrtButton>
  <HrtButton type="primary" @click="open3('dark')">
    Info
  </HrtButton>
  <HrtButton type="primary" @click="open4('dark')">
    Error
  </HrtButton>
  <HrtButton type="primary" @click="open5('dark')">
    Primary
  </HrtButton>
</template>
