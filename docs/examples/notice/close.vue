<script lang="ts" setup>
import { HrtButton, HrtNotice } from '@hrt/components'

let notice: ReturnType<typeof HrtNotice>

function open() {
  notice = HrtNotice({
    title: '通知',
    message: '通知的内容通知的内容通知的内容通知的内容通知的内容通知的内容通知的内容通知的内容',
    duration: 0,
  })
}

function close() {
  notice?.close()
}
</script>

<template>
  <HrtButton plain @click="open">
    打开通知
  </HrtButton>

  <HrtButton plain @click="close">
    关闭通知
  </HrtButton>
</template>
