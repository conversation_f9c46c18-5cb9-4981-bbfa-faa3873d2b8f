<script lang="ts" setup>
import { HrtButton, HrtNotice } from '@hrt/components'

function open1() {
  HrtNotice({
    title: 'Success',
    message: 'This is a success message',
    type: 'success',
    duration: 0,
  })
}

function open2() {
  HrtNotice({
    title: 'Warning',
    message: 'This is a warning message',
    type: 'warning',
    duration: 0,
  })
}

function open3() {
  HrtNotice({
    title: 'Info',
    message: 'This is an info message',
    type: 'info',
    duration: 0,
  })
}

function open4() {
  HrtNotice({
    title: 'Error',
    message: 'This is an error message',
    type: 'error',
    duration: 0,
  })
}

function open5() {
  HrtNotice({
    title: 'Primary',
    message: 'This is a primary message',
    type: 'primary',
    duration: 0,
  })
}

function closeAll() {
  HrtNotice.closeAll()
}
</script>

<template>
  <HrtButton plain @click="open5">
    Primary
  </HrtButton>
  <HrtButton plain @click="open1">
    Success
  </HrtButton>
  <HrtButton plain @click="open2">
    Warning
  </HrtButton>
  <HrtButton plain @click="open3">
    Info
  </HrtButton>
  <HrtButton plain @click="open4">
    Error
  </HrtButton>
  <br><br>
  <HrtButton type="primary" @click="closeAll">
    关闭所有
  </HrtButton>
</template>
