<script setup lang="ts">
import { HrtButton, HrtMessage } from '@hrt/components'

function open1() {
  HrtMessage({
    message: 'primary提示文案',
    type: 'primary',
  })
}
function open2() {
  HrtMessage({
    message: 'success提示文案',
    type: 'success',
  })
}
function open3() {
  HrtMessage({
    message: 'warning提示文案',
    type: 'warning',
  })
}
function open4() {
  HrtMessage.error('error提示文案')
}
function open5() {
  HrtMessage.info('info提示文案', { duration: 3000 })
}
</script>

<template>
  <div>
    <HrtButton @click="open1">
      Primary
    </HrtButton>
    <HrtButton @click="open2">
      Success
    </HrtButton>
    <HrtButton @click="open3">
      Warning
    </HrtButton>
    <HrtButton @click="open4">
      Error
    </HrtButton>
    <HrtButton @click="open5">
      Info
    </HrtButton>
  </div>
</template>
