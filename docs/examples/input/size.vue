<script lang="ts" setup>
import { HrtInput } from '@hrt/components'
import { ref } from 'vue'

const input = ref('')
</script>

<template>
  <div class="hrt-flex hrt-flex-col hrt-gap-2">
    <HrtInput v-model="input" size="mini" placeholder="116px" />
    <HrtInput v-model="input" size="small" placeholder="240px" />
    <HrtInput v-model="input" size="middle" placeholder="364px" />
    <HrtInput v-model="input" size="large" placeholder="488px" />
    <HrtInput v-model="input" size="super" placeholder="612px" />
    <HrtInput v-model="input" placeholder="全宽" />
  </div>
</template>

<style lang="css">
.el-input__inner::placeholder {
  @apply hrt-text-neutral-400;
}
</style>
