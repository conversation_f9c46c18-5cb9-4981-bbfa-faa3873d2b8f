<script lang="ts" setup>
import { HrtMessage } from '@hrt/components'
import { ref } from 'vue'

const content = ref('')
function onSend(value: string) {
  console.log(value.replace(/<p>/g, '').replace(/<\/p>/g, '<br>'))
  HrtMessage.info('发送消息')
}
function onBeforeSend() {
  return new Promise<boolean>((resolve) => {
    setTimeout(() => {
      resolve(true)
    }, 1000)
  })
}
</script>

<template>
  <div class="hrt-h-[200px]">
    <HrtChatInput v-model="content" :before-send="onBeforeSend" @send="onSend" />
  </div>
</template>
