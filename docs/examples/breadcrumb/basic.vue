<script lang="ts" setup>
import { HrtBreadcrumb, HrtBreadcrumbItem } from '@hrt/components'
import { ref } from 'vue'

const value = ref<string>('ddd')
</script>

<template>
  <div class="breadcrumb-demo">
    <HrtBreadcrumb v-model="value">
      <HrtBreadcrumbItem value="aaa" :to="{ path: '/' }">
        一级界面
      </HrtBreadcrumbItem>
      <HrtBreadcrumbItem value="bbb">
        <a href="/">二级界面</a>
      </HrtBreadcrumbItem>
      <HrtBreadcrumbItem value="ccc">
        三级界面
      </HrtBreadcrumbItem>
      <HrtBreadcrumbItem value="ddd">
        四级界面
      </HrtBreadcrumbItem>
    </HrtBreadcrumb>
  </div>
</template>

<style lang="less">
.breadcrumb-demo {
  a {
    text-decoration: unset !important;
    color: unset !important;
    font-weight: normal !important;
  }
}
</style>
