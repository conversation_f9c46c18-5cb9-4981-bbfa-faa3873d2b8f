<script lang="ts" setup>
import { HrtOption, HrtSelect, HrtTag } from '@hrt/components'
import { ref } from 'vue'

const value = ref<string[]>([])
const colors = [
  {
    value: '#E63415',
    label: 'red',
  },
  {
    value: '#FF6600',
    label: 'orange',
  },
  {
    value: '#FFDE0A',
    label: 'yellow',
  },
  {
    value: '#1EC79D',
    label: 'green',
  },
  {
    value: '#14CCCC',
    label: 'cyan',
  },
  {
    value: '#4167F0',
    label: 'blue',
  },
  {
    value: '#6222C9',
    label: 'purple',
  },
]
colors.forEach((color) => {
  value.value.push(color.value)
})
</script>

<template>
  <div class="hrt-w-[240px]">
    <HrtSelect v-model="value" multiple>
      <HrtOption
        v-for="item in colors"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      >
        <div class="hrt-flex hrt-items-center">
          <HrtTag :color="item.value" style="margin-right: 8px" size="small" />
          <span :style="{ color: item.value }">{{ item.label }}</span>
        </div>
      </HrtOption>
      <template #tag>
        <HrtTag v-for="color in value" :key="color" :color="color" />
      </template>
    </HrtSelect>
  </div>
</template>

<style scoped>
.hrt-tag {
  border: none;
  aspect-ratio: 1;
}
</style>
