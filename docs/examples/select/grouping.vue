<script lang="ts" setup>
import { HrtOption, HrtOptionGroup, HrtSelect } from '@hrt/components'
import { ref } from 'vue'

const value = ref('')
const options = [
  {
    label: '热门城市',
    options: [
      {
        value: '上海',
        label: '上海',
      },
      {
        value: '北京',
        label: '北京',
      },
    ],
  },
  {
    label: '城市名',
    options: [
      {
        value: '成都',
        label: '成都',
      },
      {
        value: '深圳',
        label: '深圳',
      },
      {
        value: '广州',
        label: '广州',
      },
      {
        value: '大连',
        label: '大连',
      },
    ],
  },
]
</script>

<template>
  <div class="hrt-w-[240px]">
    <HrtSelect v-model="value">
      <HrtOptionGroup
        v-for="group in options"
        :key="group.label"
        :label="group.label"
      >
        <HrtOption
          v-for="item in group.options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </HrtOptionGroup>
    </HrtSelect>
  </div>
</template>
