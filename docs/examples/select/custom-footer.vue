<script lang="ts" setup>
import type { CheckboxValueType } from 'element-plus'
import { HrtOption, HrtSelect } from '@hrt/components'
import { ref } from 'vue'

const value = ref<CheckboxValueType[]>([])
const cities = ref([
  {
    value: 'Beijing',
    label: '北京',
  },
  {
    value: 'Shanghai',
    label: '上海',
  },
  {
    value: 'Nanjing',
    label: '南京',
  },
  {
    value: 'Chengdu',
    label: '成都',
  },
  {
    value: 'Shenzhen',
    label: '深圳',
  },
  {
    value: 'Guangzhou',
    label: '广州',
  },
])
</script>

<template>
  <div class="hrt-w-[240px]">
    <HrtSelect v-model="value">
      <HrtOption
        v-for="item in cities"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
      <template #footer>
        <span>这是自定义内容</span>
      </template>
    </HrtSelect>
  </div>
</template>
