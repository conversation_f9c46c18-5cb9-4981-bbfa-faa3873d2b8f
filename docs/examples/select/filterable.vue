<script lang="ts" setup>
import { HrtOption, HrtSelect } from '@hrt/components'
import { ref } from 'vue'

const value = ref('')
const options = [...Array.from({ length: 20 })].map((_, index) => {
  if (index === 0) {
    return {
      label: `选项${index + 1}很长很长很长很长很长很长很长很长很长很长很长很长`,
      value: index + 1,
    }
  }
  return {
    label: `选项${index + 1}`,
    value: index + 1,
  }
})
</script>

<template>
  <div class="hrt-flex hrt-gap-8">
    <div class="hrt-flex hrt-flex-col hrt-gap-2">
      <span>单选</span>
      <HrtSelect v-model="value" placeholder="请选择或输入" filterable>
        <HrtOption
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </HrtSelect>
    </div>
    <div class="hrt-flex hrt-flex-col hrt-gap-2">
      <span>多选</span>
      <HrtSelect v-model="value" placeholder="请选择或输入" multiple filterable>
        <HrtOption
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </HrtSelect>
    </div>
  </div>
</template>
