<script lang="ts" setup>
import { HrtOption, HrtSelect } from '@hrt/components'
import { ElMessage } from 'element-plus'
import { ref } from 'vue'

const value = ref('')

const options = [
  {
    value: '',
    label: 'All',
  },
  {
    value: 'Option1',
    label: 'Option1',
  },
  {
    value: 'Option2',
    label: 'Option2',
  },
  {
    value: 'Option3',
    label: 'Option3',
  },
  {
    value: 'Option4',
    label: 'Option4',
  },
  {
    value: 'Option5',
    label: 'Option5',
  },
]

function handleClear() {
  ElMessage.info(`The clear value is: ${value.value}`)
}
</script>

<template>
  <div class="hrt-w-[240px]">
    <HrtSelect
      v-model="value"
      :empty-values="[null, undefined]"
      :value-on-clear="null"
      clearable
      @clear="handleClear"
    >
      <HrtOption
        v-for="item in options"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </HrtSelect>
  </div>
</template>
