<script lang="ts" setup>
import { HrtOption, HrtSelect } from '@hrt/components'
import { ref } from 'vue'

const value1 = ref([])
const value2 = ref([])
const value3 = ref([])
const value4 = ref([])
const options = [...Array.from({ length: 40 })].map((_, index) => ({
  label: `选项${index + 1}`,
  value: index + 1,
}))

const selectRef = ref()
</script>

<template>
  <div class="hrt-m-4">
    <p>default 换行</p>
    <div class="hrt-w-[240px]">
      <HrtSelect ref="selectRef" v-model="value1" multiple clearable>
        <HrtOption
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </HrtSelect>
    </div>
  </div>
  <div class="hrt-m-4">
    <p>use collapse-tags 折叠</p>
    <div class="hrt-w-[240px]">
      <HrtSelect v-model="value2" multiple collapse-tags clearable>
        <HrtOption
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </HrtSelect>
    </div>
  </div>
  <div class="hrt-m-4">
    <p>use collapse-tags-tooltip 折叠+tooltip</p>
    <div class="hrt-w-[240px]">
      <HrtSelect v-model="value3" multiple collapse-tags collapse-tags-tooltip clearable>
        <HrtOption
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </HrtSelect>
    </div>
  </div>
  <div class="hrt-m-4">
    <p>use max-collapse-tags 最大展示标签数</p>
    <div class="hrt-w-[240px]">
      <HrtSelect v-model="value4" multiple collapse-tags collapse-tags-tooltip :max-collapse-tags="3" clearable>
        <HrtOption
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </HrtSelect>
    </div>
  </div>
</template>
