<script lang="ts" setup>
import type { CheckboxValueType } from 'element-plus'
import { HrtOption, HrtSelect } from '@hrt/components'
import { ref, watch } from 'vue'

const checkAll = ref(false)
const indeterminate = ref(false)
const value = ref<CheckboxValueType[]>([])
const cities = ref([
  {
    value: 'Beijing',
    label: 'Beijing',
  },
  {
    value: 'Shanghai',
    label: 'Shanghai',
  },
  {
    value: 'Nanjing',
    label: 'Nanjing',
  },
  {
    value: 'Chengdu',
    label: 'Chengdu',
  },
  {
    value: 'Shenzhen',
    label: 'Shenzhen',
  },
  {
    value: 'Guangzhou',
    label: 'Guangzhou',
  },
])

watch(value, (val) => {
  if (val.length === 0) {
    checkAll.value = false
    indeterminate.value = false
  }
  else if (val.length === cities.value.length) {
    checkAll.value = true
    indeterminate.value = false
  }
  else {
    indeterminate.value = true
  }
})

function handleCheckAll(val: CheckboxValueType) {
  indeterminate.value = false
  if (val) {
    value.value = cities.value.map(_ => _.value)
  }
  else {
    value.value = []
  }
}
</script>

<template>
  <div class="hrt-w-[240px]">
    <HrtSelect
      v-model="value"
      multiple
      clearable
      collapse-tags
      popper-class="custom-header"
      :max-collapse-tags="1"
    >
      <template #header>
        <el-checkbox
          v-model="checkAll"
          :indeterminate="indeterminate"
          @change="handleCheckAll"
        >
          All
        </el-checkbox>
      </template>
      <HrtOption
        v-for="item in cities"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </HrtSelect>
  </div>
</template>

<style>
.custom-header {
  .el-checkbox {
    display: flex;
    height: unset;
  }
}
</style>
