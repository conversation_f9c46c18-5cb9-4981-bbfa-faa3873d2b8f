<script lang="ts" setup>
import { HrtOption, HrtSelect } from '@hrt/components'
import { ref } from 'vue'

const value = ref('')

const options = Array.from({ length: 8 }).map((_, index) => ({
  label: `选项${index + 1}`,
  value: index + 1,
  description: `${'辅助文字'.padStart(index * 6, '辅助文字')}${index + 1}`,
  multiline: true,
}))
</script>

<template>
  <div class="hrt-flex hrt-w-[240px]">
    <HrtSelect v-model="value">
      <HrtOption
        v-for="item in options"
        :key="item.value"
        :label="item.label"
        :value="item.value"
        :description="item.description"
        :multiline="item.multiline"
      />
    </HrtSelect>
  </div>
</template>
