<script lang="ts" setup>
import { HrtOption, HrtSelect } from '@hrt/components'
import { ref } from 'vue'

const value = ref<string[]>([])
const options = [
  {
    value: 'HTML',
    label: 'HTML',
  },
  {
    value: 'CSS',
    label: 'CSS',
  },
  {
    value: 'JavaScript',
    label: 'JavaScript',
  },
]
</script>

<template>
  <div class="hrt-w-[240px]">
    <HrtSelect
      v-model="value"
      multiple
      filterable
      allow-create
      default-first-option
      :reserve-keyword="false"
    >
      <HrtOption
        v-for="item in options"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </HrtSelect>
  </div>
</template>
