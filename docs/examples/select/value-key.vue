<script setup lang="ts">
import { HrtOption, HrtSelect } from '@hrt/components'
import { ref } from 'vue'

interface Option {
  id: number
  label: string
  desc: string
}
const value = ref<Option>()
const options = ref([
  { id: 1, label: 'Option A', desc: 'Option A - 230506' },
  { id: 2, label: 'Option B', desc: 'Option B - 230506' },
  { id: 3, label: 'Option C', desc: 'Option C - 230506' },
  { id: 4, label: 'Option A', desc: 'Option A - 230507' },
])
</script>

<template>
  <div class="hrt-w-[240px]">
    <HrtSelect
      v-model="value"
      value-key="id"
    >
      <HrtOption
        v-for="item in options"
        :key="item.id"
        :label="item.label"
        :value="item"
      />
    </HrtSelect>
    <p>
      选中选项的描述:
      {{ value ? value.desc : '未选择任何选项' }}
    </p>
  </div>
</template>
