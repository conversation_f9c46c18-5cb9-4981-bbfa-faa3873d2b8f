<script lang="ts" setup>
import { HrtOption, HrtSelect } from '@hrt/components'
import { ref } from 'vue'

const value = ref('')
const options = [...Array.from({ length: 20 })].map((_, index) => ({
  label: `选项${index + 1}`,
  value: index + 1,
}))
</script>

<template>
  <div class="hrt-w-[240px]">
    <HrtSelect
      v-model="value"
      clearable
    >
      <HrtOption
        v-for="item in options"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </HrtSelect>
  </div>
</template>
