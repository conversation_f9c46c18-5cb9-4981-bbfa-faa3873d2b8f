<script lang="ts" setup>
import { HrtOption, HrtSelect } from '@hrt/components'
import { ref } from 'vue'

const value = ref('')

const options = [
  {
    value: 'Option1',
    label: '这个是很长很长的文字这个是很长很长的文字这个是很长很长的文字这个是很长很长的文字这个是很长很长的文字这个是很长很长的文字这个是很长很长的文字这个是很长很长的文字这个是很长很长的文字',
  },
  {
    value: 'Option2',
    label: 'Option2',
  },
  {
    value: 'Option3',
    label: 'Option3',
  },
  {
    value: 'Option4',
    label: 'Option4',
  },
  {
    value: 'Option5',
    label: 'Option5',
  },
]
</script>

<template>
  <div>
    <p>默认：</p>
    <HrtSelect v-model="value" clearable placeholder="100%">
      <HrtOption
        v-for="item in options"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </HrtSelect>
  </div>
  <div class="hrt-mt-4">
    <p>大尺寸：</p>
    <HrtSelect v-model="value" size="large" clearable placeholder="364px">
      <HrtOption
        v-for="item in options"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </HrtSelect>
  </div>
  <div class="hrt-mt-4">
    <p>中尺寸：</p>
    <HrtSelect v-model="value" size="default" placeholder="240px">
      <HrtOption
        v-for="item in options"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </HrtSelect>
  </div>
  <div class="hrt-mt-4">
    <p>小尺寸：</p>
    <HrtSelect v-model="value" size="small" placeholder="116px">
      <HrtOption
        v-for="item in options"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </HrtSelect>
  </div>
</template>
