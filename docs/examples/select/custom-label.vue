<script lang="ts" setup>
import { HrtOption, HrtSelect } from '@hrt/components'
import { ref } from 'vue'

const value1 = ref<string>('Option1')
const value2 = ref<string[]>(['Option1'])
const options = [
  {
    value: 'Option1',
    label: 'Label1',
  },
  {
    value: 'Option2',
    label: 'Label2',
  },
  {
    value: 'Option3',
    label: 'Label3',
  },
  {
    value: 'Option4',
    label: 'Label4',
  },
  {
    value: 'Option5',
    label: 'Label5',
  },
]
</script>

<template>
  <div class="hrt-w-[240px]">
    <div class="hrt-flex hrt-flex-wrap hrt-gap-4 hrt-items-center">
      <HrtSelect
        v-model="value1"
        clearable
      >
        <template #label="{ label, value }">
          <span>{{ label }}: </span>
          <span style="font-weight: bold">{{ value }}</span>
        </template>
        <HrtOption
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </HrtSelect>

      <HrtSelect
        v-model="value2"
        clearable
        multiple
      >
        <template #label="{ label, value }">
          <span>{{ label }}: </span>
          <span style="font-weight: bold">{{ value }}</span>
        </template>
        <HrtOption
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </HrtSelect>
    </div>
  </div>
</template>

<style scoped></style>
