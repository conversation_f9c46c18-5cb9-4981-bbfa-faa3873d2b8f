<script setup lang="ts">
import { HrtTabPane, HrtTabs } from '@hrt/components'
import { ref } from 'vue'

const active = ref('1')
</script>

<template>
  <HrtTabs v-model="active" type="slide">
    <HrtTabPane label="Tab 1" name="1">
      Tab 1 Content
    </HrtTabPane>
    <HrtTabPane label="Tab 2" name="2">
      Tab 2 Content
    </HrtTabPane>
    <HrtTabPane label="Tab 3" name="3">
      Tab 3 Content
    </HrtTabPane>
    <HrtTabPane label="Tab 4" name="4" :disabled="true">
      Tab 4 Content
    </HrtTabPane>
    <HrtTabPane label="Tab 5" name="5">
      Tab 5 Content
    </HrtTabPane>
  </HrtTabs>
</template>

<style lang="css">
.vp-doc li + li {
  margin-top: 0 !important;
}
.vp-doc ul {
  list-style: none !important;
}
.vp-doc ul,
.vp-doc ol {
  padding-left: 0 !important;
  margin: 0 !important;
}
</style>
