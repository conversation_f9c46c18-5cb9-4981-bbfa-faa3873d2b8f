<script setup lang="ts">
import { HrtCol, HrtRow } from '@hrt/components'
</script>

<template>
  <div class="grid grid-cols-1 gap-4">
    <HrtRow justify="start">
      <HrtCol :span="3">
        <div class="rounded min-h-9 px-2 leading-9 bg-neutral-500">
          col-3
        </div>
      </HrtCol>
      <HrtCol :span="3">
        <div class="rounded min-h-9 px-2 leading-9 bg-neutral-600">
          col-3
        </div>
      </HrtCol>
      <HrtCol :span="3">
        <div class="rounded min-h-9 px-2 leading-9 bg-neutral-500">
          col-3
        </div>
      </HrtCol>
    </HrtRow>
    <HrtRow justify="center">
      <HrtCol :span="3">
        <div class="rounded min-h-9 px-2 leading-9 bg-neutral-500">
          col-3
        </div>
      </HrtCol>
      <HrtCol :span="3">
        <div class="rounded min-h-9 px-2 leading-9 bg-neutral-600">
          col-3
        </div>
      </HrtCol>
      <HrtCol :span="3">
        <div class="rounded min-h-9 px-2 leading-9 bg-neutral-500">
          col-3
        </div>
      </HrtCol>
    </HrtRow>
    <HrtRow justify="end">
      <HrtCol :span="3">
        <div class="rounded min-h-9 px-2 leading-9 bg-neutral-500">
          col-3
        </div>
      </HrtCol>
      <HrtCol :span="3">
        <div class="rounded min-h-9 px-2 leading-9 bg-neutral-600">
          col-3
        </div>
      </HrtCol>
      <HrtCol :span="3">
        <div class="rounded min-h-9 px-2 leading-9 bg-neutral-500">
          col-3
        </div>
      </HrtCol>
    </HrtRow>
    <HrtRow justify="space-between">
      <HrtCol :span="3">
        <div class="rounded min-h-9 px-2 leading-9 bg-neutral-500">
          col-3
        </div>
      </HrtCol>
      <HrtCol :span="3">
        <div class="rounded min-h-9 px-2 leading-9 bg-neutral-600">
          col-3
        </div>
      </HrtCol>
      <HrtCol :span="3">
        <div class="rounded min-h-9 px-2 leading-9 bg-neutral-500">
          col-3
        </div>
      </HrtCol>
    </HrtRow>
    <HrtRow justify="space-around">
      <HrtCol :span="3">
        <div class="rounded min-h-9 px-2 leading-9 bg-neutral-500">
          col-3
        </div>
      </HrtCol>
      <HrtCol :span="3">
        <div class="rounded min-h-9 px-2 leading-9 bg-neutral-600">
          col-3
        </div>
      </HrtCol>
      <HrtCol :span="3">
        <div class="rounded min-h-9 px-2 leading-9 bg-neutral-500">
          col-3
        </div>
      </HrtCol>
    </HrtRow>
    <HrtRow justify="space-evenly">
      <HrtCol :span="3">
        <div class="rounded min-h-9 px-2 leading-9 bg-neutral-500">
          col-3
        </div>
      </HrtCol>
      <HrtCol :span="3">
        <div class="rounded min-h-9 px-2 leading-9 bg-neutral-600">
          col-3
        </div>
      </HrtCol>
      <HrtCol :span="3">
        <div class="rounded min-h-9 px-2 leading-9 bg-neutral-500">
          col-3
        </div>
      </HrtCol>
    </HrtRow>
  </div>
</template>
