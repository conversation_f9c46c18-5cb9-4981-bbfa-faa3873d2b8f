<script setup lang="ts">
import { HrtCol, HrtRow } from '@hrt/components'
</script>

<template>
  <div class="grid grid-cols-1 gap-4">
    <HrtRow>
      <HrtCol :span="3">
        <div class="rounded-sm min-h-9 px-2 leading-9 bg-neutral-500">
          col-3
        </div>
      </HrtCol>
      <HrtCol :span="3">
        <div class="rounded-sm min-h-9 px-2 leading-9 bg-neutral-600">
          col-3
        </div>
      </HrtCol>
      <HrtCol :span="3">
        <div class="rounded-sm min-h-9 px-2 leading-9 bg-neutral-500">
          col-3
        </div>
      </HrtCol>
      <HrtCol :span="3">
        <div class="rounded-sm min-h-9 px-2 leading-9 bg-neutral-600">
          col-3
        </div>
      </HrtCol>
    </HrtRow>
    <HrtRow :gutter="0">
      <HrtCol :span="3">
        <div class="rounded-sm min-h-9 px-2 leading-9 bg-neutral-500">
          col-3
        </div>
      </HrtCol>
      <HrtCol :span="3">
        <div class="rounded-sm min-h-9 px-2 leading-9 bg-neutral-600">
          col-3
        </div>
      </HrtCol>
      <HrtCol :span="3">
        <div class="rounded-sm min-h-9 px-2 leading-9 bg-neutral-500">
          col-3
        </div>
      </HrtCol>
      <HrtCol :span="3">
        <div class="rounded-sm min-h-9 px-2 leading-9 bg-neutral-600">
          col-3
        </div>
      </HrtCol>
    </HrtRow>
  </div>
</template>
