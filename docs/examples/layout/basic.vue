<script setup lang="ts">
import { HrtCol, HrtRow } from '@hrt/components'
</script>

<template>
  <div class="grid grid-cols-1 gap-4">
    <HrtRow>
      <HrtCol :span="12">
        <div class="rounded-sm bg-neutral-400 min-h-9 px-2 leading-9">
          col-12
        </div>
      </HrtCol>
      <HrtCol :span="6">
        <div class="rounded-sm min-h-9 px-2 leading-9 bg-neutral-500">
          col-6
        </div>
      </HrtCol>
      <HrtCol :span="6">
        <div class="rounded-sm min-h-9 px-2 leading-9 bg-neutral-600">
          col-6
        </div>
      </HrtCol>
    </HrtRow>
    <HrtRow>
      <HrtCol :span="4">
        <div class="rounded-sm min-h-9 px-2 leading-9 bg-neutral-500">
          col-4
        </div>
      </HrtCol>
      <HrtCol :span="4">
        <div class="rounded-sm min-h-9 px-2 leading-9 bg-neutral-600">
          col-4
        </div>
      </HrtCol>
      <HrtCol :span="4">
        <div class="rounded-sm min-h-9 px-2 leading-9 bg-neutral-500">
          col-4
        </div>
      </HrtCol>
    </HrtRow>
    <HrtRow>
      <HrtCol :span="3">
        <div class="rounded-sm min-h-9 px-2 leading-9 bg-neutral-500">
          col-3
        </div>
      </HrtCol>
      <HrtCol :span="3">
        <div class="rounded-sm min-h-9 px-2 leading-9 bg-neutral-600">
          col-3
        </div>
      </HrtCol>
      <HrtCol :span="3">
        <div class="rounded-sm min-h-9 px-2 leading-9 bg-neutral-500">
          col-3
        </div>
      </HrtCol>
      <HrtCol :span="3">
        <div class="rounded-sm min-h-9 px-2 leading-9 bg-neutral-600">
          col-3
        </div>
      </HrtCol>
    </HrtRow>
    <HrtRow>
      <HrtCol :span="2">
        <div class="rounded-sm min-h-9 px-2 leading-9 bg-neutral-500">
          col-2
        </div>
      </HrtCol>
      <HrtCol :span="2">
        <div class="rounded-sm min-h-9 px-2 leading-9 bg-neutral-600">
          col-2
        </div>
      </HrtCol>
      <HrtCol :span="2">
        <div class="rounded-sm min-h-9 px-2 leading-9 bg-neutral-500">
          col-2
        </div>
      </HrtCol>
      <HrtCol :span="2">
        <div class="rounded-sm min-h-9 px-2 leading-9 bg-neutral-600">
          col-2
        </div>
      </HrtCol>
      <HrtCol :span="2">
        <div class="rounded-sm min-h-9 px-2 leading-9 bg-neutral-500">
          col-2
        </div>
      </HrtCol>
      <HrtCol :span="2">
        <div class="rounded-sm min-h-9 px-2 leading-9 bg-neutral-600">
          col-2
        </div>
      </HrtCol>
    </HrtRow>
  </div>
</template>
