<script lang="ts" setup>
import { HrtAlert } from '@hrt/components'
</script>

<template>
  <div class="alert-demo">
    <h4>默认样式</h4>
    <HrtAlert title="默认提示" />
    <HrtAlert title="成功提示" type="success" />
    <HrtAlert title="警告提示" type="warning" />
    <HrtAlert title="错误提示" type="error" />
    <HrtAlert title="信息提示" type="info" />
    <HrtAlert title="主要提示" type="primary" />

    <h4>卡片样式</h4>
    <HrtAlert title="默认提示" :is-card="true" />
    <HrtAlert title="成功提示" type="success" :is-card="true" />
    <HrtAlert title="警告提示" type="warning" :is-card="true" />
    <HrtAlert title="错误提示" type="error" :is-card="true" />
    <HrtAlert title="信息提示" type="info" :is-card="true" />
    <HrtAlert title="主要提示" type="primary" :is-card="true" />
  </div>
</template>

<style scoped>
.alert-demo {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

h4 {
  margin-top: 16px;
  margin-bottom: 8px;
}
</style>
