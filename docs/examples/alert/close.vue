<script lang="ts" setup>
import { HrtAlert } from '@hrt/components'

function handleClose() {
  console.log('Alert已关闭！')
}
</script>

<template>
  <div class="alert-demo">
    <HrtAlert title="不可关闭的提示" :closable="false" />
    <HrtAlert title="自定义关闭文本" close-text="关闭" />
    <HrtAlert title="关闭事件回调" type="warning" @close="handleClose" />
  </div>
</template>

<style scoped>
.alert-demo {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
</style>
