<script lang="ts" setup>
import { HrtAlert } from '@hrt/components'
</script>

<template>
  <div class="alert-demo">
    <HrtAlert title="默认提示" />
    <HrtAlert title="成功提示" type="success" />
    <HrtAlert title="警告提示" type="warning" />
    <HrtAlert title="错误提示" type="error" />
    <HrtAlert title="信息提示" type="info" />
    <HrtAlert title="主要提示" type="primary" />
  </div>
</template>

<style scoped>
.alert-demo {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
</style>
