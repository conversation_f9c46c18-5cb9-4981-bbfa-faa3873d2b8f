<script lang="ts" setup>
import { CirclePlusFilled } from '@element-plus/icons-vue'
import { HrtAlert } from '@hrt/components'
</script>

<template>
  <div class="alert-demo">
    <h4>使用内置图标</h4>
    <HrtAlert title="默认提示" show-icon />
    <HrtAlert title="成功提示" type="success" show-icon />
    <HrtAlert title="警告提示" type="warning" show-icon />
    <HrtAlert title="错误提示" type="error" show-icon />
    <HrtAlert title="信息提示" type="info" show-icon />
    <HrtAlert title="主要提示" type="primary" show-icon />

    <h4>自定义图标</h4>
    <HrtAlert title="自定义图标提示" show-icon>
      <template #icon>
        <CirclePlusFilled />
      </template>
    </HrtAlert>

    <h4>带有描述信息</h4>
    <HrtAlert
      title="带描述的成功提示"
      type="success"
      description="这是一段描述信息，用于提供更详细的上下文说明。"
      show-icon
    />
    <HrtAlert
      title="带描述的警告提示"
      type="warning"
      description="这是一段描述信息，用于提供更详细的上下文说明。"
      show-icon
    />
  </div>
</template>

<style scoped>
.alert-demo {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

h4 {
  margin: 8px 0;
}
</style>
