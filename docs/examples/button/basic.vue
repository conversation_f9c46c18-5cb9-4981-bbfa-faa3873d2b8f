<script setup lang="ts">
import { HrtButton } from '@hrt/components'
</script>

<template>
  <div class="flex gap-4">
    <HrtButton>Default</HrtButton>
    <HrtButton type="primary">
      主按钮
    </HrtButton>
    <HrtButton type="success">
      成功按钮
    </HrtButton>
    <HrtButton type="info">
      提示按钮
    </HrtButton>
    <HrtButton type="warning">
      告警按钮
    </HrtButton>
    <HrtButton type="danger">
      错误按钮
    </HrtButton>
    <HrtButton dashed>
      虚线按钮
    </HrtButton>
  </div>
</template>
