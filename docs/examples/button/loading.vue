<script setup lang="ts">
import { HrtButton } from '@hrt/components'

function handleClick(close: () => void) {
  setTimeout(() => {
    close()
  }, 2000)
}

async function asyncClick() {
  return new Promise((resolve) => {
    setTimeout(resolve, 2000)
  })
}
</script>

<template>
  <div class="hrt-flex hrt-gap-4">
    <HrtButton type="primary" :loading="true">
      主按钮
    </HrtButton>
    <HrtButton type="success" :inner-loading="true" @click="handleClick">
      成功按钮
    </HrtButton>
    <HrtButton type="info" :async-click="asyncClick">
      提示按钮
    </HrtButton>
    <HrtButton type="warning">
      告警按钮
    </HrtButton>
    <HrtButton type="danger">
      错误按钮
    </HrtButton>
    <HrtButton dashed>
      虚线按钮
    </HrtButton>
  </div>
</template>
