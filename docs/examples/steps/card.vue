<script lang="ts" setup>
import { ChatLineRound } from '@element-plus/icons-vue'
import { HrtStep, HrtSteps } from '@hrt/components'
import { ref } from 'vue'

const active = ref(-1)

function next() {
  if (active.value++ >= 4)
    active.value = -1
}
</script>

<template>
  <p>无icon和序号</p>
  <HrtSteps v-model:active="active" direction="vertical" card>
    <HrtStep title="状态文案1" />
    <HrtStep title="状态文案2" />
    <HrtStep title="状态文案3" />
    <HrtStep title="状态文案4" />
  </HrtSteps>
  <p>有icon和序号</p>
  <HrtSteps v-model:active="active" card card-seq>
    <HrtStep title="状态文案1" :icon="ChatLineRound" />
    <HrtStep title="状态文案2" />
    <HrtStep title="状态文案3" />
    <HrtStep title="状态文案4" />
  </HrtSteps>
  <div class="hrt-flex hrt-items-center hrt-gap-4 hrt-mt-4">
    <HrtButton @click="next">
      下一步
    </HrtButton>
    <span>当前步骤：{{ active }}</span>
  </div>
</template>
