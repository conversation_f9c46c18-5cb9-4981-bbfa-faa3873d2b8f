<script lang="ts" setup>
import { Edit, Message } from '@element-plus/icons-vue'
import { HrtStep, HrtSteps } from '@hrt/components'
import { ref } from 'vue'

const active = ref(-1)

function next() {
  if (active.value++ >= 3)
    active.value = -1
}
</script>

<template>
  <HrtSteps v-model:active="active">
    <HrtStep title="状态文案1" :icon="Message" description="状态说明文字1" />
    <HrtStep title="状态文案2" description="状态说明文字2">
      <template #icon>
        <el-icon class="hrt-step__status-icon">
          <Edit />
        </el-icon>
      </template>
    </HrtStep>
    <HrtStep title="状态文案3" description="状态说明文字3">
      <template #icon>
        <span>哈哈</span>
      </template>
    </HrtStep>
  </HrtSteps>

  <div class="hrt-flex hrt-items-center hrt-gap-4 hrt-mt-4">
    <HrtButton @click="next">
      下一步
    </HrtButton>
    <span>当前步骤：{{ active }}</span>
  </div>
</template>
