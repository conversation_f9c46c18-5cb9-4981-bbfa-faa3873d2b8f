<script lang="ts" setup>
import { HrtStep, HrtSteps } from '@hrt/components'
import { ref } from 'vue'

const active = ref(-1)

function next() {
  if (active.value++ >= 3)
    active.value = -1
}
</script>

<template>
  <p>第一步完成状态为success</p>
  <HrtSteps align-center :active="active">
    <HrtStep title="状态文案1" description="状态说明文字1状态说明文字1" />
    <HrtStep title="状态文案2" description="状态说明文字2" />
    <HrtStep title="状态文案3" description="状态说明文字3" />
  </HrtSteps>
  <p>第一步完成状态为error</p>
  <HrtSteps align-center :active="active">
    <HrtStep title="状态文案1" finish-status="error" description="状态说明文字1" />
    <HrtStep title="状态文案2" description="状态说明文字2" />
    <HrtStep title="状态文案3" description="状态说明文字3" />
  </HrtSteps>

  <div class="hrt-flex hrt-items-center hrt-gap-4 hrt-mt-4">
    <HrtButton @click="next">
      下一步
    </HrtButton>
    <span>当前步骤：{{ active }}</span>
  </div>
</template>
