<script setup>
import { HrtCollapseText } from '@hrt/components'

const text = '这是一段很长很长的文字这是一段很长很长的文字这是一段很长很长的文字这是一段很长很长的文字这是一段很长很长的文字这是一段很长很长的文字这是一段很长很长的文字这是一段很长很长的文字这是一段很长很长的文字这是一段很长很长的文字这是一段很长很长的文字这是一段很长很长的文字这'
const text2 = '这是一段很长很长的文字'
</script>

<template>
  <div class="hrt-space-y-4">
    <HrtCollapseText :text="text" :max-line="2" />
    <HrtCollapseText :text="text2" />
  </div>
</template>
