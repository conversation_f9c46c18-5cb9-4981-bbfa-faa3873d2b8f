<script lang="ts" setup>
import { HrtOverflowTooltip } from '@hrt/components'
</script>

<template>
  <div>不渲染slot template结构</div>
  <div class="hrt-bg-blue-100 hrt-mb-4 hrt-p-2">
    <HrtOverflowTooltip>
      <span>普通内容<span class="red">高亮内容高亮内容</span>普通内容普通内容普通内容普通内容普通内容普通内容普通内容普通内容普通内容</span>
    </HrtOverflowTooltip>
  </div>

  <div>渲染slot template结构</div>
  <div class="hrt-bg-blue-100 hrt-mb-4 hrt-p-2">
    <HrtOverflowTooltip render-slot-template>
      <span>普通内容<span class="red">高亮内容高亮内容</span>普通内容普通内容普通内容普通内容普通内容普通内容普通内容普通内容普通内容</span>
    </HrtOverflowTooltip>
  </div>

  <div>多行</div>
  <div class="hrt-bg-blue-100 hrt-mb-4 hrt-p-2">
    <HrtOverflowTooltip render-slot-template :rows="2">
      <span>普通内容<span class="red">高亮内容高亮内容</span>普通内容普通内普通内容普通内容普通内容普通内容普通内容普通内容普通容普通内容普通内容普通内容普通内容普通<span class="red">高亮内容高亮内容</span>内容普通内容普通内容普通内容普通内容普通内容普通内容普通内容</span>
    </HrtOverflowTooltip>
  </div>
</template>

<style lang="less" scoped>
.red {
  color: red;
}
</style>
