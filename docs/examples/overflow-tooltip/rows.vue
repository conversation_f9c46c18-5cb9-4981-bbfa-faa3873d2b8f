<script lang="ts" setup>
import { HrtOverflowTooltip } from '@hrt/components'

const text = '哈瑞特健康致力于为心血管疾病专家及病人搭建心血管病院外康复管理平台，组建以专家、慢病管理医师等为一体的专业院外康复管理团队，根据您的病情情况，按照标准化流程，为您提供远程健康数据采集、个性化健康管理方案、定期线上随访、高效远程咨询等服务'
</script>

<template>
  <div class="hrt-bg-blue-100 hrt-mb-4 hrt-p-2">
    <HrtOverflowTooltip :rows="2" :content="text" />
  </div>
  <div class="hrt-bg-blue-100 hrt-mb-4 hrt-p-2">
    <HrtOverflowTooltip :rows="3" :content="text" />
  </div>
</template>

<style lang="css">

</style>
