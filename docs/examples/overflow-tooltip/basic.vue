<script lang="ts" setup>
import { HrtOverflowTooltip } from '@hrt/components'

const text = '不使用HrtOverflowTooltip未超出'
const text1 = '不使用HrtOverflowTooltip不使用HrtOverflowTooltip不使用HrtOverflowTooltip'
const text2 = '使用HrtOverflowTooltip未超出'
const text3 = '使用HrtOverflowTooltip超出了使用HrtOverflowTooltip超出了使用HrtOverflowTooltip超出了'
</script>

<template>
  <div class="hrt-whitespace-nowrap hrt-w-[300px] hrt-bg-blue-100 hrt-mb-4 hrt-p-2">
    <span class="text">
      {{ text }}
    </span>
  </div>
  <div class="hrt-whitespace-nowrap hrt-w-[300px] hrt-bg-blue-100 hrt-mb-4 hrt-p-2">
    <span class="text">
      {{ text1 }}
    </span>
  </div>
  <div class="hrt-whitespace-nowrap hrt-w-[300px] hrt-bg-blue-100 hrt-mb-4 hrt-p-2">
    <HrtOverflowTooltip class="text" :content="text2" />
  </div>
  <div class="hrt-whitespace-nowrap hrt-w-[300px] hrt-bg-blue-100 hrt-p-2">
    <HrtOverflowTooltip class="text" :content="text3" />
  </div>
</template>

<style lang="less" scoped>
.text {
  color: var(--el-color-error-dark-2);
  font-size: 14px;
}
</style>
