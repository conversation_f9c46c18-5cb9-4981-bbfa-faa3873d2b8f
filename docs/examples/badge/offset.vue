<script lang="ts" setup>
import { HrtBadge } from '@hrt/components'
</script>

<template>
  <div class="hrt-space-x-8">
    <HrtBadge :value="9" :offset="[10, 0]">
      <div class="hrt-size-10 hrt-rounded hrt-bg-neutral-400" />
    </HrtBadge>
    <HrtBadge :value="99" :offset="[0, 10]">
      <div class="hrt-size-10 hrt-rounded hrt-bg-neutral-400" />
    </HrtBadge>
    <HrtBadge :value="100" :offset="10" :max="99">
      <div class="hrt-size-10 hrt-rounded hrt-bg-neutral-400" />
    </HrtBadge>
    <HrtBadge :value="1000" :offset="[10, -12]" :max="999">
      <div class="hrt-size-10 hrt-rounded hrt-bg-neutral-400" />
    </HrtBadge>
  </div>
</template>
