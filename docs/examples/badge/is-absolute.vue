<script lang="ts" setup>
import { HrtBadge, HrtText } from '@hrt/components'
</script>

<template>
  <div class="hrt-flex hrt-gap-4">
    <HrtBadge :is-dot="true" :is-absolute="false">
      <HrtText>文字内容</HrtText>
    </HrtBadge>

    <HrtBadge :value="9" :is-absolute="false">
      <HrtText>文字内容</HrtText>
    </HrtBadge>
    <HrtBadge :value="99" :is-absolute="false">
      <HrtText>文字内容</HrtText>
    </HrtBadge>
    <HrtBadge :value="100" :max="99" :is-absolute="false">
      <HrtText>文字内容</HrtText>
    </HrtBadge>
    <HrtBadge :value="1009" :max="999" :is-absolute="false">
      <HrtText>文字内容</HrtText>
    </HrtBadge>
  </div>
</template>
