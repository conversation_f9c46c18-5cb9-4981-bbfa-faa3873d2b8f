<script lang="ts" setup>
import { HrtBadge } from '@hrt/components'
</script>

<template>
  <div class="hrt-space-x-8">
    <HrtBadge :is-dot="true">
      <div class="hrt-size-10 hrt-rounded-full hrt-bg-neutral-400" />
    </HrtBadge>
    <HrtBadge :value="9" :offset="4">
      <div class="hrt-size-10 hrt-rounded-full hrt-bg-neutral-400" />
    </HrtBadge>
    <HrtBadge :value="99" :offset="4">
      <div class="hrt-size-10 hrt-rounded-full hrt-bg-neutral-400" />
    </HrtBadge>
    <HrtBadge :value="100" :offset="4" :max="99">
      <div class="hrt-size-10 hrt-rounded-full hrt-bg-neutral-400" />
    </HrtBadge>
    <HrtBadge :value="1000" :offset="4" :max="999">
      <div class="hrt-size-10 hrt-rounded-full hrt-bg-neutral-400" />
    </HrtBadge>
  </div>
  <div class="hrt-space-x-8">
    <HrtBadge :value="12">
      <hrt-button>评论</hrt-button>
    </HrtBadge>
    <HrtBadge :value="100" :max="99">
      <hrt-button>回复</hrt-button>
    </HrtBadge>
    <HrtBadge is-dot>
      <hrt-button>消息</hrt-button>
    </HrtBadge>
  </div>
</template>
