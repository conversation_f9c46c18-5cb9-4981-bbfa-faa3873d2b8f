<script lang="ts" setup>
import { HrtBadge } from '@hrt/components'
</script>

<template>
  <div class="hrt-space-x-4">
    <HrtBadge :is-dot="true" color="pink">
      <div class="hrt-size-10 hrt-rounded hrt-bg-neutral-400" />
    </HrtBadge>
    <HrtBadge :is-dot="true" color="blue">
      <div class="hrt-size-10 hrt-rounded hrt-bg-neutral-400" />
    </HrtBadge>
    <HrtBadge :value="89" color="orange">
      <div class="hrt-size-10 hrt-rounded hrt-bg-neutral-400" />
    </HrtBadge>
  </div>
</template>
