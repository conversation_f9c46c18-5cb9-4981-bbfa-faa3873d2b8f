<script setup lang="ts">
import { HrtTimeline, HrtTimelineItem } from '@hrt/components'
</script>

<template>
  <HrtTimeline>
    <HrtTimelineItem timestamp="2023-10-01 12:00:00" type="primary">
      活动一（完成）
    </HrtTimelineItem>
    <HrtTimelineItem timestamp="2023-10-02 14:30:00" type="success">
      活动二（成功）
    </HrtTimelineItem>
    <HrtTimelineItem timestamp="2023-10-03 16:45:00" center type="info">
      活动三（未开始）
    </HrtTimelineItem>
    <HrtTimelineItem timestamp="2023-10-04 18:20:00" placement="top" type="warning">
      活动四（告警）
    </HrtTimelineItem>
    <HrtTimelineItem timestamp="2023-10-05 20:00:00" type="danger" :hide-timestamp="true">
      活动五（错误）
    </HrtTimelineItem>
  </HrtTimeline>
</template>
