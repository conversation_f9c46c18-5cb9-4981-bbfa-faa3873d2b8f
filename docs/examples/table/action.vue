<script setup lang="ts">
import { HrtButton, HrtTable, HrtTableColumn } from '@hrt/components'
import { ref } from 'vue'

const tableData = ref([
  {
    name: '张三',
    age: 18,
    address: '北京市海淀区',
    status: '运行中',
  },
  {
    name: '李四',
    age: 20,
    address: '北京市朝阳区',
    status: '运行中',
  },
  {
    name: '王五',
    age: 22,
    address: '北京市昌平区',
    status: '已暂停',
  },
  {
    name: '赵六',
    age: 24,
    address: '北京市顺义区',
    status: '运行中',
  },
])

function handleEdit(row: any) {
  console.log('编辑', row)
}

function handleDelete(row: any) {
  console.log('删除', row)
}
</script>

<template>
  <HrtTable :data="tableData">
    <HrtTableColumn prop="name" label="姓名" />
    <HrtTableColumn prop="age" label="年龄" />
    <HrtTableColumn prop="address" label="地址" />
    <HrtTableColumn prop="status" label="状态" />
    <HrtTableColumn label="操作" width="220" :action-flex="true" :action-fixed-number="2">
      <template #default="{ row }">
        <HrtButton text type="primary" @click="handleEdit(row)">
          编辑
        </HrtButton>
        <HrtButton text type="danger" @click="handleDelete(row)">
          删除
        </HrtButton>
        <HrtButton text type="primary">
          编辑
        </HrtButton>
        <HrtButton text type="primary">
          编辑
        </HrtButton>
        <HrtButton text type="primary">
          编辑
        </HrtButton>
      </template>
    </HrtTableColumn>
  </HrtTable>
</template>

<style lang="css">
.vp-doc table {
  margin: unset !important;
}
.vp-doc th,
.vp-doc td {
  border: none !important;
}
.vp-doc tr {
  border-top: none !important;
}
</style>
