<script lang="ts" setup>
import { ArrowRight } from '@element-plus/icons-vue'
import { HrtBreadcrumb, HrtBreadcrumbItem } from '@hrt/components'
import { ref } from 'vue'

const value = ref<string>('ccc')

</script>

<template>
  <div class="breadcrumb-demo">
    <HrtBreadcrumb v-model="value" :separator-icon="ArrowRight">
      <HrtBreadcrumbItem value="aaa" :to="{ path: '/' }">
        一级界面
      </HrtBreadcrumbItem>
      <HrtBreadcrumbItem value="bbb">
        <a href="/">二级界面</a>
      </HrtBreadcrumbItem>
      <HrtBreadcrumbItem value="ccc">
        三级界面
      </HrtBreadcrumbItem>
      <HrtBreadcrumbItem value="ddd">
        四级界面
      </HrtBreadcrumbItem>
    </HrtBreadcrumb>
  </div>
</template>

<style lang="less">
.breadcrumb-demo {
  a {
    text-decoration: unset !important;
    color: unset !important;
    font-weight: normal !important;
  }
}
</style>
