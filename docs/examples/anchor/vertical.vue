<script lang="ts" setup>
import { HrtAnchor, HrtAnchorLink } from '@hrt/components'

function clickHandler(val: string) {
  console.log('$debug: clickHandler', val)
}
function changeHandler(val: string) {
  console.log('$debug: changeHandler', val)
}
</script>

<template>
  <div class="anchor-demo">
    <HrtAnchor :offset="70" direction="vertical" @click="clickHandler" @change="changeHandler">
      <HrtAnchorLink href="#anchor-属性">
        基础用法
      </HrtAnchorLink>
      <HrtAnchorLink href="#anchorlink-属性">
        水平模式
      </HrtAnchorLink>
      <HrtAnchorLink href="#anchor-事件">
        滚动容器
      </HrtAnchorLink>
    </HrtAnchor>
  </div>
</template>

<style lang="less">
.anchor-demo {
  .hrt-anchor-link .el-anchor__link {
    text-decoration: unset;
    color: unset;
    font-weight: normal;
  }
}
</style>
