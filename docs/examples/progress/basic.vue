<script setup lang="ts">
import { CircleCheckFilled } from '@element-plus/icons-vue'
import { HrtProgress } from '@hrt/components'
</script>

<template>
  <div class="hrt-space-y-4">
    <HrtProgress :percentage="70" />
    <HrtProgress :percentage="100" status="success" />
    <HrtProgress :percentage="70" status="exception" />
    <HrtProgress :percentage="70">
      <template #default>
        <div class="hrt-flex hrt-items-center hrt-gap-2">
          <span>70%</span>
          <el-icon class="hrt-text-green">
            <CircleCheckFilled />
          </el-icon>
        </div>
      </template>
    </HrtProgress>
  </div>
</template>
