<script setup lang="ts">
import { HrtDatePicker } from '@hrt/components'
import { ref } from 'vue'

const value1 = ref('')
const value2 = ref('')
const value3 = ref('')
const value4 = ref('')
const value5 = ref('')
</script>

<template>
  <div>
    <h4>datetime</h4>
    <HrtDatePicker
      v-model="value1"
      type="datetime"
      placeholder="请选择时间"
    />
  </div>
  <div>
    <h4>date</h4>
    <HrtDatePicker
      v-model="value2"
      type="date"
      placeholder="请选择日期"
    />
  </div>
  <div>
    <h4>week</h4>
    <HrtDatePicker
      v-model="value3"
      type="week"
      placeholder="请选择周"
    />
  </div>
  <div>
    <h4>month</h4>
    <HrtDatePicker
      v-model="value4"
      type="month"
      placeholder="请选择月"
    />
  </div>
  <div>
    <h4>year</h4>
    <HrtDatePicker
      v-model="value5"
      type="year"
      placeholder="请选择年"
    />
  </div>
</template>
