<script setup lang="ts">
import { HrtDatePicker } from '@hrt/components'
import { ref } from 'vue'

const value1 = ref<[string, string]>(['2025-05-09 05:55:30', '2025-05-27 05:55:30'])
const value2 = ref<[string, string]>(['2025-05-09', '2025-05-27'])
const value3 = ref<[string, string]>(['2025-01', '2025-05'])
const value4 = ref<[string, string]>(['2020', '2025'])
</script>

<template>
  <div>
    <h4>datetime range</h4>
    <HrtDatePicker
      v-model="value1"
      type="datetimerange"
      format="YYYY-MM-DD HH:mm:ss"
      start-placeholder="开始时间"
      end-placeholder="结束时间"
    />
  </div>
  <div>
    <h4>date range</h4>
    <HrtDatePicker
      v-model="value2"
      type="daterange"
      format="YYYY-MM-DD"
      start-placeholder="开始日期"
      end-placeholder="结束日期"
    />
  </div>
  <div>
    <h4>month range</h4>
    <HrtDatePicker
      v-model="value3"
      type="monthrange"
      format="YYYY-MM"
      start-placeholder="开始月"
      end-placeholder="结束月"
    />
  </div>
  <div>
    <h4>year range</h4>
    <HrtDatePicker
      v-model="value4"
      type="yearrange"
      format="YYYY"
      start-placeholder="开始年"
      end-placeholder="结束年"
    />
  </div>
</template>
