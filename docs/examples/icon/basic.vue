<script setup lang="ts">
import { HrtBriefcaseLine, HrtCustomer } from '@hrt/icons'

const icons = [
  {
    name: 'HrtBriefcaseLine',
    component: HrtBriefcaseLine,
  },
  {
    name: 'HrtCustomer',
    component: HrtCustomer,
  },
  {
    name: 'HrtBriefcaseLine',
    component: HrtBriefcaseLine,
  },
  {
    name: 'HrtCustomer',
    component: HrtCustomer,
  },
  {
    name: 'HrtBriefcaseLine',
    component: HrtBriefcaseLine,
  },
  {
    name: 'HrtCustomer',
    component: HrtCustomer,
  },
]
</script>

<template>
  <div class="grid grid-cols-4">
    <div
      v-for="(item, index) in icons"
      :key="item.name"
      class="p-4 border flex items-center justify-center flex-col cursor-pointer border-neutral-500"
      :class="{
        'border-l-0': index % 4 !== 0,
        'border-t-0': index >= 4,
      }"
    >
      <component :is="item.component" />
      <p class="font-base m-0">
        {{ item.name }}
      </p>
    </div>
  </div>
</template>
