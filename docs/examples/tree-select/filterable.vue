<script lang="ts" setup>
import { HrtTreeSelect } from '@hrt/components'
import { ref } from 'vue'

const value = ref()

const sourceData = [
  {
    value: '1',
    label: 'Level one 1',
    children: [
      {
        value: '1-1',
        label: 'Level two 1-1',
        children: [
          {
            value: '1-1-1',
            label: 'Level three xxxxxxxxxxxxx 1-1-1',
          },
        ],
      },
    ],
  },
  {
    value: '2',
    label: 'Level one 2',
    children: [
      {
        value: '2-1',
        label: 'Level two 2-1',
        children: [
          {
            value: '2-1-1',
            label: 'Level three 2-1-1',
          },
        ],
      },
      {
        value: '2-2',
        label: 'Level two 2-2',
        children: [
          {
            value: '2-2-1',
            label: 'Level three 2-2-1',
          },
        ],
      },
    ],
  },
  {
    value: '3',
    label: 'Level one 3',
    children: [
      {
        value: '3-1',
        label: 'Level two 3-1',
        children: [
          {
            value: '3-1-1',
            label: 'Level three 3-1-1',
          },
        ],
      },
      {
        value: '3-2',
        label: 'Level two 3-2',
        children: [
          {
            value: '3-2-1',
            label: 'Level three 3-2-1',
          },
        ],
      },
    ],
  },
]
const data = ref(sourceData)

function filterMethod(value: string) {
  data.value = [...sourceData].filter(item => item.label.includes(value))
}

const filterNodeMethod = (value: string, data: any) => data.label.includes(value)
</script>

<template>
  <HrtTreeSelect
    v-model="value"
    :data="data"
    filterable
    style="width: 240px"
  />
  <hr>
  filter method:
  <HrtTreeSelect
    v-model="value"
    :data="data"
    :filter-method="filterMethod"
    filterable
    style="width: 240px"
  />
  <hr>
  filter node method:
  <HrtTreeSelect
    v-model="value"
    :data="data"
    :filter-node-method="filterNodeMethod"
    filterable
    style="width: 240px"
  />
</template>
