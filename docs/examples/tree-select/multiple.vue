<script lang="ts" setup>
import { HrtTreeSelect } from '@hrt/components'
import { ref } from 'vue'

const value = ref()
const valueStrictly = ref()

const data = [
  {
    value: '1',
    label: '1',
    children: [
      {
        value: '1-1',
        label: '1-1',
        children: [
          {
            value: '1-1-1',
            label: '1-1-1',
          },
        ],
      },
    ],
  },
  {
    value: '2',
    label: '2',
    children: [
      {
        value: '2-1',
        label: '2-1',
        children: [
          {
            value: '2-1-1',
            label: '2-1-1',
          },
        ],
      },
      {
        value: '2-2',
        label: '2-2',
        children: [
          {
            value: '2-2-1',
            label: '2-2-1',
          },
        ],
      },
    ],
  },
  {
    value: '3',
    label: '3',
    children: [
      {
        value: '3-1',
        label: '3-1',
        children: [
          {
            value: '3-1-1',
            label: '3-1-1',
          },
        ],
      },
      {
        value: '3-2',
        label: '3-2',
        children: [
          {
            value: '3-2-1',
            label: '3-2-1',
          },
        ],
      },
    ],
  },
]
</script>

<template>
  <HrtTreeSelect
    v-model="value"
    :data="data"
    multiple
    :render-after-expand="false"
    style="width: 240px"
    clearable
  />
  <hr>
  with collapse-tags and collapse-tags-tooltip:
  <HrtTreeSelect
    v-model="value"
    :data="data"
    multiple
    collapse-tags
    collapse-tags-tooltip
    :render-after-expand="false"
    style="width: 240px"
    clearable
  />
  <hr>
  show checkbox:
  <HrtTreeSelect
    v-model="value"
    :data="data"
    multiple
    :render-after-expand="false"
    show-checkbox
    style="width: 240px"
    clearable
  />
  <hr>
  show checkbox with `check-strictly`:
  <HrtTreeSelect
    v-model="valueStrictly"
    :data="data"
    multiple
    :render-after-expand="false"
    show-checkbox
    check-strictly
    check-on-click-node
    style="width: 240px"
    clearable
  />
</template>
