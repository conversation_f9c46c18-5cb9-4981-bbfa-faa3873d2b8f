<script lang="ts" setup>
import { Edit, QuestionFilled } from '@element-plus/icons-vue'
import { ElIcon } from 'element-plus'
import { ref } from 'vue'

const inputMsg = ref('')
</script>

<template>
  <HrtChatBox
    v-model="inputMsg"
    title="医患沟通"
    :tools="['image', 'video', 'emoji']"
  >
    <div class="hrt-p-2">
      内容区
    </div>
    <template #tools>
      <ElIcon>
        <Edit />
      </ElIcon>
      <ElIcon>
        <QuestionFilled />
      </ElIcon>
    </template>
  </HrtChatBox>
</template>

<style lang="less">
</style>
