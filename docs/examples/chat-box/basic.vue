<script lang="ts" setup>
import type { HrtChatBoxFile } from '@hrt/components/src/chat-box/type'
import { ElMessage } from 'element-plus'
import { ref } from 'vue'

const inputMsg = ref('')
function onFileChange(val: HrtChatBoxFile) {
  console.log('file-change:', val)
}
function onEmojiChange(val: string) {
  console.log('emoji-change:', val)
}
</script>

<template>
  <HrtChatBox
    v-model="inputMsg"
    title="医患沟通"
    @file-change="onFileChange"
    @emoji-change="onEmojiChange"
    @chat-history-click="() => ElMessage.info('点击了聊天记录')"
    @medication-click="() => ElMessage.info('点击了用药助手')"
    @common-expressions-click="() => ElMessage.info('点击了常用语')"
    @question-answer-click="() => ElMessage.info('点击了问题答复')"
  >
    <div class="hrt-p-2">
      内容区
    </div>
  </HrtChatBox>
</template>

<style lang="less">
</style>
