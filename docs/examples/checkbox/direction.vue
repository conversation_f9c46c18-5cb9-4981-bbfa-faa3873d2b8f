<script setup lang="ts">
import { HrtCheckbox, HrtCheckboxGroup } from '@hrt/components'
import { ref } from 'vue'

const modelValue = ref(['value1', 'value2'])
</script>

<template>
  <div>
    当前选中值：{{ modelValue }}
  </div>
  <HrtCheckboxGroup v-model="modelValue" direction="vertical">
    <HrtCheckbox label="选项1" value="value1" />
    <HrtCheckbox label="选项2" value="value2" />
    <HrtCheckbox label="选项3" value="value3" />
    <HrtCheckbox label="选项4" value="value4" />
    <HrtCheckbox label="选项5" value="value5" disabled />
  </HrtCheckboxGroup>
</template>
