<script setup lang="ts">
import type { HrtDrawerProps } from '@hrt/components'
import { HrtButton, HrtDrawer, HrtRadio, HrtRadioGroup } from '@hrt/components'
import { ref } from 'vue'

const visible = ref(false)

function open() {
  visible.value = true
}

const size = ref<HrtDrawerProps['size']>('middle')
</script>

<template>
  <HrtRadioGroup v-model="size">
    <HrtRadio value="large">
      large
    </HrtRadio>
    <HrtRadio value="middle">
      middle
    </HrtRadio>
    <HrtRadio value="small">
      small
    </HrtRadio>
  </HrtRadioGroup>
  <HrtButton type="primary" class="hrt-ml-4" @click="open">
    打开抽屉
  </HrtButton>
  <HrtDrawer v-model="visible" title="抽屉" :size="size">
    <div>抽屉内容</div>
  </HrtDrawer>
</template>
