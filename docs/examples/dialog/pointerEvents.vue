<script setup lang="ts">
import { HrtButton, HrtDialog } from '@hrt/components'
import { shallowRef } from 'vue'

const show = shallowRef(false)
</script>

<template>
  <HrtButton @click="show = true">
    展示弹窗
  </HrtButton>
  <HrtDialog v-model="show" title="标题" size="middle" :disable-pointer-events="true">
    <p>这是一段内容</p>
    <template #footer>
      <div class="dialog-footer">
        <HrtButton @click="show = false">
          Cancel
        </HrtButton>
        <HrtButton type="primary" @click="show = false">
          Confirm
        </HrtButton>
      </div>
    </template>
  </HrtDialog>
</template>
