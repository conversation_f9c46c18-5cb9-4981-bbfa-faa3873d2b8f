<script setup lang="ts">
import { EditPen } from '@element-plus/icons-vue'
import { HrtButton, HrtDialog } from '@hrt/components'
import { ElIcon } from 'element-plus'
import { shallowRef } from 'vue'

const show = shallowRef(false)
</script>

<template>
  <HrtButton @click="show = true">
    展示弹窗
  </HrtButton>
  <HrtDialog v-model="show" title="标题" size="middle" :height="600">
    <template #header>
      <div class="hrt-flex hrt-items-center hrt-gap-2 hrt-mr-6">
        <ElIcon>
          <EditPen />
        </ElIcon>
        <p style="margin: 0;">
          自定义标题自定义标题自定义标题
        </p>
      </div>
    </template>
    <p style="margin-top: 0;">
      这是一段内容
    </p>
    <template #footer>
      <div class="dialog-footer">
        <HrtButton @click="show = false">
          Cancel
        </HrtButton>
        <HrtButton type="primary" @click="show = false">
          Confirm
        </HrtButton>
      </div>
    </template>
  </HrtDialog>
</template>
