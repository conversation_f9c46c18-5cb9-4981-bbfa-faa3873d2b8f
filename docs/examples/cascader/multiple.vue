<script lang="ts" setup>
import { HrtCascader } from '@hrt/components'

const props = { multiple: true }
const options = [...Array.from({ length: 12 })].map((_, i) => {
  return {
    value: `zhinan${i + 1}`,
    label: `指南${i + 1}`,
    children: [...Array.from({ length: 5 })].map((_, j) => {
      return {
        value: `shejiyuanze${i + 1}_${j + 1}`,
        label: `设计原则${i + 1}_${j + 1}`,
        children: [
          {
            value: `yizhi${i + 1}_${j + 1}`,
            label: `一致${i + 1}_${j + 1}`,
          },
          {
            value: `fankui${i + 1}_${j + 1}`,
            label: `反馈${i + 1}_${j + 1}`,
          },
        ],
      }
    }),
  }
})
</script>

<template>
  <div class="hrt-w-[240px]">
    <p>显示所有已选标签 (默认)</p>
    <HrtCascader :options="options" :props="props" :show-all-levels="false" clearable />
  </div>
  <div class="hrt-w-[240px]">
    <p>折叠标签</p>
    <HrtCascader :options="options" :props="props" collapse-tags clearable />
  </div>
  <div class="hrt-w-[240px]">
    <p>折叠标签 + tooltip</p>
    <HrtCascader
      :options="options"
      :props="props"
      collapse-tags
      collapse-tags-tooltip
      clearable
    />
  </div>
  <div class="hrt-w-[240px]">
    <p>最多展示标签个数</p>
    <HrtCascader
      :options="options"
      :props="props"
      :show-all-levels="false"
      collapse-tags
      collapse-tags-tooltip
      :max-collapse-tags="3"
      clearable
    />
  </div>
</template>

<style lang="less" scoped>
</style>
