<script lang="ts" setup>
import { HrtCascader } from '@hrt/components'
import { ref } from 'vue'

const val = ref('')
const options = [...Array.from({ length: 3 })].map((_, i) => {
  return {
    value: `zhinan${i + 1}`,
    label: `指南${i + 1}`,
    disabled: i === 1,
    children: [...Array.from({ length: 5 })].map((_, j) => {
      return {
        value: `shejiyuanze${i + 1}_${j + 1}`,
        label: `设计原则${i + 1}_${j + 1}`,
        disabled: j === 3,
        children: [
          {
            value: `yizhi${i + 1}_${j + 1}`,
            label: `一致${i + 1}_${j + 1}`,
            disabled: true,
          },
          {
            value: `fankui${i + 1}_${j + 1}`,
            label: `反馈${i + 1}_${j + 1}`,
          },
        ],
      }
    }),
  }
})
</script>

<template>
  <div class="hrt-w-[240px]">
    <HrtCascader v-model="val" :options="options" clearable />
  </div>
</template>

<style lang="less" scoped>
</style>
