<script lang="ts" setup>
import { HrtCascader } from '@hrt/components'
import { ref } from 'vue'

const val = ref('')
const options = [...Array.from({ length: 12 })].map((_, i) => {
  return {
    value: `zhinan${i + 1}`,
    label: `指南${i + 1}`,
    children: [...Array.from({ length: 5 })].map((_, j) => {
      return {
        value: `shejiyuanze${i + 1}_${j + 1}`,
        label: `设计原则${i + 1}_${j + 1}`,
        children: [
          {
            value: `yizhi${i + 1}_${j + 1}`,
            label: `一致${i + 1}_${j + 1}`,
          },
          {
            value: `fankui${i + 1}_${j + 1}`,
            label: `反馈${i + 1}_${j + 1}`,
          },
        ],
      }
    }),
  }
})
</script>

<template>
  <div>
    <p>默认</p>
    <HrtCascader v-model="val" :options="options" placeholder="100%" />
  </div>
  <div class="hrt-mt-4">
    <p>大尺寸</p>
    <HrtCascader v-model="val" size="large" :options="options" placeholder="364px" />
  </div>
  <div class="hrt-mt-4">
    <p>中尺寸</p>
    <HrtCascader v-model="val" size="default" :options="options" placeholder="240px" />
  </div>
  <div class="hrt-mt-4">
    <p>小尺寸</p>
    <HrtCascader v-model="val" size="small" :options="options" placeholder="116px" />
  </div>
</template>

<style lang="less" scoped>
</style>
