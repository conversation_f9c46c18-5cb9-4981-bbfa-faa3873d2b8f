<script lang="ts" setup>
import type { HrtFormInstance } from '@hrt/components'
import { HrtButton, HrtCascader, HrtDatePicker, HrtForm, HrtFormItem, HrtInput, HrtInputNumber, HrtOption, HrtSelect, HrtTreeSelect } from '@hrt/components'
import { ref } from 'vue'

const formRef = ref<HrtFormInstance>()
const form = ref({
  name: '',
  age: null,
  date: '',
  selection: '',
  cascader: '',
  tree: '',
  remark: '',
})

const rules = {
  name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  age: [{ required: true, message: '请输入年龄', trigger: 'blur' }],
  date: [{ required: true, message: '请选择日期', trigger: 'change' }],
  selection: [{ required: true, message: '请选择选项', trigger: 'change' }],
  cascader: [{ required: true, message: '请选择级联选项', trigger: 'change' }],
  tree: [{ required: true, message: '请选择树形选项', trigger: 'change' }],
  remark: [{ required: true, message: '请输入备注信息', trigger: 'blur' }],
}

const options = [...Array.from({ length: 12 })].map((_, i) => {
  return {
    value: `zhinan${i + 1}`,
    label: `指南${i + 1}`,
    children: [...Array.from({ length: 5 })].map((_, j) => {
      return {
        value: `shejiyuanze${i + 1}_${j + 1}`,
        label: `设计原则${i + 1}_${j + 1}`,
        children: [
          {
            value: `yizhi${i + 1}_${j + 1}`,
            label: `一致${i + 1}_${j + 1}`,
          },
          {
            value: `fankui${i + 1}_${j + 1}`,
            label: `反馈${i + 1}_${j + 1}`,
          },
        ],
      }
    }),
  }
})

const treeData = [
  {
    value: '1',
    label: 'Level one 1',
    children: [
      {
        value: '1-1',
        label: 'Level two 1-1',
        children: [
          {
            value: '1-1-1',
            label: 'Level three 1-1-1',
          },
        ],
      },
    ],
  },
  {
    value: '2',
    label: 'Level one 2',
    children: [
      {
        value: '2-1',
        label: 'Level two 2-1',
        children: [
          {
            value: '2-1-1',
            label: 'Level three 2-1-1',
          },
        ],
      },
      {
        value: '2-2',
        label: 'Level two 2-2',
        children: [
          {
            value: '2-2-1',
            label: 'Level three 2-2-1',
          },
        ],
      },
    ],
  },
  {
    value: '3',
    label: 'Level one 3',
    children: [
      {
        value: '3-1',
        label: 'Level two 3-1',
        children: [
          {
            value: '3-1-1',
            label: 'Level three 3-1-1',
          },
        ],
      },
      {
        value: '3-2',
        label: 'Level two 3-2',
        children: [
          {
            value: '3-2-1',
            label: 'Level three 3-2-1',
          },
        ],
      },
    ],
  },
]

function onSubmit() {
  formRef.value?.validate((valid) => {
    if (valid) {
      console.log('提交成功')
    }
  })
}
</script>

<template>
  <HrtForm ref="formRef" :model="form" :rules="rules" label-width="auto">
    <HrtFormItem label="姓名" prop="name">
      <HrtInput v-model="form.name" placeholder="请输入" />
    </HrtFormItem>
    <HrtFormItem label="年龄" prop="age">
      <HrtInputNumber v-model="form.age" placeholder="请输入" />
    </HrtFormItem>
    <HrtFormItem label="入院日期" prop="date">
      <HrtDatePicker v-model="form.date" type="daterange" />
    </HrtFormItem>
    <HrtFormItem label="选项" prop="selection">
      <HrtSelect v-model="form.selection" clearable>
        <HrtOption label="选项1" value="1" />
        <HrtOption label="选项2" value="2" />
        <HrtOption label="选项3" value="3" />
      </HrtSelect>
    </HrtFormItem>
    <HrtFormItem label="级联选项" prop="cascader">
      <HrtCascader v-model="form.cascader" :options="options" clearable />
    </HrtFormItem>
    <HrtFormItem label="树形选择" prop="tree">
      <HrtTreeSelect v-model="form.tree" :data="treeData" clearable />
    </HrtFormItem>
    <HrtFormItem label="备注信息" prop="remark">
      <HrtInput v-model="form.remark" type="textarea" placeholder="请输入" />
    </HrtFormItem>

    <HrtFormItem>
      <HrtButton type="primary" @click="onSubmit">
        提交
      </HrtButton>
    </HrtFormItem>
  </HrtForm>
</template>
