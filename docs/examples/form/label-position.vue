<script lang="ts" setup>
import { HrtCheckbox, HrtCheckboxGroup, HrtDatePicker, HrtForm, HrtFormItem, HrtInput, HrtOption, HrtRadio, HrtRadioGroup, HrtSelect, HrtSwitch } from '@hrt/components'
import { ref } from 'vue'

const form = ref({
  name: '',
  date: '',
  gender: 'male',
  selection: '',
  disease: [2, 3],
  status: true,
  remark: '',
})
</script>

<template>
  <HrtForm :model="form" label-position="top">
    <HrtFormItem label="姓名" prop="name">
      <HrtInput v-model="form.name" placeholder="请输入" />
    </HrtFormItem>
    <HrtFormItem label="入院日期" prop="date">
      <HrtDatePicker v-model="form.date" type="daterange" />
    </HrtFormItem>
    <HrtFormItem label="选项" prop="selection">
      <HrtSelect v-model="form.selection">
        <HrtOption label="选项1" value="1" />
        <HrtOption label="选项2" value="2" />
        <HrtOption label="选项3" value="3" />
      </HrtSelect>
    </HrtFormItem>
    <HrtFormItem label="患者性别" prop="gender">
      <HrtRadioGroup v-model="form.gender">
        <HrtRadio label="男" value="male" />
        <HrtRadio label="女" value="female" />
      </HrtRadioGroup>
    </HrtFormItem>
    <HrtFormItem label="主诉/现病史" prop="disease">
      <HrtCheckboxGroup v-model="form.disease">
        <HrtCheckbox label="高血压" :value="1" />
        <HrtCheckbox label="糖尿病" :value="2" />
        <HrtCheckbox label="冠心病" :value="3" />
      </HrtCheckboxGroup>
    </HrtFormItem>
    <HrtFormItem label="状态" prop="status">
      <HrtSwitch v-model="form.status" />
    </HrtFormItem>
    <HrtFormItem label="备注信息" prop="remark">
      <HrtInput v-model="form.remark" type="textarea" placeholder="请输入" />
    </HrtFormItem>
  </HrtForm>
</template>
