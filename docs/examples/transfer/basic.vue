<script setup lang="ts">
import { HrtTransfer } from '@hrt/components'

import { ref } from 'vue'

interface Option {
  key: number
  label: string
  disabled: boolean
}

function generateData() {
  const data: Option[] = []
  for (let i = 1; i <= 8; i++) {
    data.push({
      key: i,
      label: `Option ${i}`,
      disabled: i === 6,
    })
  }
  return data
}

const data = ref<Option[]>(generateData())
const value = ref([1, 2])
</script>

<template>
  <HrtTransfer v-model="value" :data="data" :titles="['待选区', '已选区']" />
</template>
