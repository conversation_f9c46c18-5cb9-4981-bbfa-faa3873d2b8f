import mdContainer from 'markdown-it-container'
import { defineConfig } from 'vitepress'
import createDemoContainer from './plugins/demo'
import tagPlugin from './plugins/tag'
import tooltipPlugin from './plugins/tooltip'

// https://vitepress.dev/reference/site-config
export default defineConfig({
  title: '哈瑞特前端组件库',
  description: '哈瑞特前端组件库',
  lang: 'zh-CN',
  appearance: false,
  themeConfig: {
    // https://vitepress.dev/reference/default-theme-config
    nav: [
      { text: '首页', link: '/' },
      { text: '指南', link: '/guide/installation' },
      { text: '组件', link: '/components/icon' },
      { text: '工具函数', link: '/tools' },
      { text: '移动端组件库', link: 'https://h5-comp.hrttest.cn/#/home' },
    ],
    sidebar: {
      '/guide/': [
        {
          text: '指南',
          items: [
            { text: '设计', link: '/guide/design' },
            { text: '安装', link: '/guide/installation' },
            { text: '快速开始', link: '/guide/quickstart' },
          ],
        },
        {
          text: '开发',
          items: [
            { text: '开发指南', link: '/guide/dev-guide' },
            { text: '更新日志', link: '/guide/changelog' },
          ],
        },
      ],
      '/components/': [
        {
          text: '组件',
          items: [
            { text: 'Icon 图标', link: '/components/icon' },
            { text: 'Button 按钮', link: '/components/button' },
            { text: 'Layout 布局', link: '/components/layout' },
            { text: 'Text 文本', link: '/components/text' },
            { text: 'Dialog 对话框', link: '/components/dialog' },
          ],
        },
        {
          text: 'Form 表单组件',
          items: [
            { text: 'Cascader 级联选择器', link: '/components/cascader' },
            { text: 'Form 表单', link: '/components/form' },
            { text: 'Input 输入框', link: '/components/input' },
            { text: 'InputNumber 数字输入框', link: '/components/input-number' },
            { text: 'Radio 单选框', link: '/components/radio' },
            { text: 'Upload 上传', link: '/components/upload' },
            { text: 'Checkbox 多选框', link: '/components/checkbox' },
            { text: 'Select 选择器', link: '/components/select' },
            { text: 'Switch 开关', link: '/components/switch' },
            { text: 'DatePicker 日期选择器', link: '/components/date-picker' },
            { text: 'Transfer 穿梭框', link: '/components/transfer' },
            { text: 'TreeSelect 树形选择', link: '/components/tree-select' },
            { text: 'Slider 滑块', link: '/components/slider' },
          ],
        },
        {
          text: 'Data 数据展示',
          items: [
            { text: 'Table 表格', link: '/components/table' },
            { text: 'Tag 标签', link: '/components/tag' },
            { text: 'Spin 加载', link: '/components/spin' },
            { text: 'Badge 徽标', link: '/components/badge' },
            { text: 'Collapse 折叠面板', link: '/components/collapse' },
            { text: 'CollapseText 折叠文本', link: '/components/collapse-text' },
            { text: 'Drawer 抽屉', link: '/components/drawer' },
            { text: 'Pagination 分页', link: '/components/pagination' },
            { text: 'Progress 进度条', link: '/components/progress' },
            { text: 'Timeline 时间线', link: '/components/timeline' },
          ],
        },
        {
          text: 'Navigation 导航',
          items: [
            { text: 'Anchor 锚点', link: '/components/anchor' },
            { text: 'Breadcrumb 面包屑', link: '/components/breadcrumb' },
            { text: 'Dropdown 下拉菜单', link: '/components/dropdown' },
            { text: 'Tabs 标签页', link: '/components/tabs' },
            { text: 'Steps 步骤条', link: '/components/steps' },
          ],
        },
        {
          text: 'Feedback 反馈组件',
          items: [
            { text: 'Alert 提示', link: '/components/alert' },
            { text: 'Notice 通知', link: '/components/notice' },
            { text: 'Tooltip 文字提示', link: '/components/tooltip' },
            { text: 'OverflowTooltip 超长文本提示', link: '/components/overflow-tooltip' },
            { text: 'Message 消息提示', link: '/components/message' },
            { text: 'Popover 弹出框', link: '/components/popover' },
          ],
        },
        {
          text: 'Others 其他',
          items: [
            { text: 'ChatBox 聊天框', link: '/components/chat-box' },
            { text: 'ChatInput 聊天输入框', link: '/components/chat-input' },
            { text: 'ImgPreview 图片预览', link: '/components/img-preview' },
          ],
        },
      ],
      '/tools/': [
        { text: '指南', link: '/tools/' },
        {
          text: '日期相关',
          items: [
            { text: 'formatDate 日期格式化', link: '/tools/format-date' },
            { text: 'getDateStart 日期开始时间', link: '/tools/get-date-start' },
            { text: 'getDateEnd 日期结束时间', link: '/tools/get-date-end' },
            { text: 'getYearStart 当年开始时间', link: '/tools/get-year-start' },
            { text: 'getYearEnd 当年结束时间', link: '/tools/get-year-end' },
            { text: 'getMonthStart 当月开始时间', link: '/tools/get-month-start' },
            { text: 'getMonthEnd 当月结束时间', link: '/tools/get-month-end' },
            { text: 'parseSeconds 秒数解析', link: '/tools/parse-seconds' },
          ],
        },
        {
          text: '验证相关',
          items: [
            { text: 'isDate 日期验证', link: '/tools/is-date' },
            { text: 'isEmail 邮箱验证', link: '/tools/is-email' },
            { text: 'isPhone 手机号验证', link: '/tools/is-phone' },
            { text: 'isEmpty 空值验证', link: '/tools/is-empty' },
            { text: 'isIDCard 身份证验证', link: '/tools/is-id-card' },
          ],
        },
        {
          text: '格式化相关',
          items: [
            { text: 'moneyFormat 金额格式化', link: '/tools/money-format' },
          ],
        },
        {
          text: '工具类相关',
          items: [
            { text: 'generateKey 随机字符串生成', link: '/tools/generate-key' },
          ],
        },
      ],
    },
    socialLinks: [
      { icon: 'gitlab', link: 'https://gitlab.hrttest.cn/frontend/common/HRT-components-web' },
    ],
    outline: {
      label: '页面导航',
      level: [2, 3],
    },
    docFooter: {
      prev: '上一页',
      next: '下一页',
    },
  },
  markdown: {
    config: (md) => {
      md.use(mdContainer, 'demo', createDemoContainer(md))
      md.use(tagPlugin)
      md.use(tooltipPlugin)
    },
  },
  vite: {
    css: {
      preprocessorOptions: {
        scss: {
          api: 'modern-compiler',
        },
      },
    },
  },
})
