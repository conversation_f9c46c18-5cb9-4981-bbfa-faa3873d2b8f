import mdContainer from 'markdown-it-container'
import { defineConfig } from 'vitepress'
import createDemoContainer from './plugins/demo'

// https://vitepress.dev/reference/site-config
export default defineConfig({
  title: '哈瑞特前端组件库',
  description: '哈瑞特前端组件库',
  lang: 'zh-CN',
  appearance: false,
  themeConfig: {
    // https://vitepress.dev/reference/default-theme-config
    nav: [
      { text: '首页', link: '/' },
      { text: '指南', link: '/guide/installation' },
      { text: '组件', link: '/components/icon' },
    ],
    sidebar: {
      '/guide/': [
        {
          text: '指南',
          items: [
            { text: '设计', link: '/guide/design' },
            { text: '安装', link: '/guide/installation' },
            { text: '快速开始', link: '/guide/quickstart' },
          ],
        },
      ],
      '/components/': [
        {
          text: '组件',
          items: [
            { text: 'Icon 图标', link: '/components/icon' },
            { text: 'Button 按钮', link: '/components/button' },
            { text: 'Layout 布局', link: '/components/layout' },
            { text: 'Text 文本', link: '/components/text' },
          ],
        },
      ],
    },
    socialLinks: [
      { icon: 'gitlab', link: 'https://gitlab.hrttest.cn/frontend/common/HRT-components-web' },
    ],
    outline: {
      label: '页面导航',
    },
    docFooter: {
      prev: '上一页',
      next: '下一页',
    },
  },
  markdown: {
    config: (md) => {
      md.use(mdContainer, 'demo', createDemoContainer(md))
    },
  },
})
