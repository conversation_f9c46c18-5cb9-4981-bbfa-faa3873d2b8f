import type { Theme } from 'vitepress'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import HrtUI from '@hrt/components'
import HrtIcons from '@hrt/icons'
import ElementPlus from 'element-plus'
import DefaultTheme from 'vitepress/theme'
import HrtColors from '../vitepress/colors.vue'
import vpDemo from '../vitepress/components/vp-demo.vue'
import Home from '../vitepress/home.vue'
import HrtRadius from '../vitepress/radius.vue'
import HrtShadow from '../vitepress/shadow.vue'
import 'element-plus/dist/index.css'
import './styles.css'

export default {
  extends: DefaultTheme,
  enhanceApp({ app }) {
    app.component('Demo', vpDemo)
    app.component('Home', Home)
    app.component('HrtColors', HrtColors)
    app.component('HrtRadius', HrtRadius)
    app.component('HrtShadow', HrtShadow)
    app.use(ElementPlus)
    app.use(HrtUI)
    app.use(HrtIcons)
    for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
      app.component(key, component)
    }
    const examples = import.meta.glob('../../examples/**/*.vue', {
      eager: true,
    })

    Object.entries(examples).forEach(([path, module]: [string, any]) => {
      const name = path
        ?.split('/examples/')
        ?.pop()
        .replace(/\.vue$/, '')
        .split('/')
        .join('-')

      app.component(`ep-${name}`, module.default)
    })
    // app is the Vue 3 app instance from `createApp()`. router is VitePress'
    // custom router. `siteData`` is a `ref`` of current site-level metadata.
  },
} satisfies Theme
