import type { Theme } from 'vitepress'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import HrtUI from '@hrt/components'
import HrtIcons from '@hrt/icons'
import ElementPlus from 'element-plus'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import DefaultTheme from 'vitepress/theme'
import HrtColors from '../vitepress/colors.vue'
import ApiTyping from '../vitepress/components/vp-api-typing.vue'
import vpDemo from '../vitepress/components/vp-demo.vue'
import Home from '../vitepress/home.vue'
import HrtRadius from '../vitepress/radius.vue'
import HrtShadow from '../vitepress/shadow.vue'
import Toc from './components/Toc.vue'
import ComponentLayout from './layout/ComponentLayout.vue'
import './styles.css'

export default {
  extends: DefaultTheme,
  async enhanceApp({ app }) {
    app.component('Demo', vpDemo)
    app.component('Home', Home)
    app.component('HrtColors', HrtColors)
    app.component('HrtRadius', HrtRadius)
    app.component('HrtShadow', HrtShadow)
    app.component('ComponentLayout', ComponentLayout)
    app.component('Toc', Toc)
    app.component('ApiTyping', ApiTyping)
    app.use(ElementPlus, { locale: zhCn })
    app.use(HrtUI)
    app.use(HrtIcons)
    for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
      app.component(key, component)
    }
    // if (!import.meta.env.SSR) {
    //   const HrtComponents = await import('@hrt/components')
    //   app.use(HrtComponents.default)
    //   const examples = await import.meta.glob('../../examples/**/*.vue', {
    //     eager: true,
    //   })
    //   Object.entries(examples).forEach(([path, module]: [string, any]) => {
    //     const name = path
    //       ?.split('/examples/')
    //       ?.pop()
    //       ?.replace(/\.vue$/, '')
    //       .split('/')
    //       .join('-')

    //     app.component(`ep-${name}`, module.default)
    //   })
    // }
    const examples = import.meta.glob('../../examples/**/*.vue', {
      eager: true,
    })

    Object.entries(examples).forEach(([path, module]: [string, any]) => {
      const name = path
        ?.split('/examples/')
        ?.pop()
        ?.replace(/\.vue$/, '')
        .split('/')
        .join('-')

      app.component(`ep-${name}`, module.default)
    })
    // app is the Vue 3 app instance from `createApp()`. router is VitePress'
    // custom router. `siteData`` is a `ref`` of current site-level metadata.
  },
} satisfies Theme
