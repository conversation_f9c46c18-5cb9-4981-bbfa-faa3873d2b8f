<script setup lang="ts">
defineOptions({
  name: 'HrtColors',
})

const colors = [
  {
    name: '雾松绿',
    enName: 'Fog pine green',
    colors: [
      '#EDFCFF',
      '#8CD6E6',
      '#3BB4CC',
      '#18A3BF',
      '#10849C',
      '#086578',
      '#034754',
    ],
  },
]
</script>

<template>
  <div class="grid grid-cols-1 gap-4">
    <div v-for="color in colors" :key="color.name">
      <p class="text-lg font-bold m-0">
        {{ color.name }}/{{ color.enName }}
      </p>
      <div class="grid grid-cols-7">
        <div v-for="(c, index) in color.colors" :key="c" class="flex flex-col">
          <div
            class="w-full h-[100px] rounded-full" :style="{
              backgroundColor: c,
              height: '100px',
              width: '100%',
            }"
          />
          <div class="text-sm">
            {{ (index + 1) * 100 }}
          </div>
          <div>
            {{ c }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.grid-cols-7 {
  grid-template-columns: repeat(7, minmax(0, 1fr));
}
</style>
