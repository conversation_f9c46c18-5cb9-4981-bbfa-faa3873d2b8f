<script setup lang="ts">
defineOptions({
  name: 'HrtColors',
})

const colors = [
  {
    name: '雾松绿',
    enName: 'Fog pine green',
    colors: [
      {
        hex: '#EDFCFF',
        class: '100',
      },
      {
        hex: '#8CD6E6',
        class: '200',
      },
      {
        hex: '#3BB4CC',
        class: '300',
      },
      {
        hex: '#18A3BF',
        class: '400',
      },
      {
        hex: '#10849C',
        class: '500',
      },
      {
        hex: '#086578',
        class: '600',
      },
      {
        hex: '#034754',
        class: '700',
      },
    ],
  },
]
</script>

<template>
  <div class="grid grid-cols-1 gap-4">
    <div v-for="color in colors" :key="color.name">
      <p class="text-lg font-bold m-0">
        {{ color.name }}/{{ color.enName }}
      </p>
      <div class="grid grid-cols-7">
        <div v-for="(c, index) in color.colors" :key="c.hex" class="flex flex-col">
          <div
            class="w-full h-[100px] rounded-full" :style="{
              backgroundColor: c.hex,
              height: '100px',
              width: '100%',
            }"
          />
          <div class="text-sm">
            {{ (index + 1) * 100 }}
          </div>
          <div>
            {{ c.hex }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.grid-cols-7 {
  grid-template-columns: repeat(7, minmax(0, 1fr));
}
</style>
