<script setup lang="ts">
import VPDemo from './vp-demo.vue'

const props = defineProps<{
  source: string
  path: string
  rawSource: string
  description: string
}>()
</script>

<template>
  <ClientOnly>
    <!-- 只能在浏览器环境渲染的内容 -->
    <VPDemo
      :source="props.source"
      :path="props.path"
      :raw-source="props.rawSource"
      :description="props.description"
    />
  </ClientOnly>
</template>
