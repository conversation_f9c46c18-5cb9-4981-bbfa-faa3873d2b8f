<script setup lang="ts">
defineOptions({
  name: 'HrtShadow',
})
</script>

<template>
  <div class="grid grid-cols-4 gap-2">
    <div>
      <div class="w-[100px] h-[100px] shadow" />
      <h4 class="my-2">
        小投影
      </h4>
      <p class="text-xs">
        适合用在非模态弹窗
      </p>
    </div>
    <div>
      <div class="w-[100px] h-[100px] shadow-md" />
      <h4 class="my-2">
        中投影
      </h4>
      <p class="text-xs">
        适合用在模态弹窗里小、中弹窗
      </p>
    </div>
    <div>
      <div class="w-[100px] h-[100px] shadow-lg" />
      <h4 class="my-2">
        大投影
      </h4>
      <p class="text-xs">
        适合用在模态弹窗里大、超大弹窗
      </p>
    </div>
    <div>
      <div class="w-[100px] h-[100px] shadow-sm" />
      <h4 class="my-2">
        底层卡片投影
      </h4>
      <p class="text-xs">
        只适用于最底层信息卡片
      </p>
    </div>
  </div>
</template>
