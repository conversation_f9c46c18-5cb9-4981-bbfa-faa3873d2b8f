<script setup lang="ts">
defineOptions({
  name: 'HrtRadius',
})
</script>

<template>
  <div>
    <div class="grid grid-cols-1 gap-4">
      <p>圆角-4px：适合用在上层弹窗、文字</p>
      <div
        class="w-full h-36 rounded border"
        :style="{
          borderRadius: '4px',
          height: '100px',
          borderColor: '#DCDFE6',
        }"
      />
      <p>圆角-6px：适合用在底层信息卡片</p>
      <div
        class="w-full h-36 rounded border"
        :style="{
          borderRadius: '6px',
          height: '100px',
          borderColor: '#DCDFE6',
        }"
      />
    </div>
  </div>
</template>
