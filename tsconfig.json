{"compilerOptions": {"target": "esnext", "jsx": "preserve", "lib": ["esnext", "dom"], "useDefineForClassFields": true, "baseUrl": "./", "module": "esnext", "moduleResolution": "node", "paths": {"@": ["src"], "@/*": ["src/*"]}, "resolveJsonModule": true, "typeRoots": ["node_modules/@types", "src/types"], "strict": true, "sourceMap": true, "esModuleInterop": true}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "./auto-imports.d.ts"]}