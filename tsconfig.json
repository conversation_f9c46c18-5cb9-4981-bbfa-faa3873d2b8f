{
  "compilerOptions": {
    "target": "esnext",
    "jsx": "preserve",
    "lib": [
      "esnext",
      "dom"
    ],
    "useDefineForClassFields": true,
    "baseUrl": "./",
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    /** 组件二封时必须手动引入Element组件，这里禁用Element全局类型提示 */
    // "types": ["element-plus/global"],
    "strict": true,
    "sourceMap": true,
    "esModuleInterop": true
  },
  "include": [
    "docs/**/*.ts",
    "docs/**/*.d.ts",
    "docs/**/*.tsx",
    "docs/**/*.vue",
    "packages/**/*.ts",
    "packages/**/*.d.ts",
    "packages/**/*.tsx",
    "packages/**/*.vue"
  ]
}
